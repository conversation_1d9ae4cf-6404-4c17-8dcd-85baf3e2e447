#!/bin/bash
# ============================================================================
# 所有部署脚本参数处理测试
# ============================================================================

echo "=== 所有部署脚本参数处理测试 ==="
echo

exit_code=0

# 测试项目列表
projects=("zentao-mcp-backend-service" "zentao-mcp-admin-web" "zentao-mcp-client")
project_names=("后端服务" "前端Web" "客户端")

# 测试1: help命令测试
echo "1. 测试所有项目的 help 命令:"
for i in "${!projects[@]}"; do
    project="${projects[$i]}"
    name="${project_names[$i]}"
    
    echo "  测试 $name ($project):"
    echo "    命令: cd $project && bash deploy.sh --help"
    echo "    预期: 显示帮助信息，无错误信息"
    
    cd "$project"
    if bash deploy.sh --help 2>&1 | grep -q "ERROR"; then
        echo "    ❌ FAILED: help命令显示错误信息"
        exit_code=1
    else
        echo "    ✅ PASSED: help命令正常工作"
    fi
    cd ..
    echo
done

# 测试2: 未知参数处理测试
echo "2. 测试所有项目的未知参数处理:"
for i in "${!projects[@]}"; do
    project="${projects[$i]}"
    name="${project_names[$i]}"
    
    echo "  测试 $name ($project):"
    echo "    命令: cd $project && bash deploy.sh unknown_param"
    echo "    预期: 显示错误信息和帮助信息，不执行部署"
    
    cd "$project"
    output=$(bash deploy.sh unknown_param 2>&1)
    
    # 检查是否包含错误信息
    if echo "$output" | grep -q "未识别的参数\|未知参数"; then
        # 检查是否显示了帮助信息
        if echo "$output" | grep -q "用法:"; then
            # 检查是否没有执行实际部署（不应该包含部署相关的成功信息）
            if ! echo "$output" | grep -q "部署完成\|操作完成\|SUCCESS.*完成"; then
                echo "    ✅ PASSED: 正确处理未知参数"
            else
                echo "    ❌ FAILED: 未知参数导致了实际部署"
                exit_code=1
            fi
        else
            echo "    ❌ FAILED: 未显示帮助信息"
            exit_code=1
        fi
    else
        echo "    ❌ FAILED: 未显示错误信息"
        exit_code=1
    fi
    cd ..
    echo
done

# 测试3: 多个未知参数测试
echo "3. 测试多个未知参数处理:"
for i in "${!projects[@]}"; do
    project="${projects[$i]}"
    name="${project_names[$i]}"
    
    echo "  测试 $name ($project):"
    echo "    命令: cd $project && bash deploy.sh param1 param2 param3"
    echo "    预期: 显示所有未知参数的错误信息"
    
    cd "$project"
    output=$(bash deploy.sh param1 param2 param3 2>&1)
    
    if echo "$output" | grep -q "param1.*param2.*param3\|param1 param2 param3"; then
        echo "    ✅ PASSED: 正确显示所有未知参数"
    else
        echo "    ❌ FAILED: 未正确显示所有未知参数"
        echo "    输出: $output"
        exit_code=1
    fi
    cd ..
    echo
done

# 测试4: 有效参数测试（不应该触发错误）
echo "4. 测试有效参数处理:"
valid_tests=(
    "zentao-mcp-backend-service:dev --help"
    "zentao-mcp-admin-web:test --help"
    "zentao-mcp-client:prod --help"
)

for test_case in "${valid_tests[@]}"; do
    project="${test_case%%:*}"
    params="${test_case##*:}"
    name=""
    
    case $project in
        "zentao-mcp-backend-service") name="后端服务" ;;
        "zentao-mcp-admin-web") name="前端Web" ;;
        "zentao-mcp-client") name="客户端" ;;
    esac
    
    echo "  测试 $name ($project):"
    echo "    命令: cd $project && bash deploy.sh $params"
    echo "    预期: 正常显示帮助信息"
    
    cd "$project"
    if bash deploy.sh $params >/dev/null 2>&1; then
        echo "    ✅ PASSED: 有效参数正常处理"
    else
        echo "    ❌ FAILED: 有效参数处理失败"
        exit_code=1
    fi
    cd ..
    echo
done

echo "=== 测试完成 ==="
if [[ $exit_code -eq 0 ]]; then
    echo "🎉 所有测试通过！所有部署脚本的参数处理都正常！"
    echo
    echo "📋 测试总结:"
    echo "✅ 后端服务脚本 - 参数处理正常"
    echo "✅ 前端Web脚本 - 参数处理正常"
    echo "✅ 客户端脚本 - 参数处理正常"
    echo
    echo "🔧 修复内容:"
    echo "- 修复了前端Web脚本的未知参数处理逻辑"
    echo "- 修复了客户端脚本的未知参数处理逻辑"
    echo "- 后端脚本原本就有正确的处理逻辑"
    echo
    echo "现在所有脚本都会："
    echo "1. 正确显示help信息"
    echo "2. 拒绝未知参数并显示错误"
    echo "3. 提示用户查看帮助信息"
    echo "4. 不会意外执行默认操作"
else
    echo "❌ 部分测试失败，需要进一步检查"
fi

exit $exit_code
