#!/bin/bash
# ============================================================================
# 最终部署测试脚本 - 验证所有修复结果
# ============================================================================

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_header() {
    echo -e "${CYAN}$1${NC}"
}

# 测试计数器
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 运行测试
run_test() {
    local test_name="$1"
    local test_command="$2"

    ((TOTAL_TESTS++))
    log_info "测试: $test_name"

    if bash -c "$test_command" &>/dev/null; then
        log_success "✅ $test_name"
        ((PASSED_TESTS++))
        return 0
    else
        log_error "❌ $test_name"
        ((FAILED_TESTS++))
        return 1
    fi
}

# 测试镜像加速配置
test_mirror_acceleration() {
    log_header "========== 测试镜像加速配置 =========="
    
    local files_to_check=(
        "zentao-mcp-admin-web/config/Dockerfile.dev:NPM_REGISTRY"
        "zentao-mcp-admin-web/config/Dockerfile.prod:NPM_REGISTRY"
        "zentao-mcp-admin-web/config/Dockerfile.test:NPM_REGISTRY"
        "zentao-mcp-admin-web/Dockerfile:NPM_REGISTRY"
        "zentao-mcp-backend-service/config/docker/Dockerfile.dev:PIP_INDEX_URL"
        "zentao-mcp-backend-service/config/docker/Dockerfile.prod:PIP_INDEX_URL"
        "zentao-mcp-backend-service/config/docker/Dockerfile.test:PIP_INDEX_URL"
        "zentao-mcp-client/config/Dockerfile.dev:PIP_INDEX_URL"
        "zentao-mcp-client/config/Dockerfile.prod:PIP_INDEX_URL"
        "zentao-mcp-client/config/Dockerfile.test:PIP_INDEX_URL"
    )
    
    for file_config in "${files_to_check[@]}"; do
        local file_path="${file_config%%:*}"
        local required_var="${file_config##*:}"
        
        run_test "镜像加速配置: $file_path" "[[ -f '$file_path' ]] && grep -q 'ARG $required_var=' '$file_path'"
    done
}

# 测试部署脚本
test_deployment_scripts() {
    log_header "========== 测试部署脚本 =========="
    
    local projects=("zentao-mcp-backend-service" "zentao-mcp-admin-web" "zentao-mcp-client")
    
    for project in "${projects[@]}"; do
        run_test "部署脚本存在: $project" "test -f $project/deploy.sh"
        run_test "部署脚本可执行: $project" "test -x $project/deploy.sh"
        run_test "支持help参数: $project" "cd $project && ./deploy.sh --help >/dev/null 2>&1"
    done
    
    # 测试统一部署脚本
    run_test "统一部署脚本存在" "[[ -f 'deploy-zentao.sh' ]]"
    run_test "统一部署脚本可执行" "[[ -x 'deploy-zentao.sh' ]]"
}

# 测试环境配置文件
test_environment_configs() {
    log_header "========== 测试环境配置文件 =========="
    
    # 后端环境配置
    run_test "后端dev环境配置" "[[ -f 'zentao-mcp-backend-service/config/environments/dev.env' ]]"
    run_test "后端test环境配置" "[[ -f 'zentao-mcp-backend-service/config/environments/test.env' ]]"
    run_test "后端prod环境配置" "[[ -f 'zentao-mcp-backend-service/config/environments/prod.env' ]]"
    
    # 前端环境配置
    run_test "前端dev环境配置" "[[ -f 'zentao-mcp-admin-web/config/.env.dev' ]]"
    run_test "前端test环境配置" "[[ -f 'zentao-mcp-admin-web/config/.env.test' ]]"
    run_test "前端prod环境配置" "[[ -f 'zentao-mcp-admin-web/config/.env.prod' ]]"
    
    # Docker Compose配置
    run_test "后端test compose配置" "[[ -f 'zentao-mcp-backend-service/config/compose/docker-compose.test.yml' ]]"
    run_test "后端prod compose配置" "[[ -f 'zentao-mcp-backend-service/config/compose/docker-compose.prod.yml' ]]"
    run_test "客户端test compose配置" "[[ -f 'zentao-mcp-client/config/docker-compose.test.yml' ]]"
    run_test "客户端prod compose配置" "[[ -f 'zentao-mcp-client/config/docker-compose.prod.yml' ]]"
}

# 测试Docker文件
test_docker_files() {
    log_header "========== 测试Docker文件 =========="
    
    # 后端Dockerfile
    run_test "后端dev Dockerfile" "[[ -f 'zentao-mcp-backend-service/config/docker/Dockerfile.dev' ]]"
    run_test "后端test Dockerfile" "[[ -f 'zentao-mcp-backend-service/config/docker/Dockerfile.test' ]]"
    run_test "后端prod Dockerfile" "[[ -f 'zentao-mcp-backend-service/config/docker/Dockerfile.prod' ]]"
    
    # 前端Dockerfile
    run_test "前端dev Dockerfile" "[[ -f 'zentao-mcp-admin-web/config/Dockerfile.dev' ]]"
    run_test "前端test Dockerfile" "[[ -f 'zentao-mcp-admin-web/config/Dockerfile.test' ]]"
    run_test "前端prod Dockerfile" "[[ -f 'zentao-mcp-admin-web/config/Dockerfile.prod' ]]"
    
    # 客户端Dockerfile
    run_test "客户端dev Dockerfile" "[[ -f 'zentao-mcp-client/config/Dockerfile.dev' ]]"
    run_test "客户端test Dockerfile" "[[ -f 'zentao-mcp-client/config/Dockerfile.test' ]]"
    run_test "客户端prod Dockerfile" "[[ -f 'zentao-mcp-client/config/Dockerfile.prod' ]]"
}

# 测试Web端构建修复
test_web_build_fix() {
    log_header "========== 测试Web端构建修复 =========="
    
    cd zentao-mcp-admin-web
    
    run_test "package.json构建脚本修复" "grep -q 'vite build' package.json"
    run_test "vite.config.ts配置修复" "[[ -f 'vite.config.ts' ]] && ! grep -q 'esbuild: false' vite.config.ts"
    run_test "部署脚本超时机制" "grep -q 'timeout.*build' deploy.sh"
    
    cd ..
}

# 测试配置文件完整性
test_config_completeness() {
    log_header "========== 测试配置文件完整性 =========="
    
    # 全局配置
    run_test "镜像源配置文件" "[[ -f 'config/mirrors.env' ]]"
    run_test "根目录docker-compose" "[[ -f 'docker-compose.yml' ]]"
    
    # 测试脚本
    run_test "镜像和部署检查脚本" "[[ -f 'test_mirror_and_deployment_check.sh' ]]"
    run_test "Web构建测试脚本" "[[ -f 'test_web_build.sh' ]]"
    run_test "部署总结文档" "[[ -f 'DEPLOYMENT_SUMMARY.md' ]]"
}

# 显示测试结果
show_test_results() {
    echo ""
    log_header "============================================================================"
    log_header "                              测试结果汇总"
    log_header "============================================================================"
    
    log_info "总测试数: $TOTAL_TESTS"
    log_success "通过测试: $PASSED_TESTS"
    log_error "失败测试: $FAILED_TESTS"
    
    local success_rate=$((PASSED_TESTS * 100 / TOTAL_TESTS))
    log_info "成功率: ${success_rate}%"
    
    echo ""
    if [[ $FAILED_TESTS -eq 0 ]]; then
        log_success "🎉 所有测试通过！项目已准备好部署！"
        echo ""
        log_info "下一步操作："
        log_info "1. 运行 './deploy-zentao.sh all test deploy' 部署测试环境"
        log_info "2. 运行 './deploy-zentao.sh all prod deploy' 部署生产环境"
        log_info "3. 查看 'DEPLOYMENT_SUMMARY.md' 了解详细信息"
    else
        log_error "❌ 有 $FAILED_TESTS 个测试失败，请检查并修复问题"
    fi
    
    log_header "============================================================================"
}

# 主函数
main() {
    echo ""
    log_header "============================================================================"
    log_header "                        禅道MCP服务器最终部署测试"
    log_header "============================================================================"
    echo ""
    
    # 运行所有测试
    test_mirror_acceleration
    echo ""
    test_deployment_scripts
    echo ""
    test_environment_configs
    echo ""
    test_docker_files
    echo ""
    test_web_build_fix
    echo ""
    test_config_completeness
    
    # 显示结果
    show_test_results
    
    # 返回适当的退出码
    if [[ $FAILED_TESTS -eq 0 ]]; then
        exit 0
    else
        exit 1
    fi
}

# 执行主函数
main "$@"
