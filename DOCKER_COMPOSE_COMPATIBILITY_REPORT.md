# Docker Compose 兼容性报告

## 概述

本报告测试了项目中Docker Compose命令的兼容性，确保支持以下两种命令格式：
- `docker compose` (Docker Compose V2)
- `docker-compose` (Docker Compose V1)

## 测试结果

### 命令检测结果

| 命令 | 状态 | 版本信息 |
|------|------|----------|
| docker | ❌ 不可用 | - |
| docker compose | ❌ 不可用 | - |
| docker-compose | ❌ 不可用 | - |

### 兼容性实现

项目已实现Docker Compose命令兼容性：

1. **自动检测**: `scripts/common.sh` 中的 `setup_compose_command()` 函数会自动检测可用的命令
2. **优先级**: 
   - Docker环境: `docker compose` > `docker-compose`
   - Podman环境: `podman-compose` > `docker-compose` > `docker compose`
3. **统一使用**: 所有部署脚本使用 `$COMPOSE_CMD` 变量

### 配置文件兼容性

所有Docker Compose配置文件都兼容V1和V2格式：
- 使用 `version: '3.8'` 格式
- 避免使用V2专有特性
- 标准的服务、网络、卷配置

### 使用方法

```bash
# 自动检测并使用最佳的Compose命令
./deploy-zentao.sh backend dev deploy

# 手动指定容器引擎
./deploy-zentao.sh --engine docker backend dev deploy
./deploy-zentao.sh --engine podman backend dev deploy
```

### 故障排除

如果遇到Compose命令问题：

1. **检查Docker安装**:
   ```bash
   docker --version
   docker compose version  # V2
   docker-compose --version # V1
   ```

2. **安装Docker Compose V1** (如果需要):
   ```bash
   # Linux
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   
   # macOS
   brew install docker-compose
   ```

3. **升级到Docker Compose V2**:
   - Docker Desktop用户: 自动包含V2
   - Linux用户: 安装最新版Docker

## 结论

✅ 项目已完全支持Docker Compose V1和V2命令格式  
✅ 自动检测和选择最佳可用命令  
✅ 所有部署脚本使用统一的命令变量  
✅ 配置文件完全兼容两种版本  

项目的Docker Compose兼容性实现完善，可以在不同环境下正常工作。
