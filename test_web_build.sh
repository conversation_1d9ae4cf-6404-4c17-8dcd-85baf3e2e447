#!/bin/bash
# ============================================================================
# Web端构建测试脚本
# ============================================================================

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 进入web目录
cd zentao-mcp-admin-web

log_info "========== Web端构建诊断 =========="

# 1. 检查Node.js版本
log_info "1. 检查Node.js版本:"
node --version
npm --version

# 2. 检查项目文件
log_info "2. 检查关键文件:"
files_to_check=("package.json" "vite.config.ts" "tsconfig.json" "index.html" "src/main.ts")
for file in "${files_to_check[@]}"; do
    if [[ -f "$file" ]]; then
        log_success "  ✅ $file 存在"
    else
        log_error "  ❌ $file 不存在"
    fi
done

# 3. 检查依赖
log_info "3. 检查依赖安装:"
if [[ -d "node_modules" ]]; then
    log_success "  ✅ node_modules 存在"
    
    # 检查关键依赖
    key_deps=("vue" "vite" "@vitejs/plugin-vue" "vue-tsc" "typescript")
    for dep in "${key_deps[@]}"; do
        if [[ -d "node_modules/$dep" ]]; then
            log_success "    ✅ $dep 已安装"
        else
            log_error "    ❌ $dep 未安装"
        fi
    done
else
    log_error "  ❌ node_modules 不存在"
    log_info "  正在安装依赖..."
    npm install
fi

# 4. 尝试TypeScript检查
log_info "4. TypeScript类型检查:"
if timeout 30s npm run type-check 2>/dev/null; then
    log_success "  ✅ TypeScript检查通过"
else
    log_warning "  ⚠️ TypeScript检查超时或失败"
fi

# 5. 尝试简单构建（跳过TypeScript检查）
log_info "5. 尝试Vite构建（跳过TypeScript）:"
if timeout 60s npx vite build --logLevel info 2>&1; then
    log_success "  ✅ Vite构建成功"
    
    # 检查构建产物
    if [[ -d "dist" ]]; then
        log_success "  ✅ dist目录已生成"
        log_info "  构建产物:"
        ls -la dist/ | head -10
    else
        log_error "  ❌ dist目录未生成"
    fi
else
    log_error "  ❌ Vite构建失败"
fi

# 6. 清理并重新尝试
log_info "6. 清理缓存并重试:"
rm -rf dist/ node_modules/.vite/
log_info "  已清理缓存"

if timeout 60s npx vite build --force 2>&1; then
    log_success "  ✅ 强制重新构建成功"
else
    log_error "  ❌ 强制重新构建失败"
fi

log_info "========== 诊断完成 =========="
