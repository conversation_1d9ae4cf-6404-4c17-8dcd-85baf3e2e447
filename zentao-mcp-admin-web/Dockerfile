# 多阶段构建 - 构建阶段
# 使用ARG支持包管理器镜像源配置
ARG NPM_REGISTRY=https://registry.npmmirror.com
ARG APK_MIRROR=mirrors.aliyun.com

FROM node:18-alpine AS builder

# 重新声明ARG变量（FROM之后需要重新声明）
ARG NPM_REGISTRY
ARG APK_MIRROR

# 设置工作目录
WORKDIR /app

# 配置Alpine镜像源
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories

# 安装系统依赖
RUN apk add --no-cache curl tzdata

# 配置npm镜像源
RUN npm config set registry ${NPM_REGISTRY}

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段 - 使用nginx提供静态文件
FROM nginx:alpine

# 重新声明ARG变量（FROM之后需要重新声明）
ARG APK_MIRROR

# 配置Alpine镜像源并安装curl
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories && \
    apk add --no-cache curl

# 复制构建产物到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
