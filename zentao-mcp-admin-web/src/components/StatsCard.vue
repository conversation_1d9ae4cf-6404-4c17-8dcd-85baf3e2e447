<template>
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M12 2L2 7V10C2 16 6 20.5 12 22C18 20.5 22 16 22 10V7L12 2Z" stroke="currentColor" stroke-width="2" fill="none"/>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ stats?.total_keys || 0 }}</div>
        <div class="stat-label">总API Keys</div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon active">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" fill="none"/>
          <path d="M8 12L11 15L16 9" stroke="currentColor" stroke-width="2" fill="none"/>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ stats?.active_keys || 0 }}</div>
        <div class="stat-label">活跃Keys</div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon requests">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" stroke="currentColor" stroke-width="2" fill="none"/>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ formatNumber(stats?.total_requests || 0) }}</div>
        <div class="stat-label">总请求数</div>
      </div>
    </div>
    
    <div class="stat-card">
      <div class="stat-icon today">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke="currentColor" stroke-width="2" fill="none"/>
          <line x1="16" y1="2" x2="16" y2="6" stroke="currentColor" stroke-width="2"/>
          <line x1="8" y1="2" x2="8" y2="6" stroke="currentColor" stroke-width="2"/>
          <line x1="3" y1="10" x2="21" y2="10" stroke="currentColor" stroke-width="2"/>
        </svg>
      </div>
      <div class="stat-content">
        <div class="stat-value">{{ formatNumber(stats?.requests_today || 0) }}</div>
        <div class="stat-label">今日请求</div>
      </div>
    </div>
  </div>
  
  <div v-if="stats?.most_used_key" class="most-used-card">
    <h3>使用最多的API Key</h3>
    <div class="most-used-content">
      <div class="key-name">{{ stats.most_used_key.name }}</div>
      <div class="usage-count">{{ formatNumber(stats.most_used_key.usage_count) }} 次使用</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { UsageStats } from '@/types/api'

interface Props {
  stats: UsageStats | null
}

defineProps<Props>()

function formatNumber(num: number): string {
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K'
  }
  return num.toString()
}
</script>

<style scoped>
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: transform 0.2s, box-shadow 0.2s;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  color: #6c757d;
}

.stat-icon.active {
  background: #d4edda;
  color: #28a745;
}

.stat-icon.requests {
  background: #d1ecf1;
  color: #17a2b8;
}

.stat-icon.today {
  background: #fff3cd;
  color: #ffc107;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.most-used-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.most-used-card h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
}

.most-used-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.key-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.usage-count {
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 12px;
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .stat-card {
    padding: 16px;
    gap: 12px;
  }
  
  .stat-icon {
    width: 40px;
    height: 40px;
  }
  
  .stat-value {
    font-size: 20px;
  }
  
  .most-used-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}
</style>