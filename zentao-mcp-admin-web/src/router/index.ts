import { createRouter, createWebHistory } from 'vue-router'
import { authService } from '@/services/auth'
const Dashboard = () => import('@/views/Dashboard.vue')
const Login = () => import('@/views/Login.vue')

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      redirect: '/dashboard'
    },
    {
      path: '/login',
      name: 'Login',
      component: Login,
      meta: { requiresAuth: false }
    },
    {
      path: '/dashboard',
      name: 'Dashboard',
      component: Dashboard,
      meta: { requiresAuth: true }
    },
    {
      path: '/users',
      name: 'UserManagement',
      component: () => import('@/views/UserManagement.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: 'admin',
        title: '用户管理'
      }
    },
    {
      path: '/api-keys',
      name: 'ApiKeyManagement',
      component: () => import('@/views/ApiKeyManagement.vue'),
      meta: { 
        requiresAuth: true,
        title: 'API Key管理'
      }
    },
    {
      path: '/audit-logs',
      name: 'AuditLogs',
      component: () => import('@/views/AuditLogs.vue'),
      meta: { 
        requiresAuth: true,
        requiresRole: 'admin',
        title: '审计日志'
      }
    },
    {
      path: '/profile',
      name: 'UserProfile',
      component: () => import('@/views/UserProfile.vue'),
      meta: { 
        requiresAuth: true,
        title: '个人资料'
      }
    }
  ]
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const isLoggedIn = authService.isLoggedIn()

  if (to.meta.requiresAuth && !isLoggedIn) {
    // 需要认证但未登录，跳转到登录页
    next('/login')
    return
  }

  if (to.path === '/login' && isLoggedIn) {
    // 已登录但访问登录页，跳转到仪表板
    next('/dashboard')
    return
  }

  // 基于角色的访问控制：当路由声明了 requiresRole（如 'admin'）时进行校验
  const requiredRole = (to.meta as any).requiresRole as string | undefined
  if (requiredRole && !authService.hasPermission(requiredRole.toUpperCase())) {
    // 无权限则回到仪表板
    next('/dashboard')
    return
  }

  next()
})

export default router