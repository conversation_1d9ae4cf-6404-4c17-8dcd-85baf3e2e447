/**
 * 用户相关类型定义
 */

export interface User {
  id: number
  username: string
  email: string
  phone?: string
  department?: string
  bio?: string
  role: string
  active: boolean
  created_at: string
  last_login: string
}

export interface CreateUserRequest {
  username: string
  email: string
  password: string
  role: string
  department?: string
  phone?: string
}

export interface UpdateUserRequest {
  email?: string
  role?: string
  department?: string
  phone?: string
  bio?: string
  is_active?: boolean
}

export interface UpdateProfileRequest {
  username?: string
  email?: string
  phone?: string
  department?: string
  bio?: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface ResetPasswordRequest {
  user_id: string
  new_password: string
}