/**
 * API相关类型定义
 */

export interface ApiKey {
  id: string
  name: string
  created_at: string
  last_used_at: string | null
  is_active: boolean
  active?: boolean
  usage_count: number
  key?: string
  description?: string
  // 后端响应字段：仅创建时完整返回，列表为掩码预览
  key_value?: string
}

export interface CreateApiKeyRequest {
  name: string
}

export interface CreateApiKeyResponse {
  id: string
  name: string
  api_key: string
}

export interface ApiResponse<T> {
  status: 'success' | 'error'
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  pages: number
}

export interface UsageStats {
  total_keys: number
  active_keys: number
  total_requests: number
  requests_today: number
  most_used_key: {
    name: string
    usage_count: number
  } | null
}