/**
 * API Keys状态管理
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { <PERSON><PERSON><PERSON>ey, CreateApiKeyRequest, UsageStats } from '@/types/api'
import { apiService } from '@/services/api'

export const useApiKeysStore = defineStore('apiKeys', () => {
  // 状态
  const apiKeys = ref<ApiKey[]>([])
  const loading = ref(false)
  const error = ref<string | null>(null)
  const stats = ref<UsageStats | null>(null)
  
  // 分页状态
  const currentPage = ref(1)
  const pageSize = ref(10)
  const totalItems = ref(0)
  const totalPages = ref(0)

  // 计算属性
  const activeKeys = computed(() => 
    apiKeys.value.filter(key => key.is_active === true)
  )

  const inactiveKeys = computed(() => 
    apiKeys.value.filter(key => key.is_active === false)
  )

  // 操作方法
  async function fetchApiKeys(page: number = 1) {
    loading.value = true
    error.value = null
    
    try {
      const response = await apiService.apiKeys.getList(page, pageSize.value)
      const payload: any = response.data
      // 兼容后端返回：{ api_keys, total, skip, limit } 或 { status, data }
      const data = payload?.status === 'success' && payload?.data ? payload.data : payload
      const items = data?.api_keys ?? data?.items ?? []
      const total = data?.total ?? data?.totalItems ?? (Array.isArray(items) ? items.length : 0)

      apiKeys.value = (items as unknown as ApiKey[])
      totalItems.value = total
      totalPages.value = Math.ceil((total || 0) / (pageSize.value || 10))
    } catch (err) {
      error.value = err instanceof Error ? err.message : '未知错误'
      console.error('获取API Keys失败:', err)
    } finally {
      loading.value = false
    }
  }

  async function createApiKey(data: Pick<CreateApiKeyRequest, 'name'>) {
    loading.value = true
    error.value = null
    
    try {
      const response = await apiService.apiKeys.create(data)
      
      if (response.data.status === 'success' && response.data.data) {
        // 重新获取列表
        await fetchApiKeys(currentPage.value)
        return response.data.data
      } else {
        throw new Error(response.data.error ?? '创建API Key失败')
      }
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.response?.data?.message || (err instanceof Error ? err.message : '未知错误');
      error.value = errorMessage;
      console.error('创建API Key失败:', err);
      // Don't re-throw, let the component decide what to do based on the error state.
    } finally {
      loading.value = false
    }
  }

  async function deleteApiKey(keyId: number | string) {
    loading.value = true
    error.value = null
    
    try {
      const idNum = typeof keyId === 'string' ? parseInt(keyId, 10) : keyId
      const response = await apiService.apiKeys.delete(idNum)
      
      // If axios doesn't throw, the request is successful.
      await fetchApiKeys(currentPage.value)
      return response.data.message // Return the message from API
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || err.response?.data?.message || (err instanceof Error ? err.message : '未知错误');
      error.value = errorMessage;
      console.error('删除API Key失败:', err);
      throw new Error(errorMessage);
    } finally {
      loading.value = false
    }
  }

  async function toggleApiKey(keyId: number | string) {
    loading.value = true
    error.value = null
    
    try {
      const idNum = typeof keyId === 'string' ? parseInt(keyId, 10) : keyId
      // 从本地状态获取当前状态
      const current = apiKeys.value.find(key => Number(key.id) === Number(keyId))
      const isActive = current?.is_active === true
      const response = isActive
        ? await apiService.apiKeys.revoke(idNum)
        : await apiService.apiKeys.activate(idNum)
      // 后端直接返回更新后的API Key对象
      const updated = response.data
      // 更新本地状态
      const index = apiKeys.value.findIndex(key => Number(key.id) === Number(keyId))
      if (index !== -1) {
        apiKeys.value[index] = { ...apiKeys.value[index], ...updated }
      }
    } catch (err) {
      error.value = err instanceof Error ? err.message : '未知错误'
      console.error('切换API Key状态失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function fetchStats() {
    try {
      const response = await apiService.apiKeys.getStats()
      
      if (response.data.status === 'success' && response.data.data) {
        stats.value = response.data.data
      } else {
        console.error('获取统计信息失败:', response.data.error)
      }
    } catch (err) {
      console.error('获取统计信息失败:', err)
    }
  }

  // 重置状态
  function resetError() {
    error.value = null
  }

  return {
    // 状态
    apiKeys,
    loading,
    error,
    stats,
    currentPage,
    pageSize,
    totalItems,
    totalPages,
    
    // 计算属性
    activeKeys,
    inactiveKeys,
    
    // 方法
    fetchApiKeys,
    createApiKey,
    deleteApiKey,
    toggleApiKey,
    fetchStats,
    resetError,
  }
})