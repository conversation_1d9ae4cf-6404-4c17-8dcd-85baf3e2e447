<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          禅道MCP管理系统
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          请登录您的管理员账户
        </p>
      </div>
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <label for="username" class="sr-only">用户名</label>
            <input
              id="username"
              v-model="loginForm.username"
              name="username"
              type="text"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="用户名"
            />
          </div>
          <div>
            <label for="password" class="sr-only">密码</label>
            <input
              id="password"
              v-model="loginForm.password"
              name="password"
              type="password"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="密码"
            />
          </div>
        </div>

        <div v-if="error" class="text-red-600 text-sm text-center">
          {{ error }}
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <span v-if="loading">登录中...</span>
            <span v-else>登录</span>
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { authService } from '@/services/auth'

const router = useRouter()

const loginForm = ref({
  username: '',
  password: ''
})

const loading = ref(false)
const error = ref('')

const handleLogin = async () => {
  loading.value = true
  error.value = ''
  
  try {
    console.log('开始登录流程:', loginForm.value.username)
    
    const result = await authService.login(loginForm.value)
    console.log('登录服务返回结果:', result)
    
    // 验证登录是否真正成功
    if (authService.isLoggedIn()) {
      console.log('登录状态验证成功，跳转到dashboard')
      router.push('/dashboard')
    } else {
      console.error('登录状态验证失败')
      error.value = '登录状态验证失败，请重试'
    }
  } catch (err: any) {
    console.error('登录失败:', err)
    
    // 更详细的错误处理
    if (err.response) {
      // 服务器返回了错误响应
      const errorMsg = err.response.data?.detail || err.response.data?.message || '服务器错误'
      error.value = `登录失败: ${errorMsg}`
    } else if (err.request) {
      // 请求发送了但没有收到响应
      error.value = '网络错误，请检查网络连接'
    } else {
      // 其他错误
      error.value = err.message || '登录失败，请重试'
    }
  } finally {
    loading.value = false
  }
}
</script>