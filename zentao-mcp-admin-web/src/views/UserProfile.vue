<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">用户资料</h1>
      <p class="text-gray-600">查看和管理您的个人信息</p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <!-- 左侧个人信息卡片 -->
      <div class="md:col-span-1">
        <div class="bg-white shadow rounded-lg p-6">
          <div class="flex flex-col items-center text-center mb-6">
            <div class="w-24 h-24 bg-gray-300 rounded-full flex items-center justify-center mb-4 overflow-hidden">
              <div class="w-full h-full flex items-center justify-center bg-blue-100 text-blue-800">
                <span class="text-4xl">{{ user?.username ? user.username.charAt(0).toUpperCase() : 'U' }}</span>
              </div>
            </div>
            <h2 class="text-xl font-semibold">{{ user?.username || '用户名未设置' }}</h2>
            <p class="text-gray-600">{{ user?.email || '邮箱未设置' }}</p>
            <p class="text-sm text-gray-500 mt-1">{{ user?.role || '角色未设置' }}</p>
          </div>
          
          <div class="border-t pt-4">
            <div class="flex justify-between items-center mb-2">
              <span class="text-gray-600">用户ID</span>
              <span class="text-gray-900 font-medium">{{ user?.id || '未知' }}</span>
            </div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-gray-600">创建时间</span>
              <span class="text-gray-900 font-medium">{{ formatDate(user?.created_at) }}</span>
            </div>
            <div class="flex justify-between items-center mb-2">
              <span class="text-gray-600">上次登录</span>
              <span class="text-gray-900 font-medium">{{ formatDate(user?.last_login) }}</span>
            </div>
            <div class="flex justify-between items-center">
              <span class="text-gray-500">状态</span>
              <span :class="user?.active ? 'text-green-600' : 'text-red-600'" class="font-medium">
                {{ user?.active ? '活跃' : '禁用' }}
              </span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧信息编辑区域 -->
      <div class="md:col-span-2">
        <div class="bg-white shadow rounded-lg p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">个人信息</h3>
          
          <form @submit.prevent="updateProfile">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-1">用户名</label>
                <input
                  id="username"
                  v-model="profileForm.username"
                  type="text"
                  class="w-full border border-gray-300 rounded-lg px-3 py-2"
                  :disabled="!isEditing"
                />
              </div>
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">邮箱</label>
                <input
                  id="email"
                  v-model="profileForm.email"
                  type="email"
                  class="w-full border border-gray-300 rounded-lg px-3 py-2"
                  :disabled="!isEditing"
                />
              </div>
              <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">电话</label>
                <input
                  id="phone"
                  v-model="profileForm.phone"
                  type="text"
                  class="w-full border border-gray-300 rounded-lg px-3 py-2"
                  :disabled="!isEditing"
                />
              </div>
              <div>
                <label for="department" class="block text-sm font-medium text-gray-700 mb-1">部门</label>
                <input
                  id="department"
                  v-model="profileForm.department"
                  type="text"
                  class="w-full border border-gray-300 rounded-lg px-3 py-2"
                  :disabled="!isEditing"
                />
              </div>
            </div>

            <div class="mb-6">
              <label for="bio" class="block text-sm font-medium text-gray-700 mb-1">个人简介</label>
              <textarea
                id="bio"
                v-model="profileForm.bio"
                rows="3"
                class="w-full border border-gray-300 rounded-lg px-3 py-2"
                :disabled="!isEditing"
              ></textarea>
            </div>

            <div class="flex justify-end space-x-3">
              <button
                v-if="!isEditing"
                type="button"
                @click="isEditing = true"
                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
              >
                编辑信息
              </button>
              <template v-else>
                <button
                  type="button"
                  @click="cancelEdit"
                  class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
                >
                  取消
                </button>
                <button
                  type="submit"
                  class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
                >
                  保存
                </button>
              </template>
            </div>
          </form>
        </div>

        <!-- 密码修改区域 -->
        <div class="bg-white shadow rounded-lg p-6 mt-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">修改密码</h3>
          
          <form @submit.prevent="changePassword">
            <div class="space-y-4 mb-6">
              <div>
                <label for="current-password" class="block text-sm font-medium text-gray-700 mb-1">当前密码</label>
                <input
                  id="current-password"
                  v-model="passwordForm.currentPassword"
                  type="password"
                  class="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label for="new-password" class="block text-sm font-medium text-gray-700 mb-1">新密码</label>
                <input
                  id="new-password"
                  v-model="passwordForm.newPassword"
                  type="password"
                  class="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
              <div>
                <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-1">确认新密码</label>
                <input
                  id="confirm-password"
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  class="w-full border border-gray-300 rounded-lg px-3 py-2"
                />
              </div>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
              >
                更新密码
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { userStore } from '@/stores/user'
import { useToast } from '@/composables/useToast'
import type { User } from '@/types/user'

const store = userStore()
const { showToast } = useToast()

// 响应式数据
const isEditing = ref(false)
const profileForm = ref({
  username: '',
  email: '',
  phone: '',
  department: '',
  bio: ''
})

const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 计算属性
const user = computed(() => store.currentUser)

// 方法
const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return '未知'
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  })
}

const loadUserProfile = () => {
  if (user.value) {
    profileForm.value = {
      username: user.value.username || '',
      email: user.value.email || '',
      phone: user.value.phone || '',
      department: user.value.department || '',
      bio: user.value.bio || ''
    }
  }
}

const updateProfile = async () => {
  try {
    await store.updateProfile(profileForm.value)
    isEditing.value = false
    showToast('个人信息更新成功', 'success')
  } catch (error: any) {
    showToast('更新失败: ' + error.message, 'error')
  }
}

const cancelEdit = () => {
  isEditing.value = false
  loadUserProfile()
}

const changePassword = async () => {
  // 验证密码
  if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
    showToast('新密码与确认密码不匹配', 'error')
    return
  }
  
  if (passwordForm.value.newPassword.length < 8) {
    showToast('新密码长度必须至少为8个字符', 'error')
    return
  }
  
  try {
    await store.changePassword({
      currentPassword: passwordForm.value.currentPassword,
      newPassword: passwordForm.value.newPassword
    })
    
    // 清空表单
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
    
    showToast('密码修改成功', 'success')
  } catch (error: any) {
    showToast('密码修改失败: ' + error.message, 'error')
  }
}

// 生命周期钩子
onMounted(async () => {
  if (!user.value?.id) {
    await store.fetchCurrentUser()
  }
  loadUserProfile()
})
</script>