<template>
  <div class="p-6">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">用户管理</h1>
      <p class="text-gray-600">管理系统用户和权限</p>
    </div>

    <!-- 操作栏 -->
    <div class="mb-4 flex justify-between items-center">
      <div class="flex space-x-2">
        <button 
          @click="showCreateModal = true"
          class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
        >
          新建用户
        </button>
        <button 
          @click="refreshUsers"
          class="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg"
        >
          刷新
        </button>
      </div>
      
      <div class="flex items-center space-x-2">
        <input
          v-model="searchQuery"
          type="text"
          placeholder="搜索用户..."
          class="border border-gray-300 rounded-lg px-3 py-2"
        />
      </div>
    </div>

    <!-- 用户列表 -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              用户信息
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              权限类型
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              状态
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              创建时间
            </th>
            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              操作
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="user in filteredUsers" :key="user.id">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="flex items-center">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ user.username }}</div>
                  <div class="text-sm text-gray-500">{{ user.email }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="getUserTypeClass(user.role)" class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                {{ getUserTypeText(user.role) }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span :class="user.active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'" 
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full">
                {{ user.active ? '活跃' : '禁用' }}
              </span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
              {{ formatDate(user.created_at) }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
              <div class="flex space-x-2">
                <button @click="editUser(user)" class="text-indigo-600 hover:text-indigo-900">编辑</button>
                <button @click="resetPassword(user)" class="text-blue-600 hover:text-blue-900">重置密码</button>
                <button 
                  @click="toggleUserStatus(user)" 
                  :class="user.active ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'"
                >
                  {{ user.active ? '禁用' : '启用' }}
                </button>
                <button
                  @click="deleteUser(user)" 
                  class="text-red-600 hover:text-red-900"
                >
                  删除
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 创建用户模态框 -->
    <div v-if="showCreateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
      <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3">
          <h3 class="text-lg font-medium text-gray-900 mb-4">创建新用户</h3>
          <form @submit.prevent="createUser">
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700">用户名</label>
              <input
                v-model="newUser.username"
                type="text"
                required
                class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700">邮箱</label>
              <input
                v-model="newUser.email"
                type="email"
                required
                class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700">密码</label>
              <input
                v-model="newUser.password"
                type="password"
                required
                minlength="8"
                class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              />
              <p v-if="newUser.password.length > 0 && newUser.password.length < 8" class="text-xs text-red-500 mt-1">密码长度至少为8个字符。</p>
            </div>
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700">用户类型</label>
              <select
                v-model="newUser.role"
                class="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2"
              >
                <option value="USER">普通用户</option>
                <option value="ADMIN">管理员</option>
              </select>
            </div>
            <div class="flex justify-end space-x-2">
              <button
                type="button"
                @click="showCreateModal = false"
                class="bg-gray-300 hover:bg-gray-400 text-gray-800 px-4 py-2 rounded-lg"
              >
                取消
              </button>
              <button
                type="submit"
                class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg"
              >
                创建
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { userStore } from '@/stores/user'
import type { User } from '@/types/user'

const store = userStore()

// 响应式数据
const showCreateModal = ref(false)
const searchQuery = ref('')
const newUser = ref({
  username: '',
  email: '',
  password: '',
  role: 'USER'
})

// 计算属性
const filteredUsers = computed(() => {
  if (!searchQuery.value) return store.users
  return store.users.filter(user => 
    user.username.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    user.email.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 方法
const refreshUsers = async () => {
  await store.fetchUsers()
}

const createUser = async () => {
  if (newUser.value.password.length < 8) {
    alert('密码长度至少为8个字符。');
    return;
  }
  try {
    await store.createUser(newUser.value)
    showCreateModal.value = false
    newUser.value = { username: '', email: '', password: '', role: 'USER' }
    await refreshUsers()
  } catch (error: any) {
    console.error('创建用户失败:', error);
    if (error.response && error.response.data) {
      const responseData = error.response.data;
      // 检查新的错误格式 { "message": "..." }
      if (responseData.message) {
        alert(`创建失败: ${responseData.message}`);
      } 
      // 检查 FastAPI 的验证错误格式 { "detail": [...] }
      else if (responseData.detail) {
        const errorDetail = responseData.detail;
        if (typeof errorDetail === 'string') {
          alert(`创建失败: ${errorDetail}`);
        } else if (Array.isArray(errorDetail)) {
          const messages = errorDetail.map(err => {
            if (err.loc && err.msg) {
              return `${err.loc.join('.')} - ${err.msg}`;
            }
            return JSON.stringify(err);
          }).join('\n');
          alert(`创建失败:\n${messages}`);
        } else {
          alert('创建用户失败，请检查输入或联系管理员。');
        }
      } else {
        alert('创建用户失败，发生未知错误。');
      }
    } else {
      alert('创建用户失败，发生未知网络错误。');
    }
  }
}

const editUser = (user: User) => {
  // TODO: 实现编辑用户功能
  console.log('编辑用户:', user)
}

const resetPassword = async (user: User) => {
  if (confirm(`确定要重置用户 ${user.username} 的密码吗？`)) {
    try {
      await store.resetUserPassword(user.id)
      alert('密码重置成功')
    } catch (error) {
      console.error('重置密码失败:', error)
    }
  }
}

const toggleUserStatus = async (user: User) => {
  const action = user.active ? '禁用' : '启用'
  if (confirm(`确定要${action}用户 ${user.username} 吗？`)) {
    try {
      await store.toggleUserStatus(user.id)
      await refreshUsers()
    } catch (error) {
      console.error(`${action}用户失败:`, error)
    }
  }
}

const deleteUser = async (user: User) => {
  if (confirm(`确定要删除用户 ${user.username} 吗？此操作不可恢复！`)) {
    try {
      await store.deleteUser(user.id)
      await refreshUsers()
    } catch (error) {
      console.error('删除用户失败:', error)
    }
  }
}

const getUserTypeClass = (type: string) => {
  switch (type) {
    case 'ADMIN': return 'bg-blue-100 text-blue-800'
    case 'USER': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getUserTypeText = (type: string) => {
  switch (type) {
    case 'ADMIN': return '管理员'
    case 'USER': return '普通用户'
    default: return '未知'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  refreshUsers()
})
</script>