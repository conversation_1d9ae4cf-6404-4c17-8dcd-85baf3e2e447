# 统一的生产级 Dockerfile - 多阶段构建
# 支持 test 和 prod 环境，通过环境变量控制差异
ARG NPM_REGISTRY=https://registry.npmmirror.com
ARG APK_MIRROR=mirrors.aliyun.com

# 构建阶段
FROM node:18-alpine AS builder

WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai

# 重新声明ARG变量（FROM之后需要重新声明）
ARG NPM_REGISTRY
ARG APK_MIRROR

# 配置Alpine镜像源
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories

# 配置npm镜像源
RUN npm config set registry ${NPM_REGISTRY}

# 复制package文件
COPY package*.json ./
COPY bun.lock* ./

# 安装依赖（ARM64架构跳过bun，修复兼容性问题）
RUN if [ -f "bun.lock" ] && [ "$(uname -m)" != "aarch64" ]; then \
        npm install -g bun && bun install; \
    else \
        npm ci; \
    fi

# 复制源代码
COPY . .

# 构建应用
RUN if [ -f "bun.lock" ] && [ "$(uname -m)" != "aarch64" ]; then \
        bun run build; \
    else \
        npm run build; \
    fi

# 生产阶段
FROM nginx:alpine

# 重新声明ARG变量
ARG APK_MIRROR

# 配置Alpine镜像源
RUN sed -i "s/dl-cdn.alpinelinux.org/${APK_MIRROR}/g" /etc/apk/repositories

# 安装curl用于健康检查
RUN apk add --no-cache curl tzdata

# 复制构建产物
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制统一的nginx配置
COPY config/nginx.conf /etc/nginx/nginx.conf

# 创建非root用户（如果不存在）
RUN if ! getent group nginx > /dev/null 2>&1; then \
        addgroup -g 1001 -S nginx; \
    fi && \
    if ! getent passwd nginx > /dev/null 2>&1; then \
        adduser -S nginx -u 1001; \
    fi

# 设置权限
RUN chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    touch /var/run/nginx.pid && \
    chown -R nginx:nginx /var/run/nginx.pid

# 切换到非root用户
USER nginx

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/ || exit 1

# 启动命令
CMD ["nginx", "-g", "daemon off;"]
