version: '3.8'

services:
  frontend:
    build:
      context: ../
      dockerfile: config/Dockerfile
    container_name: ${CONTAINER_NAME}
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_API_VERSION=${VITE_API_VERSION:-v1}
      - VITE_API_TIMEOUT=${VITE_API_TIMEOUT:-15000}
      - VITE_APP_ENV=${VITE_APP_ENV}
      - VITE_ENABLE_MOCK=${VITE_ENABLE_MOCK:-false}
      - VITE_ENABLE_DEVTOOLS=${VITE_ENABLE_DEVTOOLS:-false}
      - VITE_LOG_LEVEL=${VITE_LOG_LEVEL:-info}
      - VITE_ENABLE_CONSOLE=${VITE_ENABLE_CONSOLE:-false}
      - VITE_BUILD_SOURCEMAP=${VITE_BUILD_SOURCEMAP:-false}
      - VITE_BUILD_MINIFY=${VITE_BUILD_MINIFY:-true}
    ports:
      - "${FRONTEND_PORT:-3000}:${NGINX_PORT:-8080}"
    networks:
      - ${NETWORK_NAME}
    restart: unless-stopped
    deploy:
      replicas: ${REPLICAS:-1}
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-256M}
          cpus: ${CPU_LIMIT:-0.25}
        reservations:
          memory: ${MEMORY_RESERVE:-128M}
          cpus: ${CPU_RESERVE:-0.1}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${NGINX_PORT:-8080}/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: ${HEALTH_START_PERIOD:-30s}
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  ${NETWORK_NAME}:
    driver: bridge
    name: ${NETWORK_NAME}
    external: true
