# 测试环境配置
VITE_API_BASE_URL=http://localhost:8000
VITE_API_VERSION=v1
VITE_API_TIMEOUT=15000

# 测试配置
VITE_APP_ENV=testing
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 调试配置
VITE_LOG_LEVEL=info
VITE_ENABLE_CONSOLE=false

# 构建配置
VITE_BUILD_SOURCEMAP=true
VITE_BUILD_MINIFY=true

# 容器配置
CONTAINER_NAME=zentao-frontend-test
NETWORK_NAME=zentao-test-network
VOLUME_PREFIX=zentao-frontend-test
ENVIRONMENT=test
NODE_ENV=testing

# Docker Compose 资源配置
MEMORY_LIMIT=256M
CPU_LIMIT=0.25
MEMORY_RESERVE=128M
CPU_RESERVE=0.1
FRONTEND_PORT=3000
NGINX_PORT=8080
HEALTH_START_PERIOD=30s
REPLICAS=1
