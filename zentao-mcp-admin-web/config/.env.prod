# 生产环境配置
VITE_API_BASE_URL=https://api.yourdomain.com
VITE_API_VERSION=v1
VITE_API_TIMEOUT=20000

# 生产配置
VITE_APP_ENV=production
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=false

# 性能配置
VITE_LOG_LEVEL=error
VITE_ENABLE_CONSOLE=false
VITE_ENABLE_PWA=true

# 构建配置
VITE_BUILD_SOURCEMAP=false
VITE_BUILD_MINIFY=true
VITE_BUILD_GZIP=true

# 容器配置
CONTAINER_NAME=zentao-frontend-prod
NETWORK_NAME=zentao-prod-network
VOLUME_PREFIX=zentao-frontend-prod
ENVIRONMENT=prod
NODE_ENV=production

# Docker Compose 资源配置
MEMORY_LIMIT=512M
CPU_LIMIT=0.5
MEMORY_RESERVE=256M
CPU_RESERVE=0.2
FRONTEND_PORT=3001
NGINX_PORT=8080
HEALTH_START_PERIOD=60s
REPLICAS=2
