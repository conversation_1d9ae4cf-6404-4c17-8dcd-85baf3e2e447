{"program": {"fileNames": ["./node_modules/typescript/lib/lib.es5.d.ts", "./node_modules/typescript/lib/lib.es2015.d.ts", "./node_modules/typescript/lib/lib.es2016.d.ts", "./node_modules/typescript/lib/lib.es2017.d.ts", "./node_modules/typescript/lib/lib.es2018.d.ts", "./node_modules/typescript/lib/lib.es2019.d.ts", "./node_modules/typescript/lib/lib.es2020.d.ts", "./node_modules/typescript/lib/lib.dom.d.ts", "./node_modules/typescript/lib/lib.dom.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.core.d.ts", "./node_modules/typescript/lib/lib.es2015.collection.d.ts", "./node_modules/typescript/lib/lib.es2015.generator.d.ts", "./node_modules/typescript/lib/lib.es2015.iterable.d.ts", "./node_modules/typescript/lib/lib.es2015.promise.d.ts", "./node_modules/typescript/lib/lib.es2015.proxy.d.ts", "./node_modules/typescript/lib/lib.es2015.reflect.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.d.ts", "./node_modules/typescript/lib/lib.es2015.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2016.array.include.d.ts", "./node_modules/typescript/lib/lib.es2017.date.d.ts", "./node_modules/typescript/lib/lib.es2017.object.d.ts", "./node_modules/typescript/lib/lib.es2017.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2017.string.d.ts", "./node_modules/typescript/lib/lib.es2017.intl.d.ts", "./node_modules/typescript/lib/lib.es2017.typedarrays.d.ts", "./node_modules/typescript/lib/lib.es2018.asyncgenerator.d.ts", "./node_modules/typescript/lib/lib.es2018.asynciterable.d.ts", "./node_modules/typescript/lib/lib.es2018.intl.d.ts", "./node_modules/typescript/lib/lib.es2018.promise.d.ts", "./node_modules/typescript/lib/lib.es2018.regexp.d.ts", "./node_modules/typescript/lib/lib.es2019.array.d.ts", "./node_modules/typescript/lib/lib.es2019.object.d.ts", "./node_modules/typescript/lib/lib.es2019.string.d.ts", "./node_modules/typescript/lib/lib.es2019.symbol.d.ts", "./node_modules/typescript/lib/lib.es2019.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.bigint.d.ts", "./node_modules/typescript/lib/lib.es2020.date.d.ts", "./node_modules/typescript/lib/lib.es2020.promise.d.ts", "./node_modules/typescript/lib/lib.es2020.sharedmemory.d.ts", "./node_modules/typescript/lib/lib.es2020.string.d.ts", "./node_modules/typescript/lib/lib.es2020.symbol.wellknown.d.ts", "./node_modules/typescript/lib/lib.es2020.intl.d.ts", "./node_modules/typescript/lib/lib.es2020.number.d.ts", "./node_modules/typescript/lib/lib.decorators.d.ts", "./node_modules/typescript/lib/lib.decorators.legacy.d.ts", "./node_modules/@vue/shared/dist/shared.d.ts", "./node_modules/@vue/reactivity/dist/reactivity.d.ts", "./node_modules/@vue/runtime-core/dist/runtime-core.d.ts", "./node_modules/csstype/index.d.ts", "./node_modules/@vue/runtime-dom/dist/runtime-dom.d.ts", "./node_modules/vue/jsx-runtime/index.d.ts", "./node_modules/@babel/types/lib/index.d.ts", "./node_modules/@babel/parser/typings/babel-parser.d.ts", "./node_modules/@vue/compiler-core/dist/compiler-core.d.ts", "./node_modules/@vue/compiler-dom/dist/compiler-dom.d.ts", "./node_modules/vue/dist/vue.d.mts", "./node_modules/vue-router/dist/vue-router.d.ts", "./node_modules/axios/index.d.ts", "./src/types/api.ts", "./src/types/user.ts", "./src/config/api.ts", "./src/services/api.ts", "./src/services/auth.ts", "./src/components/layout.vue.ts", "./src/app.vue.ts", "./src/components/createapikeymodal.vue.ts", "./src/components/statscard.vue.ts", "./node_modules/vue-demi/lib/index.d.ts", "./node_modules/pinia/dist/pinia.d.ts", "./src/stores/apikeys.ts", "./src/views/apikeymanagement.vue.ts", "./src/stores/auditlogs.ts", "./src/stores/user.ts", "./src/views/auditlogs.vue.ts", "./src/views/dashboard.vue.ts", "./src/views/login.vue.ts", "./src/views/usermanagement.vue.ts", "./src/composables/usetoast.ts", "./src/views/userprofile.vue.ts", "./__vls_types.d.ts", "./node_modules/vite/types/hmrpayload.d.ts", "./node_modules/vite/types/customevent.d.ts", "./node_modules/vite/types/hot.d.ts", "./node_modules/vite/types/importglob.d.ts", "./node_modules/vite/types/importmeta.d.ts", "./node_modules/vite/client.d.ts", "./env.d.ts", "./src/router/index.ts", "./src/main.ts"], "fileInfos": [{"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, "0", "0", "0", "0", {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, {"version": "0", "affectsGlobalScope": true}, "0", "0"], "root": [[59, 67], [70, 80], [87, 89]], "options": {"composite": true, "esModuleInterop": true, "jsx": 1, "jsxImportSource": "vue", "module": 99, "noImplicitThis": true, "skipLibCheck": true, "strict": true, "target": 99, "useDefineForClassFields": true}, "fileIdsList": [[48, 50, 51, 56, 57, 69], [56, 57, 69, 86], [52], [46, 52, 53], [54], [46], [46, 47, 48, 50], [47, 48, 49, 50], [56, 57, 68], [85], [81], [82], [83, 84], [56, 57, 69], [50, 55], [50], [51, 56, 57, 64, 69], [51, 56, 57, 59, 69], [51, 56, 57, 63, 69], [51, 56, 57, 69], [51], [51, 56, 57, 65, 69, 86, 88], [51, 57, 63, 71, 74, 75, 76, 77, 79], [51, 58, 59, 60, 61], [51, 60, 62], [51, 56, 57, 59, 62, 69], [51, 56, 57, 62, 69], [51, 56, 57, 59, 66, 69, 70], [51, 56, 57, 60, 69, 72, 73], [51, 56, 57, 60, 69, 73], [51, 56, 57, 60, 69, 73, 78], [56, 68], [56, 69], [51, 59], [51, 62], [51, 56, 59, 62, 69], [51, 56, 57, 58, 69]], "referencedMap": [[80, 1], [87, 2], [53, 3], [54, 4], [55, 5], [47, 6], [48, 7], [50, 8], [69, 9], [86, 10], [82, 11], [83, 12], [85, 13], [68, 14], [57, 14], [56, 15], [51, 16], [65, 17], [66, 18], [64, 19], [67, 18], [78, 20], [61, 21], [89, 22], [88, 23], [62, 24], [63, 25], [70, 26], [72, 26], [73, 27], [59, 21], [60, 21], [71, 28], [74, 29], [75, 19], [76, 19], [77, 30], [79, 31]], "exportedModulesMap": [[80, 1], [87, 1], [53, 3], [54, 4], [55, 5], [47, 6], [48, 7], [50, 8], [69, 32], [86, 1], [82, 1], [81, 1], [83, 1], [84, 1], [85, 1], [68, 33], [57, 14], [56, 15], [51, 16], [65, 17], [66, 18], [64, 19], [67, 18], [78, 20], [61, 21], [89, 1], [88, 1], [62, 34], [63, 35], [70, 36], [72, 37], [73, 27], [59, 21], [60, 21], [71, 28], [74, 29], [75, 19], [76, 19], [77, 30], [79, 31]], "semanticDiagnosticsPerFile": [80, 87, 53, 52, 54, 55, 47, 48, 50, 46, 58, 49, 69, 44, 45, 8, 9, 11, 10, 2, 12, 13, 14, 15, 16, 17, 18, 19, 3, 4, 20, 24, 21, 22, 23, 25, 26, 27, 5, 28, 29, 30, 31, 6, 35, 32, 33, 34, 36, 7, 37, 42, 43, 38, 39, 40, 41, 1, 86, 82, 81, 83, 84, 85, 68, 57, 56, 51, 65, 66, 64, 67, 78, 61, 89, 88, 62, 63, 70, 72, 73, 59, 60, 71, 74, 75, 76, 77, 79], "affectedFilesPendingEmit": [65, 66, 64, 67, 78, 61, 89, 88, 62, 63, 70, 72, 73, 59, 60, 71, 74, 75, 76, 77, 79], "emitSignatures": [59, 60, 61, 62, 63, 64, 65, 66, 67, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79]}, "version": "5.3.3"}