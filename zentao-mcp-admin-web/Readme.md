
# 禅道MCP权限系统 - 用户使用手册

## 📖 系统概述

禅道MCP权限系统是一个基于FastAPI和Vue.js开发的现代化权限管理系统，提供用户管理、API密钥管理、审计日志等功能。

### 🎯 主要功能
- **用户管理**: 创建、编辑、删除用户账户
- **权限控制**: 两级权限体系（普通用户/管理员）
- **API密钥管理**: 生成和管理API访问密钥
- **审计日志**: 记录所有系统操作
- **安全认证**: JWT令牌认证和bcrypt密码加密

## 🚀 快速开始

### 1. 系统访问
- **管理界面**: http://localhost:3000
- **API文档**: http://localhost:8000/docs
- **默认管理员**: admin / admin123

### 2. 首次登录
1. 打开浏览器访问管理界面
2. 使用默认管理员账户登录
3. 建议立即修改默认密码

## 👤 用户权限说明

### 权限级别
1. **普通用户 (USER)**
   - 查看个人信息
   - 管理自己的API密钥
   - 重置自己的密码

2. **管理员 (ADMIN)**
   - 普通用户的所有权限
   - 管理其他用户账户
   - 查看审计日志
   - 管理所有API密钥



## 🎛️ 功能使用指南

### 📊 仪表板
仪表板提供系统概览信息：
- **统计信息**: 用户数量、API密钥数量等
- **快速操作**: 常用功能的快捷入口
- **系统信息**: 当前用户信息和登录状态

### 👥 用户管理

#### 查看用户列表
1. 点击导航栏"用户管理"
2. 查看所有用户的基本信息
3. 使用搜索框快速查找用户

#### 创建新用户
1. 点击"新建用户"按钮
2. 填写用户信息：
   - **用户名**: 唯一标识符
   - **邮箱**: 用于通知和找回密码
   - **密码**: 初始密码（用户可后续修改）
   - **用户类型**: 选择权限级别
3. 点击"创建"完成

#### 管理现有用户
- **编辑**: 修改用户基本信息
- **重置密码**: 为用户生成新的临时密码
- **启用/禁用**: 控制用户账户状态
- **删除**: 永久删除用户账户（谨慎操作）

### 🔑 API密钥管理

#### 创建API密钥
1. 访问"API Keys"页面
2. 点击"新建密钥"
3. 设置密钥信息：
   - **名称**: 密钥用途描述
   - **描述**: 详细说明
   - **权限**: 选择访问权限范围
4. 保存后获得密钥字符串

#### 使用API密钥
```bash
# 在HTTP请求头中添加认证信息
Authorization: Bearer YOUR_API_KEY
```

#### 管理密钥
- **查看**: 显示密钥基本信息（不显示完整密钥）
- **重置**: 生成新的密钥字符串
- **删除**: 撤销密钥访问权限

### 📝 审计日志

#### 查看操作记录
1. 访问"审计日志"页面
2. 查看所有系统操作记录
3. 包含信息：
   - **操作时间**: 精确到秒
   - **操作用户**: 执行操作的用户
   - **操作类型**: 创建、修改、删除等
   - **操作对象**: 被操作的资源
   - **操作详情**: 具体操作内容

#### 日志筛选
- **时间范围**: 选择特定时间段
- **用户筛选**: 查看特定用户的操作
- **操作类型**: 筛选特定类型的操作

### 👤 个人资料管理

#### 修改个人信息
1. 点击右上角用户头像
2. 选择"个人资料"
3. 修改可编辑的信息
4. 保存更改

#### 修改密码
1. 在个人资料页面
2. 点击"修改密码"
3. 输入当前密码和新密码
4. 确认修改

## 🔧 API接口使用

### 认证接口

#### 登录获取令牌
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your_username",
  "password": "your_password"
}
```

#### 获取当前用户信息
```bash
GET /api/v1/auth/me
Authorization: Bearer YOUR_TOKEN
```

### 用户管理接口

#### 获取用户列表
```bash
GET /api/v1/users
Authorization: Bearer YOUR_TOKEN
```

#### 创建用户
```bash
POST /api/v1/users
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "username": "newuser",
  "email": "<EMAIL>",
  "password": "password123",
  "user_type": "USER"
}
```

### API密钥接口

#### 获取密钥列表
```bash
GET /api/v1/api-keys
Authorization: Bearer YOUR_TOKEN
```

#### 创建API密钥
```bash
POST /api/v1/api-keys
Authorization: Bearer YOUR_TOKEN
Content-Type: application/json

{
  "name": "My API Key",
  "description": "For external service",
  "permissions": ["read", "write"]
}
```

## 🛡️ 安全最佳实践

### 密码安全
- 使用强密码（至少8位，包含字母、数字、特殊字符）
- 定期更换密码
- 不要共享账户密码

### API密钥安全
- 定期轮换API密钥
- 为不同用途创建不同的密钥
- 及时删除不再使用的密钥
- 不要在代码中硬编码密钥

### 账户安全
- 及时禁用离职员工账户
- 定期审查用户权限
- 监控异常登录活动

## 🚨 故障排除

### 登录问题
**问题**: 无法登录系统
**解决方案**:
1. 检查用户名和密码是否正确
2. 确认账户是否被禁用
3. 清除浏览器缓存和Cookie
4. 联系管理员重置密码

### 权限问题
**问题**: 无法访问某些功能
**解决方案**:
1. 确认当前用户权限级别
2. 联系管理员提升权限
3. 检查是否需要重新登录

### API调用问题
**问题**: API请求返回401错误
**解决方案**:
1. 检查API密钥是否正确
2. 确认密钥是否已过期
3. 验证请求头格式是否正确
4. 检查密钥权限是否足够

### 页面加载问题
**问题**: 页面无法正常加载
**解决方案**:
1. 检查网络连接
2. 确认服务是否正常运行
3. 清除浏览器缓存
4. 尝试使用其他浏览器

## 📞 技术支持

### 联系方式
- **技术支持**: 联系系统管理员
- **问题反馈**: 通过系统内置反馈功能
- **紧急情况**: 联系IT部门

### 常用资源
- **API文档**: http://localhost:8000/docs
- **系统状态**: http://localhost:8000/health
- **操作日志**: 系统内审计日志页面

## 📋 附录

### 系统要求
- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+
- **网络**: 稳定的网络连接
- **分辨率**: 最低1024x768

### 更新日志
- **v1.0.0**: 初始版本发布
  - 基础用户管理功能
  - API密钥管理
  - 审计日志记录
  - 权限控制系统

### 术语表
- **JWT**: JSON Web Token，用于身份认证的令牌
- **API**: Application Programming Interface，应用程序接口
- **CRUD**: Create, Read, Update, Delete，增删改查操作
- **bcrypt**: 密码哈希算法
