---
trigger: model_decision
description: 每次新开对话时候引入让模型了解项目的结构和任务
---
这是一个禅道MCP项目，为了给Agent添加禅道数据查询分析的支持。

文件doc/PRD.md是项目的整体需求文档。
文件doc/erd/zantaoapi.md是项目的ERD接口文档
文件.kiro/specs/zentao-mcp-service/requirements.md 是需求PRD拆解出的需求描述文件
文件.kiro/specs/zentao-mcp-service/design.md 是需求描述requirements拆解出的实施设计文件
文件.kiro/specs/zentao-mcp-service/tasks.md 是实施设计文件design和需求文件拆解出的当前任务列表。


这个python项目使用uv进行依赖管理，如果遇到依赖问题或者是导包问题请思考一下是不是因为调用方式有问题，必要时使用参数激活项目的依赖和python环境