# 产品上下文

## 项目概述
Zentao MCP服务化改造项目 - 将现有本地化MCP数据访问工具改造为"云端服务 + 轻量级客户端"架构

## 核心目标
- **保护核心资产**: 将核心逻辑部署在中心化的私有服务器上
- **实现集中管理**: 实现服务的统一更新、监控和日志记录  
- **提供可控访问**: 通过API密钥机制，对用户进行认证和授权管理
- **优化用户体验**: 提供与现有使用习惯（HTTP, STDIO, SSE）完全兼容的客户端工具

## 技术架构
### 后端服务 (zentao-mcp-backend-service)
- **技术栈**: Python 3.10+, FastAPI, PostgreSQL/SQLite, Docker
- **核心功能**: API接口层、认证授权、API Key管理、日志监控
- **业务逻辑**: 封装原zentao_mcp的完整功能

### 轻量级客户端 (zentao-mcp-client)  
- **技术栈**: Python 3.10+, FastMCP SDK
- **核心功能**: 本地代理服务，支持HTTP/STDIO/SSE三种模式
- **通信机制**: HTTPS + API Key与后端服务通信

### Admin Web界面 (zentao-token-web)
- **技术栈**: Vue.js 3, TypeScript, Vite, Tailwind CSS, bun
- **核心功能**: API Key生命周期管理的Web界面
- **用户**: 系统管理员

## 关键特性
* FastMCP到FastAPI完整迁移: 移除24个@mcp.tool()装饰器，创建8个服务类的完整服务层架构
* MCP工具API标准化: 实现符合设计文档的MCP工具格式API端点(/api/v1/mcp/tools/*)
* 安全架构升级: 使用bcrypt密码哈希，统一数据模型命名，明确认证机制分离
* 完整日志系统: 实现请求追踪、异常处理和性能监控的结构化日志解决方案，支持JSON格式输出、请求ID追踪、敏感信息过滤

## 项目阶段
1. **阶段零**: 项目初始化与环境配置 ✅
2. **阶段一**: 后端服务核心功能 ✅ FastMCP迁移完成
3. **阶段二**: 轻量级客户端开发  
4. **阶段三**: Admin Web前端开发
5. **阶段四**: 测试、部署与发布

## 成功指标
- 技术指标: 后端服务API成功率 > 99.9%，平均响应延迟 < 500ms
- 业务指标: 成功迁移现有用户到新服务架构
- 安全指标: 未发生未经授权的访问事件

[2025-08-28 20:35:35] - New feature: FastMCP到FastAPI迁移已100%完成。已移除24个@mcp.tool()装饰器，创建完整服务层架构(8个服务类+依赖注入)，更新8个API模块使用服务层，清理所有MCP相关代码。验证脚本显示23/23检查通过。用户要求：生成测试脚本、编译、运行服务。下一步：创建API测试脚本，启动uvicorn服务进行功能验证。项目使用uv管理依赖，FastAPI框架，异步架构。

[2025-09-01 01:29:39] - Architecture update: 完成服务端架构偏差修复，解决了密码哈希、数据模型、API路径等关键问题

[2025-09-03 01:59:06] - New feature: 完成服务端日志系统全面优化，实现请求追踪、异常处理和性能监控的完整日志解决方案