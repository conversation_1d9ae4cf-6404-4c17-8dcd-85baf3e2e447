# System Patterns *Optional*

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-08-28 17:10:28 - Log of updates made.

## Coding Patterns

### 日志系统架构模式
**模式名称**: 结构化日志与中间件集成
**适用场景**: Web应用的完整日志追踪和监控
**实现方式**:
```
日志配置层 (app/core/logging.py)
├── JSONFormatter - 结构化日志格式化
├── setup_logging() - 统一日志配置
└── 预定义Logger实例 (app_logger, api_logger, db_logger等)

中间件层 (app/middleware/)
├── RequestLoggingMiddleware - 请求生命周期追踪
├── ExceptionHandlingMiddleware - 异常统一处理
└── 敏感信息过滤机制

应用集成层 (main.py)
├── 日志系统初始化
├── 中间件注册
└── 全局异常处理器设置
```

### 日志上下文传递模式
**模式名称**: 请求ID追踪
**实现策略**:
- 每个请求生成唯一UUID作为request_id
- 通过request.state传递上下文信息
- 在所有日志记录中包含request_id
- 响应头中返回X-Request-ID便于客户端追踪

### 敏感信息保护模式
**模式名称**: 递归数据脱敏
**保护字段**: password, passwd, pwd, secret, token, key, authorization, auth, credential, private
**实现方式**: 递归遍历dict和list，自动替换敏感字段为"***FILTERED***"

## Architectural Patterns

### 中间件分层架构
**模式名称**: 洋葱式中间件架构
**执行顺序**:
1. ExceptionHandlingMiddleware (最外层，捕获所有异常)
2. RequestLoggingMiddleware (记录请求详情)
3. CORSMiddleware (处理跨域)
4. 业务逻辑处理

### 日志输出策略
**模式名称**: 环境适应性日志输出
**策略**:
- 开发环境: 控制台输出 + 详细DEBUG日志
- 生产环境: 文件输出 + 日志轮转 + INFO级别
- 测试环境: 内存缓存 + ERROR级别

## Testing Patterns

### 测试分层架构模式
**模式名称**: 四层测试金字塔
**适用场景**: 大型Web应用的测试架构设计
**实现方式**:
```
第四层: 系统测试 (System Tests)
├── MCP工具完整测试 (run_complete_test.py)
└── 管理员流程测试 (test_admin_flow.py)

第三层: 集成测试 (Integration Tests)  
├── 前后端集成测试 (integration_test.py)
└── 服务间集成测试 (tests/integration/)

第二层: API测试 (API Tests)
└── REST API端点测试 (tests/api/)

第一层: 单元测试 (Unit Tests)
└── 服务层和模型层测试 (tests/unit/)
```

### 日志系统测试模式
**模式名称**: 多维度日志验证
**测试维度**:
- 日志配置正确性测试
- 不同级别日志输出测试
- 上下文信息传递测试
- 异常日志记录测试
- API请求日志追踪测试
- 敏感信息过滤测试

### 测试文件命名模式
**模式名称**: 功能导向命名
**命名规则**:
- 单元测试: `test_{service_name}.py`
- API测试: `test_{endpoint_group}_endpoints.py`  
- 集成测试: `test_{flow_name}_flow.py`
- 系统测试: `{feature_name}_test.py`
- 日志测试: `test_logging_system.py`

### 测试运行模式
**模式名称**: 分级测试执行
**执行策略**:
- 快速测试: 仅单元测试 (< 30秒)
- 标准测试: 单元 + API + 集成 (< 5分钟)
- 完整测试: 所有测试包括UI (< 15分钟)
- 覆盖率测试: 带覆盖率报告的完整测试