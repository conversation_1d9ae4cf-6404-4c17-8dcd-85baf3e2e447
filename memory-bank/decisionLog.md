# 决策记录

## 2025-09-03 日志系统架构设计决策

### 决策背景
在代码审查过程中发现项目缺乏完整的日志系统，存在以下问题：
1. 缺乏统一的日志配置和格式标准
2. 没有请求追踪机制，难以调试问题
3. 异常处理不完善，缺少详细的错误信息记录
4. 没有性能监控和分析能力
5. 敏感信息可能在日志中泄露

### 考虑选项
**选项A**: 使用简单的Python logging模块
- 优点: 轻量级，学习成本低
- 缺点: 功能有限，缺乏结构化支持

**选项B**: 集成第三方日志服务（如ELK Stack）
- 优点: 功能强大，分析能力强
- 缺点: 复杂度高，部署成本大

**最终选择**: 自建结构化日志系统
- 优点: 可控性强，满足当前需求，便于扩展
- 缺点: 需要自行维护

### 实施细节
**核心组件设计**:
1. **日志配置模块** (`app/core/logging.py`)
   - JSONFormatter: 实现结构化日志输出
   - setup_logging(): 统一日志配置入口
   - 预定义Logger实例: 按功能模块分类

2. **请求日志中间件** (`app/middleware/logging.py`)
   - 自动生成请求ID (UUID)
   - 记录请求入参、出参和执行时间
   - 客户端IP获取和API Key脱敏
   - 敏感信息自动过滤

3. **异常处理中间件** (`app/middleware/exception.py`)
   - 统一异常捕获和处理
   - 详细异常信息记录（包含堆栈跟踪）
   - 标准化错误响应格式
   - 异常分类处理（HTTP、验证、数据库等）

**技术选型理由**:
- JSON格式: 便于日志分析工具处理
- 中间件架构: 无侵入性，易于维护
- 请求ID追踪: 便于分布式环境下的问题定位
- 敏感信息过滤: 保护用户隐私和系统安全

### 影响评估
**正面影响**:
- 大幅提升问题调试效率
- 提供完整的API调用审计能力
- 支持性能分析和优化
- 增强系统安全性

**性能影响**:
- 轻微增加请求处理时间（< 5ms）
- 日志文件存储空间需求增加
- JSON序列化带来的CPU开销

**维护成本**:
- 需要定期清理日志文件
- 需要监控日志系统本身的健康状态
- 需要培训团队使用新的日志查询方法

### 配置策略
**环境适应性**:
- 开发环境: DEBUG级别 + 控制台输出
- 测试环境: INFO级别 + 文件输出
- 生产环境: INFO级别 + 文件轮转 + 敏感信息严格过滤

**日志轮转策略**:
- 单文件最大10MB
- 保留5个历史文件
- 自动压缩历史文件

---

### 日志系统测试验证决策
[2025-09-03 01:59:06] - 完成日志系统测试验证

**验证范围**:
1. 基础日志功能测试 - 不同级别日志输出正常
2. 上下文信息传递测试 - request_id等上下文正确记录
3. 异常处理测试 - 异常信息和堆栈跟踪完整记录
4. API请求测试 - 实际HTTP请求的日志记录验证
5. 敏感信息过滤测试 - 确认密码等敏感信息被正确过滤

**测试结果**:
- ✅ 所有基础功能测试通过
- ✅ JSON格式输出正确
- ✅ 请求ID追踪正常工作
- ✅ 异常堆栈信息完整记录
- ✅ API请求日志包含完整上下文
- ✅ 日志文件正常生成和轮转

**部署建议**:
- 生产环境使用INFO级别避免过多DEBUG信息
- 配置日志文件路径到专门的logs目录
- 设置适当的日志轮转策略防止磁盘空间耗尽
- 定期监控日志系统性能影响

---

## 2025-08-28 阶段零问题修复决策

### 决策背景
在检查阶段零任务完成状态时，发现两个关键问题：
1. zentao-token-web项目缺少Vue.js项目的核心配置文件
2. zentao_engine模块迁移不完整，缺少tools和resources等关键业务逻辑

### 考虑因素
- 项目需要符合设计文档中的技术栈要求
- Vue.js项目需要完整的现代化配置（Vite、TypeScript、Tailwind CSS）
- zentao_engine需要包含原项目的完整业务逻辑以确保功能完整性

### 最终决策
1. **zentao-token-web配置**：
   - 使用Vite作为构建工具
   - 集成TypeScript支持
   - 使用Tailwind CSS作为样式框架
   - 配置bun作为包管理器

2. **zentao_engine迁移**：
   - 完整复制原zentao_mcp项目的tools和resources目录
   - 迁移核心配置文件（auth.py, config.py, exceptions.py等）
   - 创建适当的模块导出结构

### 影响评估
- 正面影响：项目结构现在完全符合设计要求，为后续开发奠定了坚实基础
- 技术债务：无新增技术债务，反而解决了配置不完整的问题
- 风险：低风险，都是标准配置和文件复制操作

---

### 架构决策
[2025-09-01 01:29:39] - 完成服务端架构偏差修复，解决了密码哈希、数据模型、API路径等关键问题

**决策背景:**
在代码审查过程中发现服务端实现与设计文档存在多个严重偏差：
1. 密码哈希使用不安全的SHA-256而非bcrypt
2. 数据模型使用admin_users表而非设计要求的users表
3. API路径使用RESTful风格而非MCP工具格式
4. 路由结构存在重复配置问题
5. 认证机制逻辑不清晰

**考虑选项:**
- 选项A: 保持现状，仅修复安全问题
- 选项B: 部分修复，保持向后兼容
- 最终选择: 完全按设计文档修复，确保架构一致性

**实施细节:**
- 受影响模块: 认证服务、数据模型、API路由、主应用配置
- 迁移策略: 直接修复，不保留旧数据（初始化阶段）
- 风险评估: 中等风险，但通过测试脚本验证确保稳定性

**影响评估:**
- 性能影响: bcrypt提升安全性，轻微影响登录性能
- 可维护性影响: 统一架构大幅提升代码可维护性
- 扩展性影响: 标准化API路径便于客户端集成

---

### 认证文件重复代码清理决策
[2025-09-03 00:04:39] - 完成认证文件分析和重复代码清理

**决策背景:**
在代码审查过程中发现项目中存在重复的认证文件：
1. `app/api/v1/endpoints/auth.py` - 使用依赖注入的标准认证端点
2. `app/api/v1/endpoints/auth_new.py` - 功能完全相同但实现方式略有差异的重复文件
3. 两个用户服务文件需要区分用途：`user_service.py`（禅道API调用）vs `user_management_service.py`（系统用户管理）

**考虑选项:**
- 选项A: 保留两个认证文件作为备份
- 选项B: 合并两个文件的优点
- 最终选择: 删除重复文件，保留架构更规范的版本

**实施细节:**
- 删除文件: `auth_new.py`（重复的认证端点实现）
- 保留文件: `auth.py`（使用依赖注入，符合FastAPI最佳实践）
- 验证结果: 确认无引用关系，路由配置正确
- 用户服务区分: 确认两个用户服务功能不同，都需要保留

**影响评估:**
- 代码质量: 消除重复代码，提升可维护性
- 架构一致性: 统一使用依赖注入模式
- 风险评估: 低风险，已验证无外部引用

---

### 测试架构重构决策
[2025-09-02 00:04:17] - 完成测试用例整理和架构重构

**决策背景:**
项目中存在大量重复和冗余的测试用例，影响维护效率：
1. 20+个测试文件分散在不同目录，功能重复
2. 多个同名test_api.py文件，命名混乱
3. 相似功能分布在不同文件中，缺乏清晰的测试分层
4. 测试目标不明确，部分测试文件功能重叠

**考虑选项:**
- 选项A: 保持现状，仅删除明显重复的文件
- 选项B: 部分整理，保留主要测试文件
- 最终选择: 完全重构测试架构，建立清晰的测试分层

**实施细节:**
- 删除文件: 15个冗余测试文件
- 保留核心: 4个核心测试文件 + 完整的tests/目录结构
- 测试分层: 单元测试 → API测试 → 集成测试 → 系统测试
- 工具支持: 创建统一的测试运行脚本和总结文档

**影响评估:**
- 维护效率: 大幅提升，减少75%的测试文件数量
- 测试质量: 提升，消除重复逻辑，建立清晰分层
- 开发体验: 改善，统一的测试运行方式和清晰的文档
---

### Web项目API配置修复决策
[2025-09-03 01:36:57] - 修复Web项目API接口访问链接配置问题

**决策背景:**
用户报告Web项目API接口访问出现404错误，访问的地址是 `/auth/login`，但实际后端路径是 `/api/v1/admin/auth/login`。问题出现在前端API配置中：
1. 环境变量 `VITE_API_BASE_URL=http://localhost:8000` 只包含服务器地址
2. API服务配置逻辑在环境变量存在时会忽略默认的完整路径
3. 缺乏统一的API版本控制管理

**考虑选项:**
- 选项A: 修改环境变量包含完整API路径
- 选项B: 修改API服务配置动态拼接版本路径（选择）
- 选项C: 创建统一的API配置文件

**实施细节:**
- 修改API服务类：使用动态拼接 `${baseUrl}/api/${version}/admin`
- 添加版本控制：支持 `VITE_API_VERSION` 环境变量
- 创建配置管理：`src/config/api.ts` 统一管理API配置
- 增强调试支持：开发环境下输出配置信息
- 验证修复效果：创建测试脚本验证路径正确性

**影响评估:**
- 兼容性: 向后兼容，不影响现有部署
- 可维护性: 大幅提升，统一的版本控制和配置管理
- 扩展性: 支持多版本API和不同环境配置
- 调试性: 增强了开发环境的调试能力

---


### 后端接口重复问题修复决策
[2025-09-03 00:34:42] - 完成后端接口重复问题修复

**决策背景:**
在代码审查过程中发现后端服务存在多个严重的重复问题：
1. 两个main.py文件：根目录和app目录下各有一个，内容几乎完全相同
2. API Key管理接口重复：admin.py和api_keys.py实现了相同的功能
3. 路由重复导入：main.py中存在重复的import语句
4. 文档生成时出现重复的接口分组

**考虑选项:**
- 选项A: 保持现状，通过文档说明区分用途
- 选项B: 部分合并，保留核心差异
- 最终选择: 完全合并重复接口，统一架构规范

**实施细节:**
- 删除文件: `app/main.py`（重复的应用入口）、`admin.py`（重复的API Key管理）
- 功能合并: 将admin.py的激活/撤销功能合并到api_keys.py
- 路由优化: 修复main.py中的重复导入，统一路由配置
- 接口统一: 所有API Key管理功能统一到 `/api/v1/admin/api-keys/*` 路径下

**影响评估:**
- 代码减少: 删除约40%的重复代码
- 维护性提升: 统一接口规范，减少维护负担
- 文档优化: 消除Swagger文档中的重复分组
- 架构一致性: 符合RESTful设计原则和项目规范