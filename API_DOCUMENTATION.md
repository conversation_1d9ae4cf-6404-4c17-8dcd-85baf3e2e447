# Zentao MCP API 详细文档

## 概述

Zentao MCP API 提供了完整的禅道项目管理系统集成接口，支持用户管理、API Key管理、MCP工具调用等功能。

**基础信息**
- 基础URL: `http://your-server:8000`
- API版本: v1
- 认证方式: Bearer <PERSON> (API Key)
- 数据格式: JSON

## 认证

### API Key认证

所有API请求都需要在请求头中包含有效的API Key：

```http
Authorization: Bearer your-api-key-here
Content-Type: application/json
```

### 获取API Key

1. 通过管理员Web界面创建
2. 通过管理员API创建（需要管理员权限）

## API端点分类

### 1. 系统健康检查

#### GET /health
检查系统健康状态

**请求示例:**
```bash
curl -X GET "http://localhost:8000/health"
```

**响应示例:**
```json
{
  "status": "healthy",
  "service": "zentao-mcp-backend-service",
  "version": "0.1.0",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 2. 认证管理

#### POST /api/v1/auth/login
用户登录

**请求体:**
```json
{
  "username": "admin",
  "password": "password"
}
```

**响应示例:**
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "ADMIN",
    "is_active": true
  }
}
```

#### POST /api/v1/auth/change-password
修改密码

**请求头:** `Authorization: Bearer <token>`

**请求体:**
```json
{
  "current_password": "old_password",
  "new_password": "new_password"
}
```

### 3. 用户管理

#### GET /api/v1/admin/users
获取用户列表（管理员权限）

**请求参数:**
- `skip`: 跳过记录数（默认0）
- `limit`: 返回记录数（默认100）
- `search`: 搜索关键词

**请求示例:**
```bash
curl -X GET "http://localhost:8000/api/v1/admin/users?skip=0&limit=10" \
  -H "Authorization: Bearer your-api-key"
```

**响应示例:**
```json
{
  "users": [
    {
      "id": 1,
      "username": "admin",
      "role": "ADMIN",
      "is_active": true,
      "created_at": "2024-01-01T12:00:00Z",
      "updated_at": "2024-01-01T12:00:00Z"
    }
  ],
  "total": 1,
  "skip": 0,
  "limit": 10
}
```

#### POST /api/v1/admin/users
创建用户（管理员权限）

**请求体:**
```json
{
  "username": "newuser",
  "password": "password123",
  "role": "USER"
}
```

#### PUT /api/v1/admin/users/{user_id}
更新用户信息（管理员权限）

#### DELETE /api/v1/admin/users/{user_id}
删除用户（软删除，管理员权限）

### 4. API Key管理

#### GET /api/v1/admin/keys
获取API Key列表（管理员权限）

**响应示例:**
```json
{
  "keys": [
    {
      "id": 1,
      "name": "测试Key",
      "key_preview": "sk-1234***5678",
      "user_id": 1,
      "user_username": "admin",
      "is_active": true,
      "created_at": "2024-01-01T12:00:00Z",
      "last_used_at": "2024-01-01T13:00:00Z"
    }
  ],
  "total": 1
}
```

#### POST /api/v1/admin/keys
创建API Key（管理员权限）

**请求体:**
```json
{
  "name": "新的API Key",
  "user_id": 1
}
```

**响应示例:**
```json
{
  "id": 2,
  "name": "新的API Key",
  "key": "sk-abcd1234efgh5678ijkl9012mnop3456",
  "user_id": 1,
  "created_at": "2024-01-01T12:00:00Z"
}
```

⚠️ **重要**: API Key只在创建时显示一次，请妥善保存。

#### GET /api/v1/keys
获取当前用户的API Key列表

#### POST /api/v1/keys
创建API Key（用户权限）

#### DELETE /api/v1/keys/{key_id}
删除API Key

### 5. MCP工具调用

所有MCP工具都在 `/api/v1/mcp/tools/` 路径下，需要API Key认证。

#### 基础查询工具

##### POST /api/v1/mcp/tools/zentao_get_all_departments
获取所有部门列表

**请求体:** `{}`

**响应示例:**
```json
{
  "rsCode": "1",
  "data": [
    {
      "id": 1,
      "name": "技术部",
      "parent": 0,
      "path": ",1,",
      "grade": 1,
      "order": 5,
      "position": "",
      "function": "",
      "manager": "admin"
    }
  ]
}
```

##### POST /api/v1/mcp/tools/zentao_get_all_projects
获取所有项目列表

**请求体:** `{}`

##### POST /api/v1/mcp/tools/zentao_get_project_detail
获取项目详情

**请求体:**
```json
{
  "project_id": 1
}
```

##### POST /api/v1/mcp/tools/zentao_get_stories_by_project
根据项目ID获取需求列表

**请求体:**
```json
{
  "project_id": 1
}
```

##### POST /api/v1/mcp/tools/zentao_get_tasks_by_project
根据项目ID获取任务列表

**请求体:**
```json
{
  "project_id": 1
}
```

##### POST /api/v1/mcp/tools/zentao_get_bugs_by_project
根据项目ID获取Bug列表

**请求体:**
```json
{
  "project_id": 1
}
```

#### 高级查询工具

##### POST /api/v1/mcp/tools/zentao_get_users_by_account
根据账号批量查询用户信息

**请求体:**
```json
{
  "accounts": ["user1", "user2", "user3"]
}
```

##### POST /api/v1/mcp/tools/zentao_get_tasks_by_account
根据域账号查询任务

**请求体:**
```json
{
  "account": "username",
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "is_doing": false
}
```

##### POST /api/v1/mcp/tools/zentao_get_bugs_by_time_and_dept
根据时间范围和部门查询Bug

**请求体:**
```json
{
  "start_date": "2024-01-01",
  "end_date": "2024-01-31",
  "dept_id": 1
}
```

#### 数据分析工具

##### POST /api/v1/mcp/tools/analyze_story_workload
分析需求工作量

**请求体:**
```json
{
  "story_ids": [1, 2, 3, 4, 5]
}
```

**响应示例:**
```json
{
  "total_stories": 5,
  "total_estimate": 120.5,
  "average_estimate": 24.1,
  "status_distribution": {
    "active": 3,
    "closed": 2
  },
  "priority_distribution": {
    "high": 2,
    "medium": 2,
    "low": 1
  }
}
```

##### POST /api/v1/mcp/tools/project_summary_analysis
项目汇总分析

**请求体:**
```json
{
  "project_id": 1
}
```

##### POST /api/v1/mcp/tools/personnel_workload_analysis
人员工作量分析

**请求体:**
```json
{
  "accounts": ["user1", "user2"],
  "start_date": "2024-01-01",
  "end_date": "2024-01-31"
}
```

#### 系统监控工具

##### POST /api/v1/mcp/tools/mcp_get_health_status
获取服务健康状态

**请求体:** `{}`

**响应示例:**
```json
{
  "status": "healthy",
  "database": "connected",
  "cache": "available",
  "external_services": {
    "zentao": "connected"
  },
  "uptime": 3600,
  "memory_usage": "45%",
  "cpu_usage": "12%"
}
```

##### POST /api/v1/mcp/tools/mcp_get_performance_metrics
获取性能指标

**请求体:** `{}`

## 错误处理

### 标准错误响应格式

```json
{
  "detail": "错误描述信息",
  "error_code": "ERROR_CODE",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 未认证或API Key无效
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `422 Unprocessable Entity`: 数据验证失败
- `500 Internal Server Error`: 服务器内部错误

## 使用示例

### Python示例

```python
import httpx

# 配置
BASE_URL = "http://localhost:8000"
API_KEY = "your-api-key-here"

headers = {
    "Authorization": f"Bearer {API_KEY}",
    "Content-Type": "application/json"
}

async def get_all_departments():
    async with httpx.AsyncClient() as client:
        response = await client.post(
            f"{BASE_URL}/api/v1/mcp/tools/zentao_get_all_departments",
            json={},
            headers=headers
        )
        return response.json()

# 使用示例
departments = await get_all_departments()
print(departments)
```

### JavaScript示例

```javascript
const BASE_URL = 'http://localhost:8000';
const API_KEY = 'your-api-key-here';

const headers = {
    'Authorization': `Bearer ${API_KEY}`,
    'Content-Type': 'application/json'
};

async function getAllProjects() {
    const response = await fetch(`${BASE_URL}/api/v1/mcp/tools/zentao_get_all_projects`, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify({})
    });
    
    return await response.json();
}

// 使用示例
getAllProjects().then(projects => {
    console.log(projects);
});
```

### cURL示例

```bash
# 获取所有部门
curl -X POST "http://localhost:8000/api/v1/mcp/tools/zentao_get_all_departments" \
  -H "Authorization: Bearer your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{}'

# 获取项目详情
curl -X POST "http://localhost:8000/api/v1/mcp/tools/zentao_get_project_detail" \
  -H "Authorization: Bearer your-api-key-here" \
  -H "Content-Type: application/json" \
  -d '{"project_id": 1}'
```

## 最佳实践

1. **API Key安全**: 
   - 不要在客户端代码中硬编码API Key
   - 使用环境变量存储API Key
   - 定期轮换API Key

2. **错误处理**:
   - 始终检查响应状态码
   - 实现适当的重试机制
   - 记录错误日志

3. **性能优化**:
   - 使用连接池
   - 实现请求缓存
   - 避免频繁的大量数据请求

4. **监控**:
   - 监控API调用频率
   - 跟踪响应时间
   - 设置告警机制

## 更多信息

- **Swagger UI**: `http://your-server:8000/docs`
- **OpenAPI规范**: `http://your-server:8000/openapi.json`
- **项目仓库**: [GitHub链接]
- **技术支持**: [联系方式]
