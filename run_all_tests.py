#!/usr/bin/env python3
"""
统一测试运行脚本
支持运行不同级别的测试套件
"""

import os
import sys
import subprocess
import argparse
import time
from pathlib import Path
from typing import List, Dict, Any

class TestRunner:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backend_dir = self.project_root / "zentao-mcp-backend-service"
        self.results = {}
        
    def log(self, message: str, level: str = "INFO"):
        """日志输出"""
        timestamp = time.strftime("%H:%M:%S")
        print(f"[{timestamp}] [{level}] {message}")
    
    def run_command(self, command: List[str], cwd: Path = None, timeout: int = 300) -> Dict[str, Any]:
        """运行命令并返回结果"""
        cwd = cwd or self.project_root
        self.log(f"执行命令: {' '.join(command)}")
        
        try:
            start_time = time.time()
            result = subprocess.run(
                command,
                cwd=cwd,
                capture_output=True,
                text=True,
                timeout=timeout
            )
            duration = time.time() - start_time
            
            return {
                "success": result.returncode == 0,
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "duration": round(duration, 2)
            }
        except subprocess.TimeoutExpired:
            return {
                "success": False,
                "error": f"命令超时 ({timeout}秒)",
                "duration": timeout
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "duration": 0
            }
    
    def check_service_health(self) -> bool:
        """检查服务健康状态"""
        self.log("检查服务健康状态...")
        
        try:
            import requests
            response = requests.get("http://localhost:8000/health", timeout=5)
            if response.status_code == 200:
                self.log("✅ 服务运行正常")
                return True
            else:
                self.log(f"❌ 服务状态异常: {response.status_code}")
                return False
        except Exception as e:
            self.log(f"❌ 无法连接服务: {e}")
            return False
    
    def run_unit_tests(self) -> Dict[str, Any]:
        """运行单元测试"""
        self.log("开始运行单元测试...")
        
        if not (self.backend_dir / "tests" / "unit").exists():
            return {"success": False, "error": "单元测试目录不存在"}
        
        command = ["python", "-m", "pytest", "tests/unit/", "-v", "--tb=short"]
        result = self.run_command(command, cwd=self.backend_dir)
        
        if result["success"]:
            self.log("✅ 单元测试通过")
        else:
            self.log("❌ 单元测试失败")
            
        return result
    
    def run_api_tests(self) -> Dict[str, Any]:
        """运行API测试"""
        self.log("开始运行API测试...")
        
        if not (self.backend_dir / "tests" / "api").exists():
            return {"success": False, "error": "API测试目录不存在"}
        
        command = ["python", "-m", "pytest", "tests/api/", "-v", "--tb=short"]
        result = self.run_command(command, cwd=self.backend_dir)
        
        if result["success"]:
            self.log("✅ API测试通过")
        else:
            self.log("❌ API测试失败")
            
        return result
    
    def run_integration_tests(self) -> Dict[str, Any]:
        """运行集成测试"""
        self.log("开始运行集成测试...")
        
        if not (self.backend_dir / "tests" / "integration").exists():
            return {"success": False, "error": "集成测试目录不存在"}
        
        command = ["python", "-m", "pytest", "tests/integration/", "-v", "--tb=short"]
        result = self.run_command(command, cwd=self.backend_dir)
        
        if result["success"]:
            self.log("✅ 集成测试通过")
        else:
            self.log("❌ 集成测试失败")
            
        return result
    
    def run_mcp_tests(self) -> Dict[str, Any]:
        """运行MCP工具测试"""
        self.log("开始运行MCP工具测试...")
        
        mcp_test_file = self.backend_dir / "run_complete_test.py"
        if not mcp_test_file.exists():
            return {"success": False, "error": "MCP测试文件不存在"}
        
        command = ["python", "run_complete_test.py"]
        result = self.run_command(command, cwd=self.backend_dir, timeout=600)
        
        if result["success"]:
            self.log("✅ MCP工具测试通过")
        else:
            self.log("❌ MCP工具测试失败")
            
        return result
    
    def run_admin_flow_tests(self) -> Dict[str, Any]:
        """运行管理员流程测试"""
        self.log("开始运行管理员流程测试...")
        
        admin_test_file = self.project_root / "test_admin_flow.py"
        if not admin_test_file.exists():
            return {"success": False, "error": "管理员流程测试文件不存在"}
        
        command = ["python", "test_admin_flow.py"]
        result = self.run_command(command, cwd=self.project_root)
        
        if result["success"]:
            self.log("✅ 管理员流程测试通过")
        else:
            self.log("❌ 管理员流程测试失败")
            
        return result
    
    def run_ui_tests(self) -> Dict[str, Any]:
        """运行UI集成测试"""
        self.log("开始运行UI集成测试...")
        
        ui_test_file = self.project_root / "integration_test.py"
        if not ui_test_file.exists():
            return {"success": False, "error": "UI测试文件不存在"}
        
        # 检查是否安装了selenium
        try:
            import selenium
        except ImportError:
            self.log("⚠️ 未安装selenium，跳过UI测试")
            return {"success": True, "skipped": True, "reason": "selenium未安装"}
        
        command = ["python", "integration_test.py"]
        result = self.run_command(command, cwd=self.project_root, timeout=600)
        
        if result["success"]:
            self.log("✅ UI集成测试通过")
        else:
            self.log("❌ UI集成测试失败")
            
        return result
    
    def run_coverage_tests(self) -> Dict[str, Any]:
        """运行覆盖率测试"""
        self.log("开始运行覆盖率测试...")
        
        command = [
            "python", "-m", "pytest", 
            "tests/", 
            "--cov=app", 
            "--cov-report=term-missing",
            "--cov-report=html:htmlcov",
            "-v"
        ]
        result = self.run_command(command, cwd=self.backend_dir, timeout=600)
        
        if result["success"]:
            self.log("✅ 覆盖率测试完成")
            self.log("📊 覆盖率报告已生成到 htmlcov/ 目录")
        else:
            self.log("❌ 覆盖率测试失败")
            
        return result
    
    def print_summary(self):
        """打印测试总结"""
        self.log("=" * 60)
        self.log("📊 测试结果总结")
        self.log("=" * 60)
        
        total_tests = 0
        passed_tests = 0
        total_duration = 0
        
        for test_name, result in self.results.items():
            total_tests += 1
            total_duration += result.get("duration", 0)
            
            if result.get("skipped"):
                status = "⏭️ 跳过"
                reason = f" ({result.get('reason', '未知原因')})"
            elif result.get("success"):
                status = "✅ 通过"
                passed_tests += 1
                reason = ""
            else:
                status = "❌ 失败"
                reason = f" ({result.get('error', '未知错误')})"
            
            duration = result.get("duration", 0)
            self.log(f"{test_name:20} | {status}{reason} | {duration:.2f}s")
        
        self.log("-" * 60)
        self.log(f"总测试数: {total_tests}")
        self.log(f"通过数: {passed_tests}")
        self.log(f"失败数: {total_tests - passed_tests}")
        self.log(f"总耗时: {total_duration:.2f}s")
        
        if passed_tests == total_tests:
            self.log("🎉 所有测试通过！")
            return True
        else:
            self.log(f"⚠️ {total_tests - passed_tests} 个测试失败")
            return False
    
    def run_quick_tests(self):
        """运行快速测试"""
        self.log("🚀 开始快速测试...")
        
        # 检查服务健康状态
        if not self.check_service_health():
            self.log("❌ 服务未运行，请先启动服务")
            return False
        
        # 运行单元测试
        self.results["单元测试"] = self.run_unit_tests()
        
        return self.print_summary()
    
    def run_standard_tests(self):
        """运行标准测试"""
        self.log("🚀 开始标准测试...")
        
        # 检查服务健康状态
        if not self.check_service_health():
            self.log("❌ 服务未运行，请先启动服务")
            return False
        
        # 运行测试套件
        self.results["单元测试"] = self.run_unit_tests()
        self.results["API测试"] = self.run_api_tests()
        self.results["集成测试"] = self.run_integration_tests()
        self.results["MCP工具测试"] = self.run_mcp_tests()
        
        return self.print_summary()
    
    def run_full_tests(self):
        """运行完整测试"""
        self.log("🚀 开始完整测试...")
        
        # 检查服务健康状态
        if not self.check_service_health():
            self.log("❌ 服务未运行，请先启动服务")
            return False
        
        # 运行所有测试
        self.results["单元测试"] = self.run_unit_tests()
        self.results["API测试"] = self.run_api_tests()
        self.results["集成测试"] = self.run_integration_tests()
        self.results["MCP工具测试"] = self.run_mcp_tests()
        self.results["管理员流程测试"] = self.run_admin_flow_tests()
        self.results["UI集成测试"] = self.run_ui_tests()
        
        return self.print_summary()
    
    def run_coverage_analysis(self):
        """运行覆盖率分析"""
        self.log("🚀 开始覆盖率分析...")
        
        # 检查服务健康状态
        if not self.check_service_health():
            self.log("❌ 服务未运行，请先启动服务")
            return False
        
        self.results["覆盖率测试"] = self.run_coverage_tests()
        
        return self.print_summary()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="统一测试运行脚本")
    parser.add_argument(
        "mode",
        choices=["quick", "standard", "full", "coverage"],
        help="测试模式: quick(快速), standard(标准), full(完整), coverage(覆盖率)"
    )
    parser.add_argument(
        "--no-health-check",
        action="store_true",
        help="跳过服务健康检查"
    )
    
    args = parser.parse_args()
    
    runner = TestRunner()
    
    try:
        if args.mode == "quick":
            success = runner.run_quick_tests()
        elif args.mode == "standard":
            success = runner.run_standard_tests()
        elif args.mode == "full":
            success = runner.run_full_tests()
        elif args.mode == "coverage":
            success = runner.run_coverage_analysis()
        else:
            print("❌ 未知的测试模式")
            sys.exit(1)
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"测试执行失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()