#!/bin/bash
# ============================================================================
# 镜像加速配置和部署环境差异检查脚本
# ============================================================================

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查镜像加速配置
check_mirror_acceleration() {
    log_info "========== 检查镜像加速配置 =========="
    
    local total_files=0
    local configured_files=0
    local missing_files=()
    
    # 定义需要检查的文件列表
    local files_to_check=(
        "zentao-mcp-admin-web/config/Dockerfile.dev:NPM_REGISTRY,APK_MIRROR"
        "zentao-mcp-admin-web/config/Dockerfile.prod:NPM_REGISTRY,APK_MIRROR"
        "zentao-mcp-admin-web/config/Dockerfile.test:NPM_REGISTRY,APK_MIRROR"
        "zentao-mcp-admin-web/Dockerfile:NPM_REGISTRY,APK_MIRROR"
        "zentao-mcp-backend-service/config/docker/Dockerfile.dev:PIP_INDEX_URL,PIP_TRUSTED_HOST,APT_MIRROR"
        "zentao-mcp-backend-service/config/docker/Dockerfile.prod:PIP_INDEX_URL,PIP_TRUSTED_HOST"
        "zentao-mcp-backend-service/config/docker/Dockerfile.test:PIP_INDEX_URL,PIP_TRUSTED_HOST,APT_MIRROR"
        "zentao-mcp-client/config/Dockerfile.dev:PIP_INDEX_URL,PIP_TRUSTED_HOST"
        "zentao-mcp-client/config/Dockerfile.prod:PIP_INDEX_URL,PIP_TRUSTED_HOST,APT_MIRROR"
        "zentao-mcp-client/config/Dockerfile.test:PIP_INDEX_URL,PIP_TRUSTED_HOST,APT_MIRROR"
    )
    
    for file_config in "${files_to_check[@]}"; do
        local file_path="${file_config%%:*}"
        local required_vars="${file_config##*:}"
        
        ((total_files++))
        
        if [[ -f "$file_path" ]]; then
            local all_vars_found=true
            local missing_vars=()
            
            IFS=',' read -ra VARS <<< "$required_vars"
            for var in "${VARS[@]}"; do
                if ! grep -q "ARG $var=" "$file_path"; then
                    all_vars_found=false
                    missing_vars+=("$var")
                fi
            done
            
            if $all_vars_found; then
                log_success "✅ $file_path - 镜像加速配置完整"
                ((configured_files++))
            else
                log_error "❌ $file_path - 缺少镜像加速配置: ${missing_vars[*]}"
                missing_files+=("$file_path")
            fi
        else
            log_error "❌ $file_path - 文件不存在"
            missing_files+=("$file_path")
        fi
    done
    
    echo ""
    log_info "镜像加速配置统计:"
    log_info "  总文件数: $total_files"
    log_success "  已配置: $configured_files"
    log_error "  未配置: $((total_files - configured_files))"
    
    if [[ ${#missing_files[@]} -gt 0 ]]; then
        log_warning "需要修复的文件:"
        for file in "${missing_files[@]}"; do
            log_warning "  - $file"
        done
        return 1
    else
        log_success "所有文件都已配置镜像加速!"
        return 0
    fi
}

# 检查test和prod环境配置差异
check_environment_differences() {
    log_info "========== 检查Test和Prod环境配置差异 =========="
    
    # 检查zentao-mcp-backend-service
    log_info "1. zentao-mcp-backend-service 环境差异:"
    compare_backend_environments
    
    echo ""
    
    # 检查zentao-mcp-admin-web
    log_info "2. zentao-mcp-admin-web 环境差异:"
    compare_frontend_environments
    
    echo ""
    
    # 检查zentao-mcp-client
    log_info "3. zentao-mcp-client 环境差异:"
    compare_client_environments
}

# 比较后端环境配置
compare_backend_environments() {
    local test_env="zentao-mcp-backend-service/config/environments/test.env"
    local prod_env="zentao-mcp-backend-service/config/environments/prod.env"
    
    if [[ -f "$test_env" && -f "$prod_env" ]]; then
        log_info "  环境变量差异对比:"
        
        # 关键配置项对比
        local key_configs=(
            "ENVIRONMENT"
            "DATABASE_URL"
            "WORKERS"
            "DEBUG"
            "LOG_LEVEL"
            "ZENTAO_ENV"
            "ENABLE_DOCS"
            "REPLICAS"
        )
        
        for config in "${key_configs[@]}"; do
            local test_val=$(grep "^$config=" "$test_env" 2>/dev/null | cut -d'=' -f2 || echo "未设置")
            local prod_val=$(grep "^$config=" "$prod_env" 2>/dev/null | cut -d'=' -f2 || echo "未设置")
            
            if [[ "$test_val" != "$prod_val" ]]; then
                log_info "    $config: test=$test_val, prod=$prod_val"
            fi
        done
        
        # 检查docker-compose配置
        local test_compose="zentao-mcp-backend-service/config/compose/docker-compose.test.yml"
        local prod_compose="zentao-mcp-backend-service/config/compose/docker-compose.prod.yml"
        
        if [[ -f "$test_compose" && -f "$prod_compose" ]]; then
            log_info "  Docker Compose差异:"
            
            # 资源限制对比
            local test_memory=$(grep -A5 "resources:" "$test_compose" | grep "memory:" | head -1 | awk '{print $2}' || echo "未设置")
            local prod_memory=$(grep -A5 "resources:" "$prod_compose" | grep "memory:" | head -1 | awk '{print $2}' || echo "未设置")
            
            log_info "    内存限制: test=$test_memory, prod=$prod_memory"
            
            # 副本数对比
            local test_replicas=$(grep "replicas:" "$test_compose" | awk '{print $2}' || echo "1")
            local prod_replicas=$(grep "replicas:" "$prod_compose" | awk '{print $2}' || echo "未设置")
            
            log_info "    副本数: test=$test_replicas, prod=$prod_replicas"
        fi
        
        log_success "  ✅ 后端环境配置检查完成"
    else
        log_error "  ❌ 后端环境配置文件缺失"
    fi
}

# 比较前端环境配置
compare_frontend_environments() {
    local test_env="zentao-mcp-admin-web/config/.env.test"
    local prod_env="zentao-mcp-admin-web/config/.env.prod"
    
    if [[ -f "$test_env" && -f "$prod_env" ]]; then
        log_info "  环境变量差异对比:"
        
        # 关键配置项对比
        local key_configs=(
            "VITE_APP_ENV"
            "VITE_API_BASE_URL"
            "VITE_LOG_LEVEL"
            "VITE_ENABLE_DEVTOOLS"
            "VITE_BUILD_SOURCEMAP"
            "VITE_BUILD_MINIFY"
        )
        
        for config in "${key_configs[@]}"; do
            local test_val=$(grep "^$config=" "$test_env" 2>/dev/null | cut -d'=' -f2 || echo "未设置")
            local prod_val=$(grep "^$config=" "$prod_env" 2>/dev/null | cut -d'=' -f2 || echo "未设置")
            
            if [[ "$test_val" != "$prod_val" ]]; then
                log_info "    $config: test=$test_val, prod=$prod_val"
            fi
        done
        
        log_success "  ✅ 前端环境配置检查完成"
    else
        log_error "  ❌ 前端环境配置文件缺失"
    fi
}

# 比较客户端环境配置
compare_client_environments() {
    local test_compose="zentao-mcp-client/config/docker-compose.test.yml"
    local prod_compose="zentao-mcp-client/config/docker-compose.prod.yml"
    
    if [[ -f "$test_compose" && -f "$prod_compose" ]]; then
        log_info "  Docker Compose差异对比:"
        
        # 环境变量对比
        local test_env_line=$(grep "ENVIRONMENT=" "$test_compose" | head -1 | awk -F'=' '{print $2}')
        local prod_env_line=$(grep "ENVIRONMENT=" "$prod_compose" | head -1 | awk -F'=' '{print $2}')
        
        log_info "    ENVIRONMENT: test=$test_env_line, prod=$prod_env_line"
        
        # 副本数对比
        local test_replicas=$(grep "replicas:" "$test_compose" | awk '{print $2}' || echo "1")
        local prod_replicas=$(grep "replicas:" "$prod_compose" | awk '{print $2}' || echo "未设置")
        
        log_info "    副本数: test=$test_replicas, prod=$prod_replicas"
        
        log_success "  ✅ 客户端环境配置检查完成"
    else
        log_error "  ❌ 客户端环境配置文件缺失"
    fi
}

# 检查部署脚本一致性
check_deployment_consistency() {
    log_info "========== 检查部署脚本一致性 =========="
    
    local projects=("zentao-mcp-backend-service" "zentao-mcp-admin-web" "zentao-mcp-client")
    local all_consistent=true
    
    for project in "${projects[@]}"; do
        local deploy_script="$project/deploy.sh"
        
        if [[ -f "$deploy_script" ]]; then
            log_info "检查 $project 部署脚本:"
            
            # 检查支持的环境
            if grep -q "test.*prod" "$deploy_script" && grep -q "dev.*test.*prod" "$deploy_script"; then
                log_success "  ✅ 支持 dev/test/prod 环境"
            else
                log_error "  ❌ 环境支持不完整"
                all_consistent=false
            fi
            
            # 检查支持的动作
            local required_actions=("deploy" "build" "start" "stop" "restart" "status" "logs" "clean")
            local missing_actions=()
            
            for action in "${required_actions[@]}"; do
                if ! grep -q "$action)" "$deploy_script"; then
                    missing_actions+=("$action")
                fi
            done
            
            if [[ ${#missing_actions[@]} -eq 0 ]]; then
                log_success "  ✅ 支持所有标准动作"
            else
                log_error "  ❌ 缺少动作: ${missing_actions[*]}"
                all_consistent=false
            fi
        else
            log_error "$project 部署脚本不存在"
            all_consistent=false
        fi
        
        echo ""
    done
    
    if $all_consistent; then
        log_success "所有部署脚本一致性检查通过!"
        return 0
    else
        log_error "部署脚本一致性检查失败!"
        return 1
    fi
}

# 主函数
main() {
    echo "============================================================================"
    echo "                    镜像加速配置和部署环境差异检查"
    echo "============================================================================"
    echo ""
    
    local exit_code=0
    
    # 检查镜像加速配置
    if ! check_mirror_acceleration; then
        exit_code=1
    fi
    
    echo ""
    
    # 检查环境配置差异
    check_environment_differences
    
    echo ""
    
    # 检查部署脚本一致性
    if ! check_deployment_consistency; then
        exit_code=1
    fi
    
    echo ""
    echo "============================================================================"
    if [[ $exit_code -eq 0 ]]; then
        log_success "所有检查通过! ✅"
    else
        log_error "检查发现问题，请修复后重新运行 ❌"
    fi
    echo "============================================================================"
    
    exit $exit_code
}

# 执行主函数
main "$@"
