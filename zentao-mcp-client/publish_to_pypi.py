#!/usr/bin/env python3
"""
PyPI发布脚本
"""

import subprocess
import sys
import os
import shutil
from pathlib import Path


def run_command(cmd, check=True):
    """运行命令"""
    print(f"执行: {' '.join(cmd)}")
    result = subprocess.run(cmd, check=check, capture_output=True, text=True)
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr, file=sys.stderr)
    return result


def clean_build_artifacts():
    """清理构建产物"""
    print("🧹 清理构建产物...")
    
    dirs_to_remove = ['build', 'dist', '*.egg-info']
    for pattern in dirs_to_remove:
        for path in Path('.').glob(pattern):
            if path.is_dir():
                shutil.rmtree(path)
                print(f"删除目录: {path}")
            else:
                path.unlink()
                print(f"删除文件: {path}")


def check_dependencies():
    """检查发布依赖"""
    print("🔧 检查发布依赖...")
    
    required_packages = ['build', 'twine']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"缺少依赖包: {missing_packages}")
        print("安装命令: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 依赖检查通过")
    return True


def build_package():
    """构建包"""
    print("📦 构建包...")
    
    try:
        run_command([sys.executable, "-m", "build"])
        print("✅ 包构建成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 包构建失败: {e}")
        return False


def check_package():
    """检查包"""
    print("🔍 检查包...")
    
    try:
        run_command([sys.executable, "-m", "twine", "check", "dist/*"])
        print("✅ 包检查通过")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 包检查失败: {e}")
        return False


def upload_to_test_pypi():
    """上传到测试PyPI"""
    print("🚀 上传到测试PyPI...")
    
    try:
        run_command([
            sys.executable, "-m", "twine", "upload",
            "--repository", "testpypi",
            "dist/*"
        ])
        print("✅ 上传到测试PyPI成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 上传到测试PyPI失败: {e}")
        return False


def upload_to_pypi():
    """上传到正式PyPI"""
    print("🚀 上传到正式PyPI...")
    
    # 确认上传
    response = input("确认上传到正式PyPI? (y/N): ")
    if response.lower() != 'y':
        print("取消上传")
        return False
    
    try:
        run_command([
            sys.executable, "-m", "twine", "upload",
            "dist/*"
        ])
        print("✅ 上传到正式PyPI成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 上传到正式PyPI失败: {e}")
        return False


def test_installation():
    """测试安装"""
    print("🧪 测试安装...")
    
    # 创建临时虚拟环境测试
    test_env = Path("test_env")
    if test_env.exists():
        shutil.rmtree(test_env)
    
    try:
        # 创建虚拟环境
        run_command([sys.executable, "-m", "venv", str(test_env)])
        
        # 激活虚拟环境并安装包
        if os.name == 'nt':  # Windows
            pip_path = test_env / "Scripts" / "pip"
        else:  # Unix/Linux/macOS
            pip_path = test_env / "bin" / "pip"
        
        run_command([str(pip_path), "install", "--index-url", "https://test.pypi.org/simple/", "zentao-mcp-client"])
        
        print("✅ 测试安装成功")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 测试安装失败: {e}")
        return False
    finally:
        # 清理测试环境
        if test_env.exists():
            shutil.rmtree(test_env)


def main():
    """主函数"""
    print("🚀 Zentao MCP Client PyPI发布工具")
    print("=" * 50)
    
    # 检查当前目录
    if not Path("pyproject.toml").exists():
        print("❌ 请在项目根目录运行此脚本")
        return 1
    
    # 检查依赖
    if not check_dependencies():
        return 1
    
    # 清理构建产物
    clean_build_artifacts()
    
    # 构建包
    if not build_package():
        return 1
    
    # 检查包
    if not check_package():
        return 1
    
    # 选择发布目标
    print("\n发布选项:")
    print("1. 测试PyPI (推荐)")
    print("2. 正式PyPI")
    print("3. 仅构建，不上传")
    
    choice = input("请选择 (1-3): ")
    
    if choice == "1":
        if upload_to_test_pypi():
            print("\n📋 测试安装命令:")
            print("pip install --index-url https://test.pypi.org/simple/ zentao-mcp-client")
            
            # 可选：自动测试安装
            test_choice = input("\n是否测试安装? (y/N): ")
            if test_choice.lower() == 'y':
                test_installation()
    
    elif choice == "2":
        upload_to_pypi()
        if upload_to_pypi():
            print("\n📋 安装命令:")
            print("pip install zentao-mcp-client")
    
    elif choice == "3":
        print("✅ 包构建完成，位于 dist/ 目录")
    
    else:
        print("❌ 无效选择")
        return 1
    
    print("\n🎉 发布流程完成!")
    return 0


if __name__ == "__main__":
    sys.exit(main())
