#!/usr/bin/env python3
"""
客户端测试运行脚本
"""

import subprocess
import sys
import os
from pathlib import Path


def run_tests():
    """运行所有测试"""
    print("🧪 运行Zentao MCP客户端测试套件...")
    print("=" * 60)
    
    # 确保在正确的目录
    os.chdir(Path(__file__).parent)
    
    try:
        # 运行pytest
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/",
            "-v",
            "--tb=short",
            "--color=yes",
            "--durations=10"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        print("-" * 60)
        
        result = subprocess.run(cmd, check=False)
        
        print("-" * 60)
        if result.returncode == 0:
            print("✅ 所有测试通过!")
        else:
            print("❌ 部分测试失败")
        
        return result.returncode == 0
        
    except FileNotFoundError:
        print("❌ pytest未安装，请运行: pip install pytest pytest-asyncio")
        return False
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False


def run_coverage():
    """运行测试覆盖率"""
    print("\n📊 运行测试覆盖率分析...")
    print("=" * 60)
    
    try:
        cmd = [
            sys.executable, "-m", "pytest",
            "tests/",
            "--cov=zentao_mcp_client",
            "--cov-report=html",
            "--cov-report=term-missing",
            "--cov-fail-under=70"
        ]
        
        result = subprocess.run(cmd, check=False)
        
        if result.returncode == 0:
            print("✅ 测试覆盖率达标!")
            print("📄 详细报告: htmlcov/index.html")
        else:
            print("⚠️  测试覆盖率不足")
        
        return result.returncode == 0
        
    except FileNotFoundError:
        print("❌ pytest-cov未安装，请运行: pip install pytest-cov")
        return False


def run_specific_test(test_file):
    """运行特定测试文件"""
    print(f"🧪 运行特定测试: {test_file}")
    print("=" * 60)
    
    try:
        cmd = [
            sys.executable, "-m", "pytest",
            f"tests/{test_file}",
            "-v",
            "--tb=short",
            "--color=yes"
        ]
        
        result = subprocess.run(cmd, check=False)
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "coverage":
            success = run_coverage()
        elif sys.argv[1].startswith("test_"):
            success = run_specific_test(sys.argv[1])
        else:
            print("用法:")
            print("  python run_tests.py              # 运行所有测试")
            print("  python run_tests.py coverage     # 运行覆盖率测试")
            print("  python run_tests.py test_config.py  # 运行特定测试")
            return 1
    else:
        success = run_tests()
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
