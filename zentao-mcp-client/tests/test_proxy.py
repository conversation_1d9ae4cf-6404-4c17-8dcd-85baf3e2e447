"""
代理服务模块测试
"""

import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
import httpx

from zentao_mcp_client.config import ClientConfig
from zentao_mcp_client.proxy import ZentaoMCPProxy


class TestZentaoMCPProxy:
    """Zentao MCP代理测试类"""
    
    @pytest.fixture
    def mock_config(self):
        """模拟配置"""
        config = MagicMock(spec=ClientConfig)
        config.get_backend_url.return_value = "http://test.example.com:8000"
        config.get_api_key.return_value = "test-api-key-12345"
        return config
    
    @pytest.fixture
    def proxy(self, mock_config):
        """代理实例"""
        return ZentaoMCPProxy(mock_config)
    
    def test_proxy_initialization(self, proxy):
        """测试代理初始化"""
        assert proxy.backend_url == "http://test.example.com:8000"
        assert proxy.api_key == "test-api-key-12345"
        assert proxy.client is not None
        assert proxy.mcp is not None
        assert len(proxy.tool_endpoints) > 0
    
    def test_tool_endpoints_mapping(self, proxy):
        """测试工具端点映射"""
        # 检查关键工具端点
        expected_tools = [
            "zentao_get_all_departments",
            "zentao_get_all_projects",
            "zentao_get_bug_detail",
            "analyze_story_workload",
            "mcp_get_health_status"
        ]
        
        for tool in expected_tools:
            assert tool in proxy.tool_endpoints
            assert proxy.tool_endpoints[tool].startswith("/api/v1/mcp/tools/")
    
    def test_tool_endpoints_completeness(self, proxy):
        """测试工具端点完整性"""
        # 应该包含所有26个工具
        assert len(proxy.tool_endpoints) >= 26
        
        # 检查各类工具都存在
        dept_tools = [k for k in proxy.tool_endpoints.keys() if "department" in k]
        project_tools = [k for k in proxy.tool_endpoints.keys() if "project" in k]
        analysis_tools = [k for k in proxy.tool_endpoints.keys() if any(keyword in k for keyword in ["analyze", "analysis", "workload", "tracking", "filter", "batch", "validate", "relation"])]
        system_tools = [k for k in proxy.tool_endpoints.keys() if "mcp_get" in k]
        
        assert len(dept_tools) >= 2
        assert len(project_tools) >= 4
        assert len(analysis_tools) >= 6
        assert len(system_tools) >= 2
    
    @pytest.mark.asyncio
    async def test_forward_request_success(self, proxy):
        """测试请求转发成功"""
        # 模拟HTTP响应
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"rsCode": "1", "data": {"test": "success"}}
        mock_response.raise_for_status = MagicMock()
        
        with patch.object(proxy.client, 'post', new_callable=AsyncMock) as mock_post:
            mock_post.return_value = mock_response
            
            result = await proxy._forward_request("zentao_get_all_departments", {})
            
            # 验证请求参数
            mock_post.assert_called_once()
            call_args = mock_post.call_args
            
            assert call_args[0][0] == "http://test.example.com:8000/api/v1/mcp/tools/zentao_get_all_departments"
            assert call_args[1]["json"] == {}
            assert "Authorization" in call_args[1]["headers"]
            assert call_args[1]["headers"]["Authorization"] == "Bearer test-api-key-12345"
            
            # 验证返回结果
            assert result == {"rsCode": "1", "data": {"test": "success"}}
    
    @pytest.mark.asyncio
    async def test_forward_request_unknown_tool(self, proxy):
        """测试未知工具请求"""
        with pytest.raises(Exception) as exc_info:
            await proxy._forward_request("unknown_tool", {})
        
        assert "不支持的工具: unknown_tool" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_forward_request_http_error(self, proxy):
        """测试HTTP错误处理"""
        mock_response = MagicMock()
        mock_response.status_code = 500
        mock_response.text = "Internal Server Error"
        mock_response.json.return_value = {"detail": "服务器内部错误"}
        
        http_error = httpx.HTTPStatusError(
            "Server Error", 
            request=MagicMock(), 
            response=mock_response
        )
        
        with patch.object(proxy.client, 'post', new_callable=AsyncMock) as mock_post:
            mock_post.side_effect = http_error
            
            with pytest.raises(Exception) as exc_info:
                await proxy._forward_request("zentao_get_all_departments", {})
            
            assert "后端服务错误" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_forward_request_network_error(self, proxy):
        """测试网络错误处理"""
        with patch.object(proxy.client, 'post', new_callable=AsyncMock) as mock_post:
            mock_post.side_effect = httpx.ConnectError("Connection failed")
            
            with pytest.raises(Exception) as exc_info:
                await proxy._forward_request("zentao_get_all_departments", {})
            
            assert "转发请求失败" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_close(self, proxy):
        """测试代理关闭"""
        with patch.object(proxy.client, 'aclose', new_callable=AsyncMock) as mock_close:
            await proxy.close()
            mock_close.assert_called_once()
    
    def test_mcp_tool_registration(self, proxy):
        """测试MCP工具注册"""
        # 验证FastMCP实例存在
        assert proxy.mcp is not None
        assert proxy.mcp.name == "Zentao MCP Client"
    
    @pytest.mark.asyncio
    async def test_start_http_server_missing_dependencies(self, proxy):
        """测试HTTP服务器启动时缺少依赖"""
        with patch('fastapi.FastAPI', side_effect=ImportError):
            with pytest.raises(Exception) as exc_info:
                await proxy.start_http_server("localhost", 8080)
            
            assert "HTTP模式需要安装 fastapi 和 uvicorn" in str(exc_info.value)


class TestProxyIntegration:
    """代理集成测试"""
    
    @pytest.fixture
    def real_config(self):
        """真实配置（用于集成测试）"""
        config = MagicMock(spec=ClientConfig)
        config.get_backend_url.return_value = "http://localhost:8000"
        config.get_api_key.return_value = "test-integration-key"
        return config
    
    @pytest.mark.asyncio
    async def test_proxy_lifecycle(self, real_config):
        """测试代理生命周期"""
        proxy = ZentaoMCPProxy(real_config)
        
        try:
            # 验证初始化
            assert proxy.backend_url == "http://localhost:8000"
            assert proxy.api_key == "test-integration-key"
            
            # 验证工具端点映射
            assert "zentao_get_all_departments" in proxy.tool_endpoints
            
            # 验证HTTP客户端
            assert isinstance(proxy.client, httpx.AsyncClient)
            
        finally:
            # 确保清理
            await proxy.close()
    
    @pytest.mark.asyncio
    async def test_request_format_validation(self, real_config):
        """测试请求格式验证"""
        proxy = ZentaoMCPProxy(real_config)
        
        try:
            # 测试工具名称到端点的映射
            tool_name = "zentao_get_all_departments"
            endpoint = proxy.tool_endpoints.get(tool_name)
            
            assert endpoint == "/api/v1/mcp/tools/zentao_get_all_departments"
            
            # 验证完整URL构建
            full_url = f"{proxy.backend_url}{endpoint}"
            assert full_url == "http://localhost:8000/api/v1/mcp/tools/zentao_get_all_departments"
            
        finally:
            await proxy.close()


@pytest.mark.asyncio
async def test_proxy_server_functions():
    """测试代理服务器函数"""
    from zentao_mcp_client.proxy import start_proxy_server
    
    mock_config = MagicMock(spec=ClientConfig)
    mock_config.get_backend_url.return_value = "http://test.com"
    mock_config.get_api_key.return_value = "test-key"
    
    # 测试不同模式的启动（模拟）
    with patch('zentao_mcp_client.proxy.ZentaoMCPProxy') as mock_proxy_class:
        mock_proxy = MagicMock()
        mock_proxy_class.return_value = mock_proxy
        mock_proxy.mcp.run = AsyncMock()
        mock_proxy.start_http_server = AsyncMock()
        mock_proxy.close = AsyncMock()
        
        # 模拟STDIO模式
        with patch('asyncio.run') as mock_run:
            start_proxy_server("localhost", 8080, mock_config, "stdio")
            mock_run.assert_called_once()
        
        # 模拟HTTP模式
        with patch('asyncio.run') as mock_run:
            start_proxy_server("localhost", 8080, mock_config, "http")
            mock_run.assert_called_once()


def test_tool_endpoint_consistency():
    """测试工具端点一致性"""
    # 创建模拟配置
    mock_config = MagicMock(spec=ClientConfig)
    mock_config.get_backend_url.return_value = "http://test.com"
    mock_config.get_api_key.return_value = "test-key"
    
    proxy = ZentaoMCPProxy(mock_config)
    
    # 验证所有端点都以正确的前缀开始
    for tool_name, endpoint in proxy.tool_endpoints.items():
        assert endpoint.startswith("/api/v1/mcp/tools/")
        assert tool_name in endpoint  # 工具名称应该在端点中
    
    # 验证没有重复的端点
    endpoints = list(proxy.tool_endpoints.values())
    assert len(endpoints) == len(set(endpoints))
    
    # 验证关键工具都存在
    required_tools = [
        "zentao_get_all_departments",
        "zentao_get_all_projects", 
        "zentao_get_bug_detail",
        "analyze_story_workload",
        "mcp_get_health_status"
    ]
    
    for tool in required_tools:
        assert tool in proxy.tool_endpoints
