"""
配置管理模块测试
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, mock_open

from zentao_mcp_client.config import ClientConfig


class TestClientConfig:
    """客户端配置测试类"""
    
    def setup_method(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "config.ini"
    
    def teardown_method(self):
        """测试后清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_config_file_path(self, mock_home):
        """测试配置文件路径"""
        mock_home.return_value = Path(self.temp_dir)
        config = ClientConfig()
        
        expected_path = Path(self.temp_dir) / ".zentao_mcp_client" / "config.ini"
        assert config.config_file == expected_path
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_set_and_get_backend_url(self, mock_home):
        """测试后端URL设置和获取"""
        mock_home.return_value = Path(self.temp_dir)
        config = ClientConfig()
        
        test_url = "http://test.example.com:8000"
        config.set_backend_url(test_url)
        
        assert config.get_backend_url() == test_url
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_set_and_get_api_key(self, mock_home):
        """测试API Key设置和获取"""
        mock_home.return_value = Path(self.temp_dir)
        config = ClientConfig()
        
        test_key = "test-api-key-12345"
        config.set_api_key(test_key)
        
        assert config.get_api_key() == test_key
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_is_configured(self, mock_home):
        """测试配置完整性检查"""
        mock_home.return_value = Path(self.temp_dir)
        config = ClientConfig()
        
        # 初始状态应该未配置
        assert not config.is_configured()
        
        # 只设置URL，仍未完整配置
        config.set_backend_url("http://test.com")
        assert not config.is_configured()
        
        # 设置API Key后，配置完整
        config.set_api_key("test-key")
        assert config.is_configured()
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_get_config_info(self, mock_home):
        """测试配置信息获取"""
        mock_home.return_value = Path(self.temp_dir)
        config = ClientConfig()
        
        config.set_backend_url("http://test.com")
        config.set_api_key("test-key-12345")
        
        info = config.get_config_info()
        
        assert "config_file" in info
        assert "backend_url" in info
        assert "api_key_configured" in info
        assert "api_key_preview" in info
        
        assert info["backend_url"] == "http://test.com"
        assert info["api_key_configured"] is True
        assert info["api_key_preview"] == "te***45"
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_config_persistence(self, mock_home):
        """测试配置持久化"""
        mock_home.return_value = Path(self.temp_dir)
        
        # 创建第一个配置实例并设置值
        config1 = ClientConfig()
        config1.set_backend_url("http://persistent.test.com")
        config1.set_api_key("persistent-key")
        
        # 创建第二个配置实例，应该能读取到相同的值
        config2 = ClientConfig()
        assert config2.get_backend_url() == "http://persistent.test.com"
        assert config2.get_api_key() == "persistent-key"
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_config_directory_creation(self, mock_home):
        """测试配置目录自动创建"""
        mock_home.return_value = Path(self.temp_dir)
        
        config_dir = Path(self.temp_dir) / ".zentao_mcp_client"
        assert not config_dir.exists()
        
        # 创建配置实例应该自动创建目录
        config = ClientConfig()
        config.set_backend_url("http://test.com")
        
        assert config_dir.exists()
        assert config_dir.is_dir()
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_invalid_config_file_handling(self, mock_home):
        """测试无效配置文件处理"""
        mock_home.return_value = Path(self.temp_dir)
        
        # 创建一个无效的配置文件
        config_dir = Path(self.temp_dir) / ".zentao_mcp_client"
        config_dir.mkdir(parents=True, exist_ok=True)
        config_file = config_dir / "config.ini"
        
        with open(config_file, 'w') as f:
            f.write("invalid config content")
        
        # 应该能够处理无效配置文件
        config = ClientConfig()
        assert config.get_backend_url() is None
        assert config.get_api_key() is None
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_empty_values_handling(self, mock_home):
        """测试空值处理"""
        mock_home.return_value = Path(self.temp_dir)
        config = ClientConfig()
        
        # 测试空字符串
        config.set_backend_url("")
        config.set_api_key("")
        
        assert not config.is_configured()
        
        # 测试None值
        config.set_backend_url(None)
        config.set_api_key(None)
        
        assert not config.is_configured()
    
    @patch('zentao_mcp_client.config.Path.home')
    def test_api_key_preview_edge_cases(self, mock_home):
        """测试API Key预览的边界情况"""
        mock_home.return_value = Path(self.temp_dir)
        config = ClientConfig()
        
        # 测试短Key
        config.set_api_key("abc")
        info = config.get_config_info()
        assert info["api_key_preview"] == "***"
        
        # 测试中等长度Key
        config.set_api_key("abcdef")
        info = config.get_config_info()
        assert info["api_key_preview"] == "abc***"
        
        # 测试长Key
        config.set_api_key("abcdefghijklmnopqrstuvwxyz")
        info = config.get_config_info()
        assert info["api_key_preview"] == "ab***yz"


@pytest.fixture
def temp_config():
    """临时配置fixture"""
    with tempfile.TemporaryDirectory() as temp_dir:
        with patch('zentao_mcp_client.config.Path.home') as mock_home:
            mock_home.return_value = Path(temp_dir)
            yield ClientConfig()


def test_config_integration(temp_config):
    """配置集成测试"""
    config = temp_config
    
    # 完整的配置流程测试
    assert not config.is_configured()
    
    config.set_backend_url("http://integration.test.com:8000")
    config.set_api_key("integration-test-key-12345")
    
    assert config.is_configured()
    
    info = config.get_config_info()
    assert info["backend_url"] == "http://integration.test.com:8000"
    assert info["api_key_configured"] is True
    assert "in***45" == info["api_key_preview"]
