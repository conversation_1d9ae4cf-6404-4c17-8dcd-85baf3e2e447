# 生产环境配置文件

# 客户端配置
ZENTAO_MCP_BACKEND_URL=http://localhost:8000
ZENTAO_MCP_API_KEY=your-production-api-key-here
CLIENT_MODE=http
CLIENT_HOST=0.0.0.0
CLIENT_PORT=8080
LOG_LEVEL=WARNING
ENVIRONMENT=production

# 容器配置
CONTAINER_NAME=zentao-client-prod
NETWORK_NAME=zentao-prod-network
VOLUME_PREFIX=zentao-client-prod

# Docker Compose 资源配置
MEMORY_LIMIT=512M
CPU_LIMIT=0.5
MEMORY_RESERVE=256M
CPU_RESERVE=0.2
HEALTH_START_PERIOD=60s
REPLICAS=2
