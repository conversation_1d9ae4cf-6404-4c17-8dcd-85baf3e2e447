# 统一的生产级 Dockerfile
# 支持 test 和 prod 环境，通过环境变量控制差异
ARG PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ARG PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
ARG APT_MIRROR=mirrors.tuna.tsinghua.edu.cn

FROM python:3.11-slim

# 重新声明ARG变量（FROM之后需要重新声明）
ARG PIP_INDEX_URL
ARG PIP_TRUSTED_HOST
ARG APT_MIRROR

WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    DEBIAN_FRONTEND=noninteractive

# 配置pip镜像源和APT镜像源
ENV PIP_INDEX_URL=${PIP_INDEX_URL} \
    PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST} \
    APT_MIRROR=${APT_MIRROR}

# 配置APT镜像源并安装系统依赖
RUN set -eux; \
    # 完全替换APT源配置
    rm -rf /etc/apt/sources.list.d/*; \
    mkdir -p /etc/apt; \
    # 配置清华大学镜像源（使用trixie源，与backend保持一致）
    echo "deb https://${APT_MIRROR}/debian/ trixie main" > /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian/ trixie-updates main" >> /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian-security/ trixie-security main" >> /etc/apt/sources.list; \
    # 清理可能存在的其他源文件
    find /etc/apt -name "*.list" -not -name "sources.list" -delete || true; \
    # 更新包列表并安装系统依赖
    apt-get update; \
    apt-get install -y --no-install-recommends \
        curl \
        locales; \
    # 配置语言环境
    echo "zh_CN.UTF-8 UTF-8" > /etc/locale.gen; \
    locale-gen; \
    # 清理APT缓存
    apt-get clean; \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*;

# 复制依赖文件
COPY pyproject.toml uv.lock ./

# 安装uv和Python依赖（使用国内镜像源）
RUN set -eux; \
    # 安装uv包管理器
    if [ -n "${PIP_INDEX_URL}" ] && [ -n "${PIP_TRUSTED_HOST}" ]; then \
        pip install --no-cache-dir -i ${PIP_INDEX_URL} --trusted-host ${PIP_TRUSTED_HOST} uv; \
    else \
        pip install --no-cache-dir uv; \
    fi; \
    # 同步依赖（测试环境，使用国内镜像源）
    UV_INDEX_URL=${PIP_INDEX_URL} uv sync --no-cache; \
    # 清理pip缓存
    pip cache purge || true; \
    # 清理uv缓存
    uv cache clean || true;

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p logs

# 创建非root用户
RUN useradd --create-home --shell /bin/bash app \
    && chown -R app:app /app

# 切换到非root用户
USER app

# 暴露端口（HTTP模式）
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 启动命令（测试环境HTTP模式）
CMD ["uv", "run", "python", "-m", "zentao_mcp_client", "start", "--mode", "http", "--host", "0.0.0.0", "--port", "8080"]
