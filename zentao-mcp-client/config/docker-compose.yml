version: '3.8'

services:
  client:
    build:
      context: ../
      dockerfile: config/Dockerfile
    image: zentao-mcp-client-${ENVIRONMENT}:latest
    container_name: ${CONTAINER_NAME:-zentao-client-test}
    environment:
      - ZENTAO_MCP_BACKEND_URL=${ZENTAO_MCP_BACKEND_URL}
      - ZENTAO_MCP_API_KEY=${ZENTAO_MCP_API_KEY}
      - CLIENT_MODE=${CLIENT_MODE:-http}
      - CLIENT_HOST=${CLIENT_HOST:-0.0.0.0}
      - CLIENT_PORT=${CLIENT_PORT:-8080}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - ENVIRONMENT=${ENVIRONMENT}
    ports:
      - "${CLIENT_PORT:-8080}:8080"
    volumes:
      - zentao-client-test-logs:/app/logs
    networks:
      - zentao-test-network
    restart: unless-stopped
    deploy:
      replicas: ${REPLICAS:-1}
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-256M}
          cpus: ${CPU_LIMIT:-0.25}
        reservations:
          memory: ${MEMORY_RESERVE:-128M}
          cpus: ${CPU_RESERVE:-0.1}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: ${HEALTH_START_PERIOD:-30s}
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    profiles:
      - client

networks:
  zentao-test-network:
    driver: bridge
    name: ${NETWORK_NAME:-zentao-test-network}

volumes:
  zentao-client-test-logs:
    driver: local
