#!/bin/bash
# ============================================================================
# 前端部署脚本 help 命令测试脚本
# ============================================================================

echo "=== 前端部署脚本 help 命令测试 ==="
echo

# 测试 --help 参数
echo "1. 测试 --help 参数:"
echo "命令: cd zentao-mcp-admin-web && bash deploy.sh --help"
echo "预期: 显示帮助信息，无错误信息"
echo "结果:"
cd zentao-mcp-admin-web
if bash deploy.sh --help 2>&1 | grep -q "ERROR"; then
    echo "❌ FAILED: 仍然显示错误信息"
    exit_code=1
else
    echo "✅ PASSED: 正常显示帮助信息"
    exit_code=0
fi
cd ..
echo

# 测试 -h 参数
echo "2. 测试 -h 参数:"
echo "命令: cd zentao-mcp-admin-web && bash deploy.sh -h"
echo "预期: 显示帮助信息，无错误信息"
echo "结果:"
cd zentao-mcp-admin-web
if bash deploy.sh -h 2>&1 | grep -q "ERROR"; then
    echo "❌ FAILED: 仍然显示错误信息"
    exit_code=1
else
    echo "✅ PASSED: 正常显示帮助信息"
    exit_code=0
fi
cd ..
echo

# 测试脚本退出码
echo "3. 测试脚本退出码:"
echo "命令: cd zentao-mcp-admin-web && bash deploy.sh --help; echo \$?"
echo "预期: 退出码为 0"
echo "结果:"
cd zentao-mcp-admin-web
bash deploy.sh --help >/dev/null 2>&1
actual_exit_code=$?
if [[ $actual_exit_code -eq 0 ]]; then
    echo "✅ PASSED: 退出码为 0"
else
    echo "❌ FAILED: 退出码为 $actual_exit_code"
    exit_code=1
fi
cd ..
echo

# 测试帮助内容完整性
echo "4. 测试帮助内容完整性:"
echo "预期: 包含用法、环境、动作、选项、示例等关键信息"
echo "结果:"
cd zentao-mcp-admin-web
help_output=$(bash deploy.sh --help 2>/dev/null)
if echo "$help_output" | grep -q "用法:" && \
   echo "$help_output" | grep -q "环境:" && \
   echo "$help_output" | grep -q "动作:" && \
   echo "$help_output" | grep -q "选项:" && \
   echo "$help_output" | grep -q "示例:"; then
    echo "✅ PASSED: 帮助内容完整"
else
    echo "❌ FAILED: 帮助内容不完整"
    exit_code=1
fi
cd ..
echo

echo "=== 测试完成 ==="
if [[ $exit_code -eq 0 ]]; then
    echo "🎉 所有测试通过！help 命令修复成功！"
else
    echo "❌ 部分测试失败，需要进一步检查"
fi

exit $exit_code
