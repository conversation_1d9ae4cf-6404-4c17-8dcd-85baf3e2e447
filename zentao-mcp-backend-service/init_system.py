#!/usr/bin/env python3
"""
系统初始化脚本
用于创建默认管理员账户和初始化数据库
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from app.core.init import initialize_system, get_system_status
from app.core.config import settings

def main():
    """主函数"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    print("=" * 60)
    print("Zentao MCP Backend Service - 系统初始化")
    print("=" * 60)
    
    # 显示配置信息
    print(f"\n📋 当前配置:")
    print(f"   数据库: {settings.database_url}")
    print(f"   管理员用户名: {settings.admin_username}")
    print(f"   管理员邮箱: {settings.admin_email}")
    
    # 检查环境变量
    env_file = project_root / ".env"
    if not env_file.exists():
        print(f"\n⚠️  未找到 .env 文件，将使用默认配置")
        print(f"   建议复制 .env.example 到 .env 并修改配置")
    
    print(f"\n🚀 开始系统初始化...")
    
    # 执行初始化
    if initialize_system():
        print(f"✅ 系统初始化完成！")
        
        # 显示系统状态
        status = get_system_status()
        print(f"\n📊 系统状态:")
        print(f"   初始化状态: {'✅ 已完成' if status['initialized'] else '❌ 未完成'}")
        print(f"   管理员用户数: {status.get('admin_users', 0)}")
        print(f"   API Key数量: {status.get('api_keys', 0)}")
        
        # 显示登录信息
        print(f"\n🔑 管理员登录信息:")
        print(f"   Web管理界面: http://localhost:3000")
        print(f"   用户名: {settings.admin_username}")
        print(f"   密码: {settings.admin_password}")
        print(f"   邮箱: {settings.admin_email}")
        
        print(f"\n⚠️  安全提醒:")
        print(f"   1. 请及时登录管理后台修改默认密码")
        print(f"   2. 生产环境请修改 .env 文件中的所有默认配置")
        print(f"   3. 确保数据库文件的访问权限设置正确")
        
        print(f"\n🎉 系统已准备就绪！")
        
    else:
        print(f"❌ 系统初始化失败！")
        print(f"   请检查日志信息并解决问题后重试")
        sys.exit(1)

if __name__ == "__main__":
    main()