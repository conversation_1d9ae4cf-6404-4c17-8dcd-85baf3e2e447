#!/usr/bin/env python3
"""
🚀 Zentao MCP Backend Service 快速启动脚本

这个脚本提供了多种启动选项，让您可以快速启动和测试服务。
"""

import asyncio
import subprocess
import sys
import os
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_python_executable():
    """
    Determines the correct Python executable to use.
    If a .venv directory exists, it uses the Python from the virtual environment.
    Otherwise, it falls back to the system's current Python executable.
    """
    project_root = Path(__file__).parent
    venv_python = project_root / ".venv" / "bin" / "python"
    if venv_python.exists():
        logger.info(f"使用虚拟环境中的Python: {venv_python}")
        return str(venv_python)
    
    logger.info(f"使用系统Python: {sys.executable}")
    return sys.executable

PYTHON_EXEC = get_python_executable()

def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    print(f"✅ Python版本: {sys.version}")
    
    # 检查虚拟环境
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 虚拟环境已激活")
    else:
        print("⚠️  建议在虚拟环境中运行")
    
    # 检查必要文件
    required_files = ['main.py', 'pyproject.toml', 'app/__init__.py']
    for file in required_files:
        if not Path(file).exists():
            print(f"❌ 缺少必要文件: {file}")
            return False
    print("✅ 必要文件检查通过")
    
    return True

def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖...")
    try:
        # 使用uv安装依赖
        result = subprocess.run(['uv', 'sync'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 依赖安装成功")
            return True
        else:
            print(f"❌ uv安装失败: {result.stderr}")
            # 尝试使用pip
            result = subprocess.run([PYTHON_EXEC, '-m', 'pip', 'install', '-e', '.'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 使用pip安装成功")
                return True
            else:
                print(f"❌ pip安装也失败: {result.stderr}")
                return False
    except FileNotFoundError:
        print("⚠️  uv未找到，尝试使用pip...")
        try:
            result = subprocess.run([PYTHON_EXEC, '-m', 'pip', 'install', '-e', '.'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ 使用pip安装成功")
                return True
            else:
                print(f"❌ pip安装失败: {result.stderr}")
                return False
        except Exception as e:
            print(f"❌ 安装依赖时出错: {e}")
            return False

def run_tests():
    """运行测试"""
    print("🧪 运行功能测试...")
    try:
        result = subprocess.run([PYTHON_EXEC, 'test_optimizations.py'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 所有测试通过")
            print(result.stdout)
            return True
        else:
            print("❌ 测试失败")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 运行测试时出错: {e}")
        return False

def start_server(port=8000, host="0.0.0.0"):
    """启动服务器"""
    print(f"🚀 启动服务器 (http://{host}:{port})...")
    try:
        # 使用uvicorn启动
        cmd = [
            PYTHON_EXEC, '-m', 'uvicorn', 
            'main:app', 
            '--host', host, 
            '--port', str(port),
            '--reload'
        ]
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")

def start_server_production(port=8000, host="0.0.0.0"):
    """启动生产环境服务器"""
    print(f"🏭 启动生产环境服务器 (http://{host}:{port})...")
    try:
        cmd = [
            PYTHON_EXEC, '-m', 'uvicorn', 
            'main:app', 
            '--host', host, 
            '--port', str(port),
            '--workers', '4'
        ]
        print(f"执行命令: {' '.join(cmd)}")
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except Exception as e:
        print(f"❌ 启动服务器时出错: {e}")

def show_usage_guide():
    """显示使用指南"""
    print("📚 运行使用指南...")
    try:
        subprocess.run([PYTHON_EXEC, 'USAGE_GUIDE.py'])
    except Exception as e:
        print(f"❌ 运行使用指南时出错: {e}")

def show_menu():
    """显示主菜单"""
    print("\n" + "="*60)
    print("🚀 Zentao MCP Backend Service 快速启动")
    print("="*60)
    print("请选择操作:")
    print("1. 🔍 检查环境")
    print("2. 📦 安装依赖")
    print("3. 🧪 运行测试")
    print("4. 🚀 启动开发服务器 (默认端口8000)")
    print("5. 🏭 启动生产服务器")
    print("6. 📚 查看使用指南")
    print("7. ⚡ 一键启动 (检查+安装+测试+启动)")
    print("8. 🛠️  自定义启动")
    print("0. 🚪 退出")
    print("="*60)

def one_click_start():
    """一键启动"""
    print("⚡ 一键启动流程...")
    
    if not check_environment():
        print("❌ 环境检查失败，请修复后重试")
        return False
    
    if not install_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        return False
    
    if not run_tests():
        print("⚠️  测试失败，但继续启动服务器")
    
    print("🚀 启动服务器...")
    start_server()
    return True

def custom_start():
    """自定义启动"""
    print("🛠️  自定义启动配置")
    
    try:
        host = input("请输入主机地址 (默认: 0.0.0.0): ").strip() or "0.0.0.0"
        port = input("请输入端口号 (默认: 8000): ").strip() or "8000"
        port = int(port)
        
        mode = input("选择模式 [dev/prod] (默认: dev): ").strip() or "dev"
        
        if mode.lower() == "prod":
            start_server_production(port, host)
        else:
            start_server(port, host)
            
    except ValueError:
        print("❌ 端口号必须是数字")
    except Exception as e:
        print(f"❌ 自定义启动失败: {e}")

def main():
    """主函数"""
    while True:
        show_menu()
        
        try:
            choice = input("\n请输入选项 (0-8): ").strip()
            
            if choice == "0":
                print("👋 再见！")
                break
            elif choice == "1":
                check_environment()
            elif choice == "2":
                install_dependencies()
            elif choice == "3":
                run_tests()
            elif choice == "4":
                start_server()
            elif choice == "5":
                start_server_production()
            elif choice == "6":
                show_usage_guide()
            elif choice == "7":
                one_click_start()
            elif choice == "8":
                custom_start()
            else:
                print("❌ 无效选项，请重新选择")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 操作失败: {e}")
        
        input("\n按回车键继续...")

if __name__ == "__main__":
    main()