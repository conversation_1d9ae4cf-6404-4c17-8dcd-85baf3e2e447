# 复制此文件为 .env 并根据需要修改配置


# 日志配置示例
# 日志级别 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO
# 日志文件路径 (留空则只输出到控制台)
LOG_FILE=logs/zentao_mcp.log
# 应用调试模式 (true/false)
DEBUG=false

# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp.db

# 管理员配置
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin123
ADMIN_EMAIL=<EMAIL>

# 禅道API配置
# 方式一：通过环境类型自动映射（推荐，二选一）
# 可选值：beta | preview | online
ZENTAO_ENV=online

# 方式二：显式指定完整URL（设置此项将覆盖上面的环境映射）
# 示例：ZENTAO_BASE_URL=http://your-zentao-server.com
