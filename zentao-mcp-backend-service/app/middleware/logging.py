"""
请求日志中间件
记录所有API请求的入参、出参和执行时间
"""
import time
import uuid
import json
from typing import Callable, Any
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp

from app.core.logging import api_logger


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """请求日志中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录日志"""
        
        # 生成请求ID
        request_id = str(uuid.uuid4())
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取请求信息
        method = request.method
        url = str(request.url)
        path = request.url.path
        query_params = dict(request.query_params)
        headers = dict(request.headers)
        # 为避免日志过大，限制Headers数量与长度
        headers = {k: (v if len(v) <= 1024 else v[:1024] + "...") for k, v in list(headers.items())[:100]}
        
        # 读取请求体
        request_body = None
        if method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if body:
                    # 尝试解析JSON
                    try:
                        request_body = json.loads(body.decode())
                    except (json.JSONDecodeError, UnicodeDecodeError):
                        request_body = body.decode('utf-8', errors='ignore')
                
                # 重新构造request以便后续处理
                async def receive():
                    return {"type": "http.request", "body": body}
                
                request._receive = receive
            except Exception as e:
                api_logger.warning(
                    "Failed to read request body",
                    extra={
                        "request_id": request_id,
                        "endpoint": path,
                        "method": method,
                        "error": str(e)
                    }
                )
        
        # 过滤敏感信息
        filtered_headers = self._filter_sensitive_data(headers)
        filtered_body = self._filter_sensitive_data(request_body) if request_body else None
        # 控制台简洁输出由 logging 配置负责；这里统一投给api_logger（文件为JSON）
        
        # 记录请求开始日志
        api_logger.info(
            f"Request started: {method} {path}",
            extra={
                "request_id": request_id,
                "endpoint": path,
                "method": method,
                "query_params": query_params,
                "headers": filtered_headers,
                "request_data": filtered_body,
                "client_ip": self._get_client_ip(request)
            }
        )
        
        # 将request_id添加到request state中，供其他地方使用
        request.state.request_id = request_id
        
        try:
            # 处理请求
            response = await call_next(request)
            
            # 计算处理时间
            duration = (time.time() - start_time) * 1000  # 转换为毫秒
            
            # 获取响应信息
            status_code = response.status_code

            # 响应头（限数量与长度）
            response_headers = dict(response.headers)
            response_headers = {k: (v if isinstance(v, str) and len(v) <= 1024 else (str(v)[:1024] + "..."))
                                for k, v in list(response_headers.items())[:100]}
            
            # 尝试获取响应体（针对JSONResponse）
            response_data = None
            try:
                # 检查是否是JSONResponse且有body属性
                if isinstance(response, JSONResponse) and hasattr(response, 'body'):
                    response_body = response.body
                    if response_body:
                        response_data = json.loads(response_body.decode())
                        response_data = self._filter_sensitive_data(response_data)
                # 对于其他类型的响应，尝试从headers判断content-type
                elif response.headers.get("content-type", "").startswith("application/json"):
                    # 对于流式响应，我们不强制读取body以避免消费响应流
                    # 这里只记录响应类型信息
                    response_data = {"_response_type": "json", "_status": "success"}
            except Exception as e:
                # 记录响应体读取失败，但不影响正常流程
                api_logger.debug(
                    f"Failed to read response body for logging: {str(e)}",
                    extra={"request_id": request_id}
                )
            
            # 记录请求完成日志
            log_level = "info" if status_code < 400 else "warning" if status_code < 500 else "error"
            getattr(api_logger, log_level)(
                f"Request completed: {method} {path} - {status_code}",
                extra={
                    "request_id": request_id,
                    "endpoint": path,
                    "method": method,
                    "status_code": status_code,
                    "duration": round(duration, 2),
                    "response_headers": self._filter_sensitive_data(response_headers),
                    "response_data": response_data
                }
            )
            
            # 添加请求ID到响应头
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # 计算处理时间
            duration = (time.time() - start_time) * 1000
            
            # 记录异常日志
            api_logger.error(
                f"Request failed: {method} {path}",
                extra={
                    "request_id": request_id,
                    "endpoint": path,
                    "method": method,
                    "duration": round(duration, 2),
                    "error": str(e),
                    "error_type": type(e).__name__
                },
                exc_info=True
            )
            
            # 重新抛出异常，让异常处理中间件处理
            raise
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("X-Forwarded-For")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("X-Real-IP")
        if real_ip:
            return real_ip
        
        # 返回直连IP
        if request.client:
            return request.client.host
        
        return "unknown"
    
    def _filter_sensitive_data(self, data: Any) -> Any:
        """过滤敏感数据"""
        if not data:
            return data

def register_request_logging(app: ASGIApp) -> None:
    """
    便捷挂载：在应用启动处调用以启用请求日志中间件
    - 控制台：简洁文本
    - 文件（若配置）：JSON结构化，包含 headers/body（已脱敏）
    """
    try:
        # FastAPI/Starlette 统一挂载
        app.add_middleware(RequestLoggingMiddleware)
    except Exception:
        # 兜底处理
        app.add_middleware(RequestLoggingMiddleware)
        
        sensitive_keys = {
            "password", "passwd", "pwd", "secret", "token", "key", 
            "authorization", "auth", "credential", "private"
        }
        
        if isinstance(data, dict):
            filtered = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in sensitive_keys):
                    filtered[key] = "***FILTERED***"
                elif isinstance(value, (dict, list)):
                    filtered[key] = self._filter_sensitive_data(value)
                else:
                    filtered[key] = value
            return filtered
        elif isinstance(data, list):
            return [self._filter_sensitive_data(item) for item in data]
        else:
            return data