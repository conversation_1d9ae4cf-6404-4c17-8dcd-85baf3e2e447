"""
异常处理中间件
统一处理和记录应用异常
"""
import traceback
from typing import Union
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

from app.core.logging import api_logger


class ExceptionHandlingMiddleware(BaseHTTPMiddleware):
    """异常处理中间件"""
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """处理请求并捕获异常"""
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            return await self._handle_exception(request, e)
    
    async def _handle_exception(self, request: Request, exc: Exception) -> JSONResponse:
        """处理异常并返回适当的响应"""
        
        # 获取请求ID（如果存在）
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        # 获取请求信息
        method = request.method
        path = request.url.path
        
        # 根据异常类型处理
        if isinstance(exc, HTTPException):
            # HTTP异常
            status_code = exc.status_code
            detail = exc.detail
            
            # 记录日志
            if status_code >= 500:
                api_logger.error(
                    f"HTTP Exception: {status_code} - {detail}",
                    extra={
                        "request_id": request_id,
                        "endpoint": path,
                        "method": method,
                        "status_code": status_code,
                        "error": detail,
                        "error_type": "HTTPException"
                    }
                )
            else:
                api_logger.warning(
                    f"HTTP Exception: {status_code} - {detail}",
                    extra={
                        "request_id": request_id,
                        "endpoint": path,
                        "method": method,
                        "status_code": status_code,
                        "error": detail,
                        "error_type": "HTTPException"
                    }
                )
            
            return JSONResponse(
                status_code=status_code,
                content={
                    "error": True,
                    "message": detail,
                    "request_id": request_id,
                    "error_type": "HTTPException"
                }
            )
        
        elif isinstance(exc, ValidationError):
            # Pydantic验证异常
            status_code = 422
            detail = "Validation Error"
            errors = exc.errors()
            
            api_logger.warning(
                f"Validation Error: {str(exc)}",
                extra={
                    "request_id": request_id,
                    "endpoint": path,
                    "method": method,
                    "status_code": status_code,
                    "error": str(exc),
                    "error_type": "ValidationError",
                    "validation_errors": errors
                }
            )
            
            return JSONResponse(
                status_code=status_code,
                content={
                    "error": True,
                    "message": detail,
                    "details": errors,
                    "request_id": request_id,
                    "error_type": "ValidationError"
                }
            )
        
        elif isinstance(exc, SQLAlchemyError):
            # 数据库异常
            status_code = 500
            detail = "Database Error"
            
            api_logger.error(
                f"Database Error: {str(exc)}",
                extra={
                    "request_id": request_id,
                    "endpoint": path,
                    "method": method,
                    "status_code": status_code,
                    "error": str(exc),
                    "error_type": "SQLAlchemyError",
                    "traceback": traceback.format_exc()
                },
                exc_info=True
            )
            
            return JSONResponse(
                status_code=status_code,
                content={
                    "error": True,
                    "message": detail,
                    "request_id": request_id,
                    "error_type": "DatabaseError"
                }
            )
        
        else:
            # 其他未预期的异常
            status_code = 500
            detail = "Internal Server Error"
            
            api_logger.error(
                f"Unhandled Exception: {type(exc).__name__} - {str(exc)}",
                extra={
                    "request_id": request_id,
                    "endpoint": path,
                    "method": method,
                    "status_code": status_code,
                    "error": str(exc),
                    "error_type": type(exc).__name__,
                    "traceback": traceback.format_exc()
                },
                exc_info=True
            )
            
            return JSONResponse(
                status_code=status_code,
                content={
                    "error": True,
                    "message": detail,
                    "request_id": request_id,
                    "error_type": "InternalServerError"
                }
            )


def setup_exception_handlers(app):
    """设置全局异常处理器"""
    
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """HTTP异常处理器"""
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        api_logger.warning(
            f"HTTP Exception: {exc.status_code} - {exc.detail}",
            extra={
                "request_id": request_id,
                "endpoint": request.url.path,
                "method": request.method,
                "status_code": exc.status_code,
                "error": exc.detail,
                "error_type": "HTTPException"
            }
        )
        
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": True,
                "message": exc.detail,
                "request_id": request_id,
                "error_type": "HTTPException"
            }
        )
    
    @app.exception_handler(ValidationError)
    async def validation_exception_handler(request: Request, exc: ValidationError):
        """验证异常处理器"""
        request_id = getattr(request.state, 'request_id', 'unknown')
        
        api_logger.warning(
            f"Validation Error: {str(exc)}",
            extra={
                "request_id": request_id,
                "endpoint": request.url.path,
                "method": request.method,
                "status_code": 422,
                "error": str(exc),
                "error_type": "ValidationError",
                "validation_errors": exc.errors()
            }
        )
        
        return JSONResponse(
            status_code=422,
            content={
                "error": True,
                "message": "Validation Error",
                "details": exc.errors(),
                "request_id": request_id,
                "error_type": "ValidationError"
            }
        )