"""
API Key Pydantic schemas
"""
from pydantic import BaseModel
from datetime import datetime
from typing import Optional


class APIKeyBase(BaseModel):
    """Base API Key schema"""
    name: str


class APIKeyCreate(APIKeyBase):
    """Schema for creating API Key"""
    pass


class APIKeyResponse(APIKeyBase):
    """Schema for API Key response"""
    id: int
    key_hash: str
    is_active: bool
    created_at: datetime
    revoked_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class APIKeyWithPlainKey(APIKeyResponse):
    """Schema for API Key response with plain key (only for creation)"""
    plain_key: str