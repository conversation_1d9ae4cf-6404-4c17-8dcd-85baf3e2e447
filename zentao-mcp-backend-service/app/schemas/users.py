"""
用户管理相关的数据模型
"""
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List
from datetime import datetime
from app.models import UserType


class UserCreateRequest(BaseModel):
    """创建用户请求"""
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=8, max_length=128, description="密码")
    email: EmailStr = Field(..., description="邮箱")
    user_type: UserType = Field(UserType.USER, description="用户类型")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")


class UserUpdateRequest(BaseModel):
    """更新用户请求"""
    email: Optional[EmailStr] = Field(None, description="邮箱")
    phone: Optional[str] = Field(None, max_length=20, description="电话号码")
    is_active: Optional[bool] = Field(None, description="是否活跃")
    user_type: Optional[UserType] = Field(None, description="用户类型")


class PasswordResetRequest(BaseModel):
    """密码重置请求"""
    new_password: Optional[str] = Field(None, min_length=8, max_length=128, description="新密码，为空则生成临时密码")
    force_change: bool = Field(True, description="是否强制用户下次登录时修改密码")


class UserResponse(BaseModel):
    """用户响应"""
    id: int
    username: str
    email: Optional[str]
    user_type: UserType
    user_type_display: str
    phone: Optional[str]
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime]
    last_password_change: Optional[datetime]
    failed_login_attempts: Optional[int]
    locked_until: Optional[datetime]
    
    class Config:
        from_attributes = True
    
    @classmethod
    def from_orm(cls, user):
        """从ORM对象创建响应对象"""
        return cls(
            id=user.id,
            username=user.username,
            email=user.email,
            user_type=user.user_type,
            user_type_display=user.get_user_type_display(),
            phone=user.phone,
            is_active=user.is_active,
            created_at=user.created_at,
            last_login=user.last_login,
            last_password_change=user.last_password_change,
            failed_login_attempts=user.failed_login_attempts,
            locked_until=user.locked_until
        )


class UserListResponse(BaseModel):
    """用户列表响应"""
    users: List[UserResponse]
    total: int
    skip: int
    limit: int