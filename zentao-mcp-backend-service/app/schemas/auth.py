"""
认证相关的数据模型
"""
from pydantic import BaseModel, EmailStr, Field
from typing import Optional
from datetime import datetime


class LoginRequest(BaseModel):
    """登录请求"""
    username: str = Field(..., min_length=1, max_length=50, description="用户名")
    password: str = Field(..., min_length=1, max_length=128, description="密码")
    remember_me: Optional[bool] = Field(False, description="记住登录状态")


class LoginResponse(BaseModel):
    """登录响应"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")
    user_id: int = Field(..., description="用户ID")
    username: str = Field(..., description="用户名")
    user_type: str = Field(..., description="用户类型")
    expires_at: datetime = Field(..., description="过期时间")


class TokenResponse(BaseModel):
    """令牌响应"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("bearer", description="令牌类型")


class ChangePasswordRequest(BaseModel):
    """修改密码请求"""
    old_password: str = Field(..., min_length=1, max_length=128, description="旧密码")
    new_password: str = Field(..., min_length=8, max_length=128, description="新密码")


class UserInfoResponse(BaseModel):
    """用户信息响应"""
    id: int
    username: str
    email: Optional[str]
    user_type: str
    user_type_display: str
    phone: Optional[str]
    is_active: bool
    created_at: datetime
    last_login: Optional[datetime]
    last_password_change: Optional[datetime]
    is_locked: bool


class SessionInfo(BaseModel):
    """会话信息"""
    id: int
    ip_address: Optional[str]
    user_agent: Optional[str]
    created_at: datetime
    last_activity: datetime
    expires_at: datetime
    is_current: bool


class SessionListResponse(BaseModel):
    """会话列表响应"""
    sessions: list[SessionInfo]