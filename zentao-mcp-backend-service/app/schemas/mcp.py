"""
MCP request and response schemas
"""
from pydantic import BaseModel
from typing import Any, Dict, Literal, Optional


class MCPRequest(BaseModel):
    """MCP request schema"""
    type: Literal["tool_call", "resource_access"]
    name: str
    arguments: Optional[Dict[str, Any]] = {}


class MCPResponse(BaseModel):
    """MCP response schema"""
    status: Literal["success", "error"]
    data: Optional[Any] = None
    error: Optional[str] = None