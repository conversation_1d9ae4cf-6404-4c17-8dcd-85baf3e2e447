"""
API Key管理相关的数据模型
"""
from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class APIKeyCreateRequest(BaseModel):
    """创建API Key请求"""
    name: str = Field(..., min_length=1, max_length=100, description="API Key名称")
    description: Optional[str] = Field(None, max_length=500, description="描述")
    permissions: Optional[List[str]] = Field(None, description="权限列表")
    expires_days: Optional[int] = Field(None, ge=1, le=3650, description="过期天数")
    user_id: Optional[int] = Field(None, description="用户ID，为空则为当前用户")


class APIKeyUpdateRequest(BaseModel):
    """更新API Key请求"""
    name: Optional[str] = Field(None, min_length=1, max_length=100, description="API Key名称")
    description: Optional[str] = Field(None, max_length=500, description="描述")
    permissions: Optional[List[str]] = Field(None, description="权限列表")
    is_active: Optional[bool] = Field(None, description="是否活跃")


class APIKeyResponse(BaseModel):
    """API Key响应"""
    id: int
    name: str
    description: Optional[str]
    permissions: List[str]
    is_active: bool
    created_at: datetime
    expires_at: Optional[datetime]
    last_used_at: Optional[datetime]
    user_id: int
    username: Optional[str]
    key_value: Optional[str] = Field(None, description="只在创建时返回完整Key值")
    
    class Config:
        from_attributes = True
    
    @classmethod
    def from_orm(cls, api_key, show_key: bool = False):
        """从ORM对象创建响应对象"""
        return cls(
            id=api_key.id,
            name=api_key.name,
            description=api_key.description,
            permissions=api_key.permissions or [],
            is_active=api_key.is_active,
            created_at=api_key.created_at,
            expires_at=api_key.expires_at,
            last_used_at=api_key.last_used_at,
            user_id=api_key.user_id,
            username=api_key.user.username if api_key.user else None,
            key_value=api_key.key_value if show_key else f"{api_key.key_value[:8]}...{api_key.key_value[-4:]}"
        )


class APIKeyListResponse(BaseModel):
    """API Key列表响应"""
    api_keys: List[APIKeyResponse]
    total: int
    skip: int
    limit: int


class CreateApiKeyResponse(BaseModel):
    """创建API Key时返回的响应，包含完整的key"""
    id: str
    name: str
    api_key: str