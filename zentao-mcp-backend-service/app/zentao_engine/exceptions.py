"""
Zentao引擎异常定义 - 精简版
"""

class ZentaoEngineError(Exception):
    """Zentao引擎基础异常"""
    def __init__(self, message: str, error_code: str = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code


class ZentaoAPIError(ZentaoEngineError):
    """Zentao API调用异常"""
    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        super().__init__(message, "ZENTAO_API_ERROR")
        self.status_code = status_code
        self.response_data = response_data or {}


class NetworkError(ZentaoEngineError):
    """网络连接异常"""
    def __init__(self, message: str):
        super().__init__(message, "NETWORK_ERROR")


class TimeoutError(ZentaoEngineError):
    """请求超时异常"""
    def __init__(self, message: str):
        super().__init__(message, "TIMEOUT_ERROR")