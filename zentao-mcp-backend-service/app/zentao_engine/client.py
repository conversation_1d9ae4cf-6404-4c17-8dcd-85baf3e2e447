"""
完整的Zentao客户端服务 - 基于原项目优化
"""

import httpx
import logging
import json
import asyncio
import time
from typing import Dict, Any, Optional, List, Union
from functools import lru_cache

from app.core.config import settings
from .cache import get_cache, cache_key_hash
from .exceptions import ZentaoAPIError, NetworkError, TimeoutError

logger = logging.getLogger(__name__)


class ZentaoService:
    """完整的Zentao服务客户端"""

    def __init__(self):
        #  如果settings.zentao_base_url 为空就抛出异常
        if not settings.zentao_base_url:
            raise ValueError("请设置 Zentao 的 Base URL")
        self.base_url = settings.zentao_base_url
        self.cache = get_cache()
        self._client = None
        self.request_counter = 0

        # 并发控制
        self._semaphore = asyncio.Semaphore(10)  # 最大并发请求数

        logger.info(f"初始化Zentao服务客户端，域名: {self.base_url}")

    async def _get_client(self) -> httpx.AsyncClient:
        """获取HTTP客户端"""
        if self._client is None:
            timeout = httpx.Timeout(
                connect=10.0,
                read=30.0,
                write=10.0,
                pool=5.0
            )

            limits = httpx.Limits(
                max_connections=20,
                max_keepalive_connections=10,
                keepalive_expiry=30.0
            )

            self._client = httpx.AsyncClient(
                base_url=self.base_url,
                timeout=timeout,
                limits=limits,
                trust_env=True,
                http2=False,
                follow_redirects=True
            )
        return self._client

    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None,
                            retries: int = 3, use_cache: bool = True) -> Dict[str, Any]:
        """发送HTTP请求，支持重试和缓存"""

        # 生成缓存键
        cache_key = None
        if use_cache and method.upper() == "GET":
            cache_key = cache_key_hash(method, endpoint, data or {})
            cached_result = await self.cache.get(cache_key)
            if cached_result:
                logger.debug(f"缓存命中: {endpoint}")
                return cached_result

        # 生成请求ID
        self.request_counter += 1
        request_id = f"req_{self.request_counter}"

        # 使用信号量控制并发
        async with self._semaphore:
            last_exception = None

            for attempt in range(retries + 1):
                try:
                    client = await self._get_client()

                    # 记录请求信息
                    logger.debug(f"[{request_id}] {method} {endpoint} (尝试 {attempt + 1})")

                    start_time = time.time()

                    if method.upper() == "GET":
                        response = await client.get(endpoint)
                    elif method.upper() == "POST":
                        response = await client.post(endpoint, json=data)
                    else:
                        raise ValueError(f"不支持的HTTP方法: {method}")

                    response.raise_for_status()

                    # 记录请求性能
                    request_duration = time.time() - start_time
                    if request_duration > 2.0:
                        logger.warning(f"慢请求: {endpoint}, 耗时: {request_duration:.2f}s")

                    # 解析响应
                    try:
                        result = response.json()
                    except ValueError as e:
                        logger.warning(f"响应不是有效的JSON格式: {response.text[:200]}")
                        raise ZentaoAPIError(f"API响应格式错误: {e}")

                    # 检查API响应中的错误信息
                    if isinstance(result, dict):
                        if result.get('status') == 'error' or result.get('error'):
                            error_msg = result.get('message', result.get('error', '未知错误'))
                            raise ZentaoAPIError(f"Zentao API返回错误: {error_msg}")

                    # 缓存结果
                    if use_cache and cache_key and method.upper() == "GET":
                        await self.cache.set(cache_key, result, ttl=300)  # 缓存5分钟

                    logger.debug(f"[{request_id}] 请求成功，耗时: {request_duration:.3f}s")
                    return result

                except httpx.TimeoutException as e:
                    last_exception = TimeoutError(f"请求超时: {str(e)}")
                    logger.warning(f"[{request_id}] 请求超时 (尝试 {attempt + 1}/{retries + 1})")

                except httpx.NetworkError as e:
                    last_exception = NetworkError(f"网络连接失败: {str(e)}")
                    logger.warning(f"[{request_id}] 网络连接失败 (尝试 {attempt + 1}/{retries + 1})")

                except httpx.HTTPStatusError as e:
                    error_msg = f"HTTP错误 {e.response.status_code}: {str(e)}"

                    # 对于4xx错误，不重试
                    if 400 <= e.response.status_code < 500:
                        logger.error(f"[{request_id}] {error_msg}")
                        raise ZentaoAPIError(error_msg)

                    last_exception = ZentaoAPIError(error_msg)
                    logger.warning(f"[{request_id}] HTTP错误 (尝试 {attempt + 1}/{retries + 1})")

                # 如果不是最后一次尝试，等待后重试
                if attempt < retries:
                    wait_time = min(1.0 * (2 ** attempt), 10.0)  # 指数退避，最大10秒
                    await asyncio.sleep(wait_time)

            # 所有重试都失败了
            logger.error(f"[{request_id}] 请求失败，已重试 {retries} 次: {str(last_exception)}")
            raise last_exception

    async def close(self):
        """关闭HTTP客户端"""
        if self._client:
            await self._client.aclose()
            logger.info("Zentao服务客户端已关闭")

    # 部门相关API
    async def get_all_departments(self) -> Dict[str, Any]:
        """获取所有部门"""
        try:
            return await self._make_request("GET", "/apiData/getAllDept")
        except Exception as e:
            logger.error(f"获取部门列表失败: {e}")
            raise

    # 项目相关API
    async def get_all_projects(self) -> Dict[str, Any]:
        """获取所有项目"""
        try:
            return await self._make_request("GET", "/apiData/getAllProject")
        except Exception as e:
            logger.error(f"获取项目列表失败: {e}")
            raise

    async def get_tasks_by_project(self, project_id: int) -> Dict[str, Any]:
        """根据项目ID查询任务列表"""
        try:
            data = {"detailId": project_id}
            return await self._make_request("POST", "/api/getTaskByProject", data)
        except Exception as e:
            logger.error(f"获取项目任务失败: {e}")
            raise

    async def get_stories_by_project(self, project_id: int) -> Dict[str, Any]:
        """根据项目ID查询需求列表"""
        try:
            data = {"detailId": project_id}
            return await self._make_request("POST", "/api/getStoryByProjectid", data)
        except Exception as e:
            logger.error(f"获取项目需求失败: {e}")
            raise

    async def get_bugs_by_project(self, project_id: int) -> Dict[str, Any]:
        """根据项目ID查询Bug列表"""
        try:
            data = {"detailId": project_id}
            return await self._make_request("POST", "/api/getBugByProject", data)
        except Exception as e:
            logger.error(f"获取项目Bug失败: {e}")
            raise

    # Bug相关API
    async def get_bugs_by_time_range(self, start_date: str, end_date: str) -> Dict[str, Any]:
        """用时间段查询bug列表"""
        try:
            data = {
                "startDate": start_date,
                "endDate": end_date
            }
            return await self._make_request("POST", "/apiData/getBugListByTimeRange", data)
        except Exception as e:
            logger.error(f"根据时间范围获取Bug列表失败: {e}")
            raise

    async def get_bugs_by_time_range_and_dept(self, start_date: str, end_date: str, dept_id: int) -> Dict[str, Any]:
        """根据时间段和部门查询bug"""
        try:
            data = {
                "startDate": start_date,
                "endDate": end_date,
                "deptId": dept_id
            }
            return await self._make_request("POST", "/apiData/getBugListByTimeRangeAndDeptId", data)
        except Exception as e:
            logger.error(f"根据时间段和部门获取Bug列表失败: {e}")
            raise

    async def get_bug_detail(self, bug_id: int) -> Dict[str, Any]:
        """根据BUGID查询BUG详情"""
        try:
            data = {"detailId": bug_id}
            return await self._make_request("POST", "/api/getBugDetail", data)
        except Exception as e:
            logger.error(f"获取Bug详情失败: {e}")
            raise

    # 需求相关API
    async def get_story_info(self, story_ids: List[str]) -> Dict[str, Any]:
        """获取产品需求工时"""
        try:
            data = {"story": story_ids}
            return await self._make_request("POST", "/api/getStory", data)
        except Exception as e:
            logger.error(f"获取需求信息失败: {e}")
            raise

    async def get_stories_end_info(self, story_ids: List[str]) -> Dict[str, Any]:
        """获取需求任务工时（已完成）"""
        try:
            data = {"story": story_ids}
            return await self._make_request("POST", "/api/getStoryEnd", data)
        except Exception as e:
            logger.error(f"获取需求完成信息失败: {e}")
            raise

    async def check_stories_exist(self, story_ids: List[str]) -> Dict[str, Any]:
        """查询需求号是否存在"""
        try:
            data = {"story": story_ids}
            return await self._make_request("POST", "/api/getStoryId", data)
        except Exception as e:
            logger.error(f"检查需求是否存在失败: {e}")
            raise

    async def get_story_by_id(self, story_id: int) -> Dict[str, Any]:
        """根据需求ID查询需求详情"""
        try:
            data = {"detailId": story_id}
            return await self._make_request("POST", "/api/getStoryDetail", data)
        except Exception as e:
            logger.error(f"获取需求详情失败: {e}")
            raise

    async def get_stories_by_time(self, status: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """根据时间段查询需求"""
        try:
            data = {
                "status": status,
                "start": start_date,
                "end": end_date
            }
            return await self._make_request("POST", "/api/getStorysByTime", data)
        except Exception as e:
            logger.error(f"根据时间段查询需求失败: {e}")
            raise

    # 任务相关API
    async def get_task_by_id(self, task_id: int) -> Dict[str, Any]:
        """根据任务ID查询任务详情"""
        try:
            data = {"detailId": task_id}
            return await self._make_request("POST", "/api/getTaskById", data)
        except Exception as e:
            logger.error(f"获取任务详情失败: {e}")
            raise

    async def get_tasks_by_account(self, account: str, start_date: str, end_date: str, is_doing: bool = False) -> Dict[
        str, Any]:
        """根据域账号查询任务"""
        try:
            data = {
                "account": account,
                "start": start_date,
                "end": end_date,
                "isdo": 1 if is_doing else 0
            }
            return await self._make_request("POST", "/api/getTaskByAccount", data)
        except Exception as e:
            logger.error(f"根据账号查询任务失败: {e}")
            raise

    async def get_tasks_by_dept(self, dept_id: int, start_date: str, end_date: str, is_doing: bool = False) -> Dict[
        str, Any]:
        """根据部门查询任务"""
        try:
            data = {
                "deptId": dept_id,
                "start": start_date,
                "end": end_date,
                "isdo": 1 if is_doing else 0
            }
            return await self._make_request("POST", "/api/getTaskByDept", data)
        except Exception as e:
            logger.error(f"根据部门查询任务失败: {e}")
            raise

    # 用户相关API
    async def get_users_by_department(self, dept_id: int) -> Dict[str, Any]:
        """根据部门ID查询用户列表"""
        try:
            data = {"deptId": dept_id}
            return await self._make_request("POST", "/api/getUserByDept", data)
        except Exception as e:
            logger.error(f"根据部门获取用户列表失败: {e}")
            raise

    async def get_users_by_accounts(self, accounts: List[str]) -> Dict[str, Any]:
        """根据域账号批量查询用户信息"""
        try:
            return await self._make_request("POST", "/api/getUserByAccount", accounts)
        except Exception as e:
            logger.error(f"批量查询用户信息失败: {e}")
            raise

    async def get_personal_bugs(self, account: str, status: str, start_date: str, end_date: str) -> Dict[str, Any]:
        """查询个人Bug"""
        try:
            data = {
                "account": account,
                "status": status,
                "start": start_date,
                "end": end_date
            }
            return await self._make_request("POST", "/api/getPersonalBugs", data)
        except Exception as e:
            logger.error(f"查询个人Bug失败: {e}")
            raise

    ##=============================== 下面是扩展增强方法

    # 批量请求支持
    async def batch_request(self, requests: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量处理请求，提高并发性能"""
        if not requests:
            return []

        logger.info(f"开始批量处理 {len(requests)} 个请求")

        # 创建并发任务
        tasks = []
        for i, req in enumerate(requests):
            method = req.get('method', 'GET')
            endpoint = req.get('endpoint', '')
            data = req.get('data')
            use_cache = req.get('use_cache', True)

            task = asyncio.create_task(
                self._make_request(method, endpoint, data, use_cache=use_cache),
                name=f"batch_request_{i}"
            )
            tasks.append(task)

        # 等待所有任务完成
        try:
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # 处理异常结果
            processed_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    logger.error(f"批量请求 {i} 失败: {result}")
                    processed_results.append({
                        "error": str(result),
                        "request_index": i
                    })
                else:
                    processed_results.append(result)

            success_count = len([r for r in results if not isinstance(r, Exception)])
            failure_count = len([r for r in results if isinstance(r, Exception)])
            logger.info(f"批量请求完成，成功: {success_count}, 失败: {failure_count}")

            return processed_results

        except Exception as e:
            logger.error(f"批量请求处理失败: {e}")
            raise

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取客户端性能统计"""
        return {
            "total_requests": self.request_counter,
            "base_url": self.base_url,
            "concurrent_limit": 10,
            "cache_enabled": True
        }


@lru_cache(maxsize=1)
def get_zentao_service() -> ZentaoService:
    """获取Zentao服务实例（单例模式）"""
    return ZentaoService()
