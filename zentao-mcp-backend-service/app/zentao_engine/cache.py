"""
简化的缓存系统模块 - 用于后端服务
"""

import time
import logging
import hashlib
import asyncio
from typing import Any, Dict, Optional, List, Callable
from collections import OrderedDict

logger = logging.getLogger(__name__)


class SimpleCache:
    """简化的异步内存缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 300):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict[str, Dict[str, Any]] = OrderedDict()
        self._lock = asyncio.Lock()
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        async with self._lock:
            if key not in self._cache:
                return None
            
            item = self._cache[key]
            
            # 检查是否过期
            if item['ttl'] > 0 and time.time() - item['created_at'] > item['ttl']:
                del self._cache[key]
                return None
            
            # 移到末尾（LRU）
            self._cache.move_to_end(key)
            return item['value']
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        async with self._lock:
            # 如果缓存满了，删除最旧的项
            if len(self._cache) >= self.max_size and key not in self._cache:
                self._cache.popitem(last=False)
            
            self._cache[key] = {
                'value': value,
                'created_at': time.time(),
                'ttl': ttl
            }
            self._cache.move_to_end(key)
    
    async def delete(self, key: str) -> bool:
        """删除缓存项"""
        async with self._lock:
            if key in self._cache:
                del self._cache[key]
                return True
            return False
    
    async def clear(self) -> None:
        """清空缓存"""
        async with self._lock:
            self._cache.clear()


# 全局缓存实例
_global_cache: Optional[SimpleCache] = None


def get_cache() -> SimpleCache:
    """获取全局缓存实例"""
    global _global_cache
    
    if _global_cache is None:
        _global_cache = SimpleCache()
    
    return _global_cache


def cache_key(*args, **kwargs) -> str:
    """生成缓存键"""
    key_parts = []
    
    for arg in args:
        if isinstance(arg, (list, tuple)):
            key_parts.append(f"[{','.join(str(x) for x in arg)}]")
        elif isinstance(arg, dict):
            sorted_items = sorted(arg.items())
            key_parts.append(f"{{{','.join(f'{k}:{v}' for k, v in sorted_items)}}}")
        else:
            key_parts.append(str(arg))
    
    if kwargs:
        sorted_kwargs = sorted(kwargs.items())
        kwargs_str = ','.join(f"{k}={v}" for k, v in sorted_kwargs)
        key_parts.append(f"({kwargs_str})")
    
    return ":".join(key_parts)


def cache_key_hash(*args, **kwargs) -> str:
    """生成缓存键的哈希版本"""
    key = cache_key(*args, **kwargs)
    if len(key) > 200:
        return hashlib.md5(key.encode('utf-8')).hexdigest()
    return key