"""
API Key认证和安全相关功能
"""

import hashlib
from fastapi import HTTPException, Depends, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.crud.api_key import get_api_key_by_hash, get_api_key_by_value, hash_api_key
from app.core.logging import auth_logger
from app.core.config import settings
from typing import Optional

security = HTTPBearer()


def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security), 
                  db: Session = Depends(get_db)) -> str:
    """
    验证API Key的FastAPI依赖项
    
    Args:
        credentials: HTTP Bearer认证凭据
        db: 数据库会话
        
    Returns:
        str: 用户标识符
        
    Raises:
        HTTPException: 认证失败时抛出401或403错误
    """
    if not credentials:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Missing API Key"
        )
    
    api_key = credentials.credentials
    if not api_key:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid API Key format"
        )
    
    # 计算API Key的哈希值
    key_hash = hash_api_key(api_key)
    masked = (api_key[:4] + "***" + api_key[-4:]) if len(api_key) >= 8 else "***"
    # 尝试hash命中
    db_api_key = get_api_key_by_hash(db, key_hash)
    hit = "hash" if db_api_key else None
    # 兼容历史脏数据：若查不到，按明文回退一次
    if not db_api_key:
        db_api_key = get_api_key_by_value(db, api_key)
        if db_api_key:
            hit = "plain"
    # 记录鉴权路径（脱敏）
    auth_logger.info(
        "API key verify",
        extra={
            "method": "bearer",
            "masked": masked,
            "hash_prefix": key_hash[:8],
            "hit": hit or "none"
        }
    )
    if not db_api_key:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid API Key"
        )
    
    if not db_api_key.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="API Key has been revoked"
        )
    
    return db_api_key.user_identifier
