"""
Configuration settings for Zentao MCP Backend Service
"""
from pydantic_settings import BaseSettings
from pydantic import ConfigDict, model_validator
from typing import Optional
import os
from pathlib import Path


class Settings(BaseSettings):
    """Application settings"""
    
    # Database
    database_url: str = "sqlite:///./data/zentao_mcp.db"
    
    # Admin Configuration
    admin_username: str = "admin"
    admin_password: str = "admin123"
    admin_email: str = "<EMAIL>"
    
    
    # Zentao API settings
    # 优先级：ZENTAO_BASE_URL > ZENTAO_ENV 映射 > 默认值
    zentao_env: Optional[str] = None  # 可选: beta | preview | online
    zentao_base_url: Optional[str] = None

    @model_validator(mode="after")
    def _apply_zentao_env_mapping(self) -> "Settings":
        """
        启动时根据 ZENTAO_ENV 自动映射 zentao_base_url。
        若 .env 显式提供 ZENTAO_BASE_URL，则优先生效。
        """
        if (not self.zentao_base_url) and self.zentao_env:
            env = str(self.zentao_env).strip().lower()
            mapping = {
                "beta": "http://newzentao-api.beta1.fn/",
                "preview": "http://newzentao-api.idc1.fn/",
                "online": "http://newzentao-api.idc1.fn/",
            }
            mapped = mapping.get(env)
            if mapped:
                self.zentao_base_url = mapped
        return self

    # 日志配置
    log_level: str = "INFO"
    log_file: str = "logs/zentao_mcp.log"
    
    # Application
    debug: bool = False
    
    model_config = ConfigDict(
        # 只读取当前目录下的.env文件
        env_file=str(Path(__file__).parent.parent.parent / ".env"),
        env_file_encoding="utf-8",
        extra="ignore",  # 忽略额外的环境变量
        case_sensitive=False
    )


settings = Settings()