"""
System initialization module
"""
import logging
from sqlalchemy.orm import Session
from app.core.database import get_db, engine
from app.models.admin import User
from app.models.api_key import APIKey
from app.services.auth_service import initialize_default_admin
from app.core.config import settings
from app.crud.api_key import hash_api_key

logger = logging.getLogger(__name__)


def create_tables():
    """Create all database tables"""
    try:
        # Import all models to ensure they are registered
        from app.models import admin, api_key
        
        # Create all tables
        User.metadata.create_all(bind=engine)
        APIKey.metadata.create_all(bind=engine)
        
        logger.info("数据库表创建成功")
        
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise


def initialize_system():
    """Initialize the system with default admin user"""
    try:
        # Create tables first
        create_tables()

        # Get database session
        db = next(get_db())

        try:
            # Initialize default admin user
            admin_user, was_created = initialize_default_admin(db)

            if was_created:
                logger.info(f"默认管理员账户创建成功: {admin_user.username}")
                logger.info(f"管理员邮箱: {admin_user.email}")
                logger.info("请及时修改默认密码！")
            else:
                logger.info(f"管理员账户已存在: {admin_user.username}")

            # Initialize development API key if in development mode
            if settings.debug:
                dev_api_key_created = initialize_dev_api_key(db, admin_user)
                if dev_api_key_created:
                    logger.info("开发环境API Key创建成功")

            return True

        finally:
            db.close()

    except Exception as e:
        logger.error(f"系统初始化失败: {e}")
        return False


def initialize_dev_api_key(db: Session, admin_user: User) -> bool:
    """
    Initialize development API key for testing

    Args:
        db: Database session
        admin_user: Admin user instance

    Returns:
        bool: True if API key was created, False if already exists
    """
    try:
        dev_api_key_value = "dev-api-key-for-testing"

        # Check if dev API key already exists
        existing_key = db.query(APIKey).filter(APIKey.key_value == dev_api_key_value).first()
        if existing_key:
            logger.info("开发环境API Key已存在")
            return False

        # Create development API key
        key_hash = hash_api_key(dev_api_key_value)

        dev_api_key = APIKey(
            user_id=admin_user.id,
            name="Development API Key",
            key=dev_api_key_value,
            key_value=dev_api_key_value,
            key_hash=key_hash,
            user_identifier=f"user_{admin_user.id}",
            description="Development environment API key for testing",
            is_active=True
        )

        db.add(dev_api_key)
        db.commit()
        db.refresh(dev_api_key)

        logger.info(f"开发环境API Key创建成功: {dev_api_key_value}")
        return True

    except Exception as e:
        logger.error(f"创建开发环境API Key失败: {e}")
        return False


def get_system_status():
    """Get system initialization status"""
    try:
        db = next(get_db())
        
        try:
            # Check if admin user exists
            admin_count = db.query(User).count()
            api_key_count = db.query(APIKey).count()
            
            return {
                "initialized": admin_count > 0,
                "admin_users": admin_count,
                "api_keys": api_key_count
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return {
            "initialized": False,
            "error": str(e)
        }


if __name__ == "__main__":
    # 直接运行此脚本进行系统初始化
    logging.basicConfig(level=logging.INFO)
    
    print("开始系统初始化...")
    
    if initialize_system():
        print("系统初始化完成！")
        
        status = get_system_status()
        print(f"系统状态: {status}")
        
        print(f"\n默认管理员信息:")
        print(f"用户名: {settings.admin_username}")
        print(f"密码: {settings.admin_password}")
        print(f"邮箱: {settings.admin_email}")
        print("\n⚠️  请及时登录管理后台修改默认密码！")
        
    else:
        print("系统初始化失败！")