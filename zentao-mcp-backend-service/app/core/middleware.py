"""
FastAPI中间件 - 请求日志记录
"""

import time
import json
import logging
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from typing import Callable

logger = logging.getLogger("zentao_api_requests")


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """API请求日志记录中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """处理请求并记录日志"""
        
        # 记录请求开始时间
        start_time = time.time()
        
        # 获取客户端IP
        client_ip = self._get_client_ip(request)
        
        # 获取API Key（用于日志，部分脱敏）
        api_key = self._get_masked_api_key(request)
        
        # 记录请求信息
        request_info = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(start_time)),
            "method": request.method,
            "url": str(request.url),
            "path": request.url.path,
            "client_ip": client_ip,
            "api_key": api_key,
            "user_agent": request.headers.get("user-agent", ""),
        }
        
        # 处理请求
        try:
            response = await call_next(request)
            
            # 计算处理时间
            process_time = time.time() - start_time
            
            # 记录响应信息
            response_info = {
                **request_info,
                "status_code": response.status_code,
                "process_time_ms": round(process_time * 1000, 2),
                "response_size": response.headers.get("content-length", "unknown")
            }
            
            # 输出JSON格式的日志到stdout
            logger.info(json.dumps(response_info, ensure_ascii=False))
            
            # 添加处理时间到响应头
            response.headers["X-Process-Time"] = str(process_time)
            
            return response
            
        except Exception as e:
            # 记录异常信息
            process_time = time.time() - start_time
            error_info = {
                **request_info,
                "status_code": 500,
                "process_time_ms": round(process_time * 1000, 2),
                "error": str(e),
                "error_type": type(e).__name__
            }
            
            logger.error(json.dumps(error_info, ensure_ascii=False))
            raise
    
    def _get_client_ip(self, request: Request) -> str:
        """获取客户端真实IP地址"""
        # 检查代理头
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # 返回直接连接的IP
        return request.client.host if request.client else "unknown"
    
    def _get_masked_api_key(self, request: Request) -> str:
        """获取部分脱敏的API Key"""
        auth_header = request.headers.get("authorization", "")
        if auth_header.startswith("Bearer "):
            api_key = auth_header[7:]  # 移除 "Bearer " 前缀
            if len(api_key) > 8:
                # 只显示前4位和后4位，中间用*代替
                return f"{api_key[:4]}***{api_key[-4:]}"
            else:
                return "***"
        return "none"