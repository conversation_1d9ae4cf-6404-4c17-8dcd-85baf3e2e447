"""
自定义异常类
"""


class UserManagementException(Exception):
    """用户管理异常基类"""
    pass


class UserNotFoundError(UserManagementException):
    """用户不存在异常"""
    pass


class UserAlreadyExistsError(UserManagementException):
    """用户已存在异常"""
    pass


class PermissionDeniedError(UserManagementException):
    """权限不足异常"""
    pass


class LastAdminError(UserManagementException):
    """不能删除最后一个活动的管理员异常"""
    pass


class WeakPasswordError(UserManagementException):
    """密码强度不足异常"""
    pass


class AccountLockedError(UserManagementException):
    """账户被锁定异常"""
    pass


class SessionExpiredError(UserManagementException):
    """会话过期异常"""
    pass


class InvalidTokenError(UserManagementException):
    """无效令牌异常"""
    pass


class APIKeyError(UserManagementException):
    """API Key相关异常"""
    pass


class AuditLogError(UserManagementException):
    """审计日志异常"""
    pass