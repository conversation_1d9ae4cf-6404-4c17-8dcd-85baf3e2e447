"""
API Key管理端点
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional, List

from app.core.database import get_db
from app.models import AdminUser, APIKey
from app.schemas.api_key import APIKeyCreate
from app.schemas.api_keys import (
    APIKeyUpdateRequest, APIKeyResponse, 
    APIKeyListResponse, CreateApiKeyResponse
)
from app.schemas.response import ApiResponse
from app.services.api_key_service import APIKeyService
from app.services.session_service import SessionService
from app.services.permission_service import PermissionService, Permission
from app.core.exceptions import (
    UserNotFoundError, PermissionDeniedError, SessionExpiredError, 
    InvalidTokenError, APIKeyError
)

router = APIRouter()
security = HTTPBearer()


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> AdminUser:
    """获取当前登录用户"""
    session_service = SessionService(db)
    
    try:
        session = session_service.validate_session(credentials.credentials)
        return session.user
    except (SessionExpiredError, InvalidTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="会话已过期，请重新登录"
        )


@router.post("/", response_model=ApiResponse[CreateApiKeyResponse])
async def create_api_key(
    key_data: APIKeyCreate,
    request: Request,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建API Key"""
    api_key_service = APIKeyService(db)
    
    try:
        # 创建API Key
        api_key = api_key_service.create_api_key(
            user_id=current_user.id,
            name=key_data.name,
            creator_id=current_user.id
        )
        
        response_data = CreateApiKeyResponse(
            id=str(api_key.id),
            name=api_key.name,
            api_key=api_key.key_value
        )
        
        return ApiResponse(data=response_data, message="API Key创建成功")
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except APIKeyError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=APIKeyListResponse)
async def get_api_keys(
    user_id: Optional[int] = Query(None, description="用户ID筛选"),
    is_active: Optional[bool] = Query(None, description="活跃状态筛选"),
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数限制"),
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取API Key列表"""
    api_key_service = APIKeyService(db)
    permission_service = PermissionService(db)
    
    try:
        # 访问控制：
        # - 普通用户：只允许查看自己的 Key，不触发 apikey:list 检查
        # - 管理员：可查看任意用户或全部 Key，需要具有 apikey:list
        if current_user.user_type.value == 'user':
            # 强制限定为当前用户，忽略外部传入的 user_id
            user_id = current_user.id
        else:
            # 管理员：查看其他用户或全量时校验 apikey:list
            if user_id and user_id != current_user.id:
                permission_service.check_permission(current_user, Permission.APIKEY_LIST)
            elif not user_id:
                permission_service.check_permission(current_user, Permission.APIKEY_LIST)
        
        # 获取API Key列表
        api_keys = api_key_service.get_api_keys(
            user_id=user_id,
            is_active=is_active,
            skip=skip,
            limit=limit
        )
        
        return APIKeyListResponse(
            api_keys=[APIKeyResponse.from_orm(key) for key in api_keys],
            total=len(api_keys),
            skip=skip,
            limit=limit
        )
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get("/{key_id}", response_model=APIKeyResponse)
async def get_api_key(
    key_id: int,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定API Key信息"""
    api_key_service = APIKeyService(db)
    permission_service = PermissionService(db)
    
    try:
        # 获取API Key
        api_key = api_key_service.get_api_key_by_id(key_id)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API Key不存在"
            )
        
        # 检查权限
        if api_key.user_id != current_user.id:
            permission_service.check_permission(current_user, Permission.APIKEY_READ)
        
        return APIKeyResponse.from_orm(api_key)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.put("/{key_id}", response_model=APIKeyResponse)
async def update_api_key(
    key_id: int,
    key_data: APIKeyUpdateRequest,
    request: Request,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新API Key"""
    api_key_service = APIKeyService(db)
    permission_service = PermissionService(db)
    
    try:
        # 获取API Key
        api_key = api_key_service.get_api_key_by_id(key_id)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API Key不存在"
            )
        
        # 检查权限
        if api_key.user_id != current_user.id:
            permission_service.check_permission(current_user, Permission.APIKEY_UPDATE)
        
        # 准备更新数据
        updates = {}
        if key_data.name is not None:
            updates['name'] = key_data.name
        if key_data.description is not None:
            updates['description'] = key_data.description
        if key_data.permissions is not None:
            updates['permissions'] = key_data.permissions
        if key_data.is_active is not None:
            updates['is_active'] = key_data.is_active
        
        # 更新API Key
        updated_key = api_key_service.update_api_key(
            key_id=key_id,
            updates=updates,
            operator_id=current_user.id
        )
        
        return APIKeyResponse.from_orm(updated_key)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except APIKeyError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.delete("/{key_id}")
async def delete_api_key(
    key_id: int,
    request: Request,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除API Key"""
    api_key_service = APIKeyService(db)
    permission_service = PermissionService(db)
    
    try:
        # 获取API Key
        api_key = api_key_service.get_api_key_by_id(key_id)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API Key不存在"
            )
        
        # 检查权限
        if api_key.user_id != current_user.id:
            permission_service.check_permission(current_user, Permission.APIKEY_DELETE)
        
        # 删除API Key
        api_key_service.delete_api_key(key_id=key_id, operator_id=current_user.id)
        
        return {"message": f"API Key {api_key.name} 已删除"}
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.put("/{key_id}/revoke", response_model=APIKeyResponse)
async def revoke_api_key(
    key_id: int,
    request: Request,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """撤销API Key"""
    api_key_service = APIKeyService(db)
    permission_service = PermissionService(db)
    
    try:
        # 获取API Key
        api_key = api_key_service.get_api_key_by_id(key_id)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API Key不存在"
            )
        
        # 检查权限
        if api_key.user_id != current_user.id:
            permission_service.check_permission(current_user, Permission.APIKEY_UPDATE)
        
        # 撤销API Key
        updated_key = api_key_service.update_api_key(
            key_id=key_id,
            updates={'is_active': False},
            operator_id=current_user.id
        )
        
        return APIKeyResponse.from_orm(updated_key)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except APIKeyError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{key_id}/activate", response_model=APIKeyResponse)
async def activate_api_key(
    key_id: int,
    request: Request,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """激活API Key"""
    api_key_service = APIKeyService(db)
    permission_service = PermissionService(db)
    
    try:
        # 获取API Key
        api_key = api_key_service.get_api_key_by_id(key_id)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API Key不存在"
            )
        
        # 检查权限
        if api_key.user_id != current_user.id:
            permission_service.check_permission(current_user, Permission.APIKEY_UPDATE)
        
        # 激活API Key
        updated_key = api_key_service.update_api_key(
            key_id=key_id,
            updates={'is_active': True},
            operator_id=current_user.id
        )
        
        return APIKeyResponse.from_orm(updated_key)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except APIKeyError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{key_id}/regenerate")
async def regenerate_api_key(
    key_id: int,
    request: Request,
    current_user: AdminUser = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """重新生成API Key"""
    api_key_service = APIKeyService(db)
    permission_service = PermissionService(db)
    
    try:
        # 获取API Key
        api_key = api_key_service.get_api_key_by_id(key_id)
        if not api_key:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="API Key不存在"
            )
        
        # 检查权限
        if api_key.user_id != current_user.id:
            permission_service.check_permission(current_user, Permission.APIKEY_UPDATE)
        
        # 重新生成API Key
        new_key = api_key_service.regenerate_api_key(key_id=key_id, operator_id=current_user.id)
        
        return {
            "message": "API Key已重新生成",
            "new_key": new_key
        }
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except APIKeyError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )