"""
用户管理API端点
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from typing import Optional, List

from app.core.database import get_db
from app.models.admin import User, UserType
from app.schemas.users import (
    UserCreateRequest, UserUpdateRequest, UserResponse,
    UserListResponse, PasswordResetRequest
)
from app.services.user_management_service import UserManagementService
from app.services.session_service import SessionService
from app.services.permission_service import PermissionService, Permission
from app.services.audit_service import AuditService
from app.core.exceptions import (
    UserNotFoundError, UserAlreadyExistsError, PermissionDeniedError,
    LastAdminError, WeakPasswordError, SessionExpiredError, InvalidTokenError
)

router = APIRouter()
security = HTTPBearer()


def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
) -> User:
    """获取当前登录用户"""
    session_service = SessionService(db)
    
    try:
        session = session_service.validate_session(credentials.credentials)
        return session.user
    except (SessionExpiredError, InvalidTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="会话已过期，请重新登录"
        )


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return forwarded.split(",")[0].strip()
    return request.client.host


@router.post("/", response_model=UserResponse, include_in_schema=False)
@router.post("", response_model=UserResponse)
async def create_user(
    user_data: UserCreateRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建新用户"""
    user_service = UserManagementService(db)
    permission_service = PermissionService(db)
    audit_service = AuditService(db)
    
    try:
        # 检查权限
        permission_service.check_permission(current_user, Permission.USER_CREATE)
        
        # 如果要创建管理员，需要额外权限
        if user_data.user_type == UserType.ADMIN:
            permission_service.check_permission(current_user, Permission.ADMIN_CREATE)
        
        # 创建用户
        new_user = user_service.create_user(
            username=user_data.username,
            password=user_data.password,
            email=user_data.email,
            user_type=user_data.user_type,
            phone=user_data.phone,
            creator_id=current_user.id
        )
        
        return UserResponse.from_orm(new_user)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except UserAlreadyExistsError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except WeakPasswordError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/", response_model=UserListResponse, include_in_schema=False)
@router.get("", response_model=UserListResponse)
async def get_users(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数限制"),
    user_type: Optional[UserType] = Query(None, description="用户类型筛选"),
    is_active: Optional[bool] = Query(None, description="活跃状态筛选"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取用户列表"""
    user_service = UserManagementService(db)
    permission_service = PermissionService(db)
    
    try:
        # 检查权限
        permission_service.check_permission(current_user, Permission.USER_LIST)
        
        # 获取用户列表
        users = user_service.get_users(
            skip=skip,
            limit=limit,
            user_type=user_type,
            is_active=is_active,
            search=search
        )
        
        # 根据权限过滤用户列表
        filtered_users = permission_service.filter_accessible_users(current_user, users)
        
        return UserListResponse(
            users=[UserResponse.from_orm(user) for user in filtered_users],
            total=len(filtered_users),
            skip=skip,
            limit=limit
        )
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取指定用户信息"""
    user_service = UserManagementService(db)
    permission_service = PermissionService(db)
    
    try:
        # 获取目标用户
        target_user = user_service.get_user_by_id(user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查是否可以查看该用户
        if current_user.id != user_id:
            permission_service.check_permission(current_user, Permission.USER_READ)
            
            # 检查是否可以管理该用户
            if not permission_service.can_manage_user(current_user, target_user):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限查看该用户信息"
                )
        
        return UserResponse.from_orm(target_user)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: int,
    user_data: UserUpdateRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新用户信息"""
    user_service = UserManagementService(db)
    permission_service = PermissionService(db)
    
    try:
        # 获取目标用户
        target_user = user_service.get_user_by_id(user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查权限
        if current_user.id != user_id:
            permission_service.check_permission(current_user, Permission.USER_UPDATE)
            
            # 检查是否可以管理该用户
            if not permission_service.can_manage_user(current_user, target_user):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限修改该用户信息"
                )
        
        # 准备更新数据
        updates = {}
        if user_data.email is not None:
            updates['email'] = user_data.email
        if user_data.phone is not None:
            updates['phone'] = user_data.phone
        if user_data.is_active is not None and current_user.id != user_id:
            # 只有管理员可以修改其他用户的活跃状态
            permission_service.check_permission(current_user, Permission.USER_UPDATE)
            updates['is_active'] = user_data.is_active
        if user_data.user_type is not None and current_user.id != user_id:
            # 只有管理员可以修改用户类型
            if current_user.user_type != UserType.ADMIN:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有管理员可以修改用户类型"
                )
            updates['user_type'] = user_data.user_type
        
        # 更新用户
        updated_user = user_service.update_user(
            user_id=user_id,
            updates=updates,
            operator_id=current_user.id
        )
        
        return UserResponse.from_orm(updated_user)
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )


@router.delete("/{user_id}")
async def delete_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除用户"""
    user_service = UserManagementService(db)
    permission_service = PermissionService(db)
    
    try:
        # 检查权限
        permission_service.check_permission(current_user, Permission.USER_DELETE)
        
        # 获取目标用户
        target_user = user_service.get_user_by_id(user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查是否可以删除该用户
        if not permission_service.can_manage_user(current_user, target_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限删除该用户"
            )
        
        # 删除用户
        user_service.delete_user(user_id=user_id, operator_id=current_user.id)
        
        return {"message": f"用户 {target_user.username} 已删除"}
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except LastAdminError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{user_id}/reset-password")
async def reset_user_password(
    user_id: int,
    password_data: PasswordResetRequest,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """重置用户密码"""
    user_service = UserManagementService(db)
    permission_service = PermissionService(db)
    
    try:
        # 检查权限
        permission_service.check_permission(current_user, Permission.USER_UPDATE)
        
        # 获取目标用户
        target_user = user_service.get_user_by_id(user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查是否可以重置该用户密码
        if not permission_service.can_manage_user(current_user, target_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限重置该用户密码"
            )
        
        # 重置密码
        new_password = user_service.reset_password(
            user_id=user_id,
            new_password=password_data.new_password,
            operator_id=current_user.id,
            force_change=password_data.force_change
        )
        
        response = {"message": "密码重置成功"}
        
        # 如果是生成的临时密码，返回密码
        if not password_data.new_password:
            response["temporary_password"] = new_password
        
        return response
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )
    except WeakPasswordError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.post("/{user_id}/unlock")
async def unlock_user(
    user_id: int,
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """解锁用户账户"""
    user_service = UserManagementService(db)
    permission_service = PermissionService(db)
    
    try:
        # 检查权限
        permission_service.check_permission(current_user, Permission.USER_UPDATE)
        
        # 获取目标用户
        target_user = user_service.get_user_by_id(user_id)
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        # 检查是否可以解锁该用户
        if not permission_service.can_manage_user(current_user, target_user):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限解锁该用户"
            )
        
        # 解锁用户
        success = user_service.unlock_user(user_id=user_id, operator_id=current_user.id)
        
        if success:
            return {"message": f"用户 {target_user.username} 已解锁"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="解锁失败"
            )
        
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e)
        )