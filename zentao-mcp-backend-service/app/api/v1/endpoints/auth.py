"""
认证相关API端点 - 新版本
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials, OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from datetime import datetime, timezone
from typing import Optional

from app.core.database import get_db
from app.models import User, UserSession
from app.schemas.auth import LoginRequest, LoginResponse, ChangePasswordRequest, TokenResponse
from app.services.user_management_service import UserManagementService
from app.services.session_service import SessionService
from app.services.audit_service import AuditService
from app.core.dependencies import get_user_management_service, get_user_session_service, get_audit_service, security
from app.core.exceptions import (
    UserNotFoundError, 
    PermissionDeniedError, 
    AccountLockedError,
    SessionExpiredError,
    InvalidTokenError,
    WeakPasswordError
)

router = APIRouter()


def get_client_ip(request: Request) -> str:
    """获取客户端IP地址"""
    forwarded = request.headers.get("X-Forwarded-For")
    if forwarded:
        return forwarded.split(",")[0].strip()
    return request.client.host


def get_user_agent(request: Request) -> str:
    """获取用户代理"""
    return request.headers.get("User-Agent", "Unknown")


@router.post("/login", response_model=LoginResponse)
async def login(
    login_data: LoginRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """用户登录"""
    user_service = UserManagementService(db)
    session_service = SessionService(db)
    audit_service = AuditService(db)
    
    ip_address = get_client_ip(request)
    user_agent = get_user_agent(request)
    
    try:
        # 查找用户
        user = user_service.get_user_by_username(login_data.username)
        
        if not user:
            # 记录失败的登录尝试
            audit_service.log_action(
                user_id=0,  # 未知用户
                action="login_failed",
                resource_type="auth",
                details={"username": login_data.username, "reason": "user_not_found"},
                ip_address=ip_address,
                user_agent=user_agent
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 检查账户状态
        if not user.is_active:
            audit_service.log_action(
                user_id=user.id,
                action="login_failed",
                resource_type="auth",
                details={"username": login_data.username, "reason": "account_disabled"},
                ip_address=ip_address,
                user_agent=user_agent
            )
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="账户已被禁用"
            )
        
        # 检查账户是否被锁定
        if user.is_locked:
            audit_service.log_action(
                user_id=user.id,
                action="login_failed",
                resource_type="auth",
                details={"username": login_data.username, "reason": "account_locked"},
                ip_address=ip_address,
                user_agent=user_agent
            )
            raise HTTPException(
                status_code=status.HTTP_423_LOCKED,
                detail=f"账户已被锁定，请在 {user.locked_until} 后重试"
            )
        
        # 验证密码
        if not user_service._verify_user_password(user, login_data.password):
            # 增加失败次数
            user.failed_login_attempts = (user.failed_login_attempts or 0) + 1
            
            # 如果失败次数过多，锁定账户
            if user.failed_login_attempts >= 5:
                user_service.lock_user(user.id, 30)  # 锁定30分钟
                
                audit_service.log_action(
                    user_id=user.id,
                    action="account_locked",
                    resource_type="auth",
                    details={"username": login_data.username, "reason": "too_many_failed_attempts"},
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
                raise HTTPException(
                    status_code=status.HTTP_423_LOCKED,
                    detail="登录失败次数过多，账户已被锁定30分钟"
                )
            
            db.commit()
            
            audit_service.log_action(
                user_id=user.id,
                action="login_failed",
                resource_type="auth",
                details={
                    "username": login_data.username, 
                    "reason": "wrong_password",
                    "failed_attempts": user.failed_login_attempts
                },
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码错误"
            )
        
        # 登录成功，重置失败次数
        user.failed_login_attempts = 0
        user.last_login = datetime.now(timezone.utc)
        db.commit()
        
        # 创建会话
        session = session_service.create_session(
            user=user,
            ip_address=ip_address,
            user_agent=user_agent,
            remember_me=getattr(login_data, 'remember_me', False)
        )
        
        # 记录成功登录
        audit_service.log_action(
            user_id=user.id,
            action="login_success",
            resource_type="auth",
            details={"username": login_data.username},
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        return LoginResponse(
            access_token=session.session_token,
            token_type="bearer",
            user_id=user.id,
            username=user.username,
            user_type=user.user_type.value,
            expires_at=session.expires_at
        )
        
    except HTTPException:
        raise
    except Exception as e:
        import traceback
        error_details = traceback.format_exc()
        print(f"登录错误详情: {error_details}")  # 调试日志
        
        try:
            audit_service.log_action(
                user_id=0,
                action="login_error",
                resource_type="auth",
                details={"username": login_data.username, "error": str(e)},
                ip_address=ip_address,
                user_agent=user_agent
            )
        except Exception as audit_error:
            print(f"审计日志错误: {audit_error}")
            
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录过程中发生错误: {str(e)}"
        )


@router.post("/logout")
async def logout(
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """用户登出"""
    session_service = SessionService(db)
    audit_service = AuditService(db)
    
    try:
        # 验证会话
        session = session_service.validate_session(credentials.credentials)
        
        # 使会话失效
        session_service.invalidate_session(credentials.credentials)
        
        # 记录登出
        audit_service.log_action(
            user_id=session.user_id,
            action="logout",
            resource_type="auth",
            details={"session_id": session.id},
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request)
        )
        
        return {"message": "登出成功"}
        
    except (SessionExpiredError, InvalidTokenError):
        return {"message": "会话已过期"}
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="登出过程中发生错误"
        )


@router.get("/me")
async def get_current_user_info(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取当前用户信息"""
    session_service = SessionService(db)
    
    try:
        # 验证会话
        session = session_service.validate_session(credentials.credentials)
        user = session.user
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )
        
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "user_type": user.user_type.value,
            "user_type_display": user.get_user_type_display(),
            "phone": user.phone,
            "is_active": user.is_active,
            "created_at": user.created_at,
            "last_login": user.last_login,
            "last_password_change": user.last_password_change,
            "is_locked": user.is_locked
        }
        
    except (SessionExpiredError, InvalidTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="会话已过期，请重新登录"
        )


@router.post("/change-password")
async def change_password(
    password_data: ChangePasswordRequest,
    request: Request,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """修改密码"""
    session_service = SessionService(db)
    user_service = UserManagementService(db)
    audit_service = AuditService(db)
    
    try:
        # 验证会话
        session = session_service.validate_session(credentials.credentials)
        user = session.user
        
        # 修改密码
        user_service.change_password(
            user_id=user.id,
            old_password=password_data.old_password,
            new_password=password_data.new_password
        )
        
        # 记录密码修改
        audit_service.log_action(
            user_id=user.id,
            action="password_change",
            resource_type="auth",
            details={"username": user.username},
            ip_address=get_client_ip(request),
            user_agent=get_user_agent(request)
        )
        
        return {"message": "密码修改成功，请重新登录"}
        
    except (SessionExpiredError, InvalidTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="会话已过期，请重新登录"
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except WeakPasswordError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.get("/sessions")
async def get_user_sessions(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """获取用户的所有会话"""
    session_service = SessionService(db)
    
    try:
        # 验证会话
        session = session_service.validate_session(credentials.credentials)
        user = session.user
        
        # 获取用户所有会话
        sessions = session_service.get_user_sessions(user.id)
        
        return {
            "sessions": [
                {
                    "id": s.id,
                    "ip_address": s.ip_address,
                    "user_agent": s.user_agent,
                    "created_at": s.created_at,
                    "last_activity": s.last_activity,
                    "expires_at": s.expires_at,
                    "is_current": s.session_token == credentials.credentials
                }
                for s in sessions
            ]
        }
        
    except (SessionExpiredError, InvalidTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="会话已过期，请重新登录"
        )


@router.delete("/sessions/{session_id}")
async def revoke_session(
    session_id: int,
    credentials: HTTPAuthorizationCredentials = Depends(security),
    db: Session = Depends(get_db)
):
    """撤销指定会话"""
    session_service = SessionService(db)
    audit_service = AuditService(db)
    
    try:
        # 验证当前会话
        current_session = session_service.validate_session(credentials.credentials)
        user = current_session.user
        
        # 获取要撤销的会话
        target_session = db.query(UserSession).filter(
            UserSession.id == session_id,
            UserSession.user_id == user.id
        ).first()
        
        if not target_session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="会话不存在"
            )
        
        # 不能撤销当前会话
        if target_session.session_token == credentials.credentials:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不能撤销当前会话"
            )
        
        # 撤销会话
        session_service.invalidate_session(target_session.session_token)
        
        # 记录会话撤销
        audit_service.log_action(
            user_id=user.id,
            action="session_revoked",
            resource_type="auth",
            details={"revoked_session_id": session_id}
        )
        
        return {"message": "会话已撤销"}
        
    except (SessionExpiredError, InvalidTokenError):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="会话已过期，请重新登录"
        )