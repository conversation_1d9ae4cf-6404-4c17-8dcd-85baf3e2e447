"""
用户管理API端点 - 使用POST + 函数式命名
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_user_service
from app.services.zentao.user_service import UserService
from typing import Any, Dict, List

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/zentao_get_users_by_accounts", response_model=Dict[str, Any])
async def zentao_get_users_by_accounts(
    accounts: List[str],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    user_service: UserService = Depends(get_user_service)
):
    """
    根据用户账号列表获取用户信息

    Args:
        accounts (List[str]): 用户账号列表，用于批量查询用户信息
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        user_service (UserService): 用户服务实例

    Returns:
        Dict[str, Any]: 包含用户信息列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_users_by_accounts")
        response = await user_service.get_users_by_accounts(accounts)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取用户信息，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据用户账号列表获取用户信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据用户账号列表获取用户信息失败: {str(e)}"
        )



