"""
任务管理API端点 - 使用POST + 函数式命名
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_task_service
from app.services.zentao.task_service import TaskService
from typing import Any, Dict

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/zentao_get_task_detail", response_model=Dict[str, Any])
async def zentao_get_task_detail(
    task_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """
    根据任务ID查询任务详情

    Args:
        task_id (int): 任务ID，用于查询任务的详细信息
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        task_service (TaskService): 任务服务实例

    Returns:
        Dict[str, Any]: 包含任务详情的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_task_detail，任务ID: {task_id}")
        response = await task_service.get_task_by_id(task_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取任务 {task_id} 详情，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务详情失败: {str(e)}"
        )


@router.post("/zentao_get_tasks_by_account", response_model=Dict[str, Any])
async def zentao_get_tasks_by_account(
    account: str,
    start_date: str,
    end_date: str,
    is_doing: bool = False,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """
    根据域账号查询任务

    Args:
        account (str): 用户域账号，用于查询该用户的任务列表
        start_date (str): 开始日期，格式为 YYYY-MM-DD HH:MM:SS
        end_date (str): 结束日期，格式为 YYYY-MM-DD HH:MM:SS
        is_doing (bool): 是否只查询进行中的任务，默认为False
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        task_service (TaskService): 任务服务实例

    Returns:
        Dict[str, Any]: 包含任务列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_tasks_by_account，账号: {account}")
        response = await task_service.get_tasks_by_account(account, start_date, end_date, is_doing)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取账号 {account} 任务列表，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据账号查询任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据账号查询任务失败: {str(e)}"
        )


@router.post("/zentao_get_tasks_by_dept", response_model=Dict[str, Any])
async def zentao_get_tasks_by_dept(
    dept_id: int,
    start_date: str,
    end_date: str,
    is_doing: bool = False,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    task_service: TaskService = Depends(get_task_service)
):
    """
    根据部门查询任务列表

    Args:
        dept_id (int): 部门ID，用于查询该部门下的所有任务
        start_date (str): 开始日期，格式为 YYYY-MM-DD HH:MM:SS
        end_date (str): 结束日期，格式为 YYYY-MM-DD HH:MM:SS
        is_doing (bool): 是否只查询进行中的任务，默认为False
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        task_service (TaskService): 任务服务实例

    Returns:
        Dict[str, Any]: 包含任务列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_tasks_by_dept，部门ID: {dept_id}")
        response = await task_service.get_tasks_by_dept(dept_id, start_date, end_date, is_doing)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取部门 {dept_id} 任务列表，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据部门查询任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据部门查询任务失败: {str(e)}"
        )
