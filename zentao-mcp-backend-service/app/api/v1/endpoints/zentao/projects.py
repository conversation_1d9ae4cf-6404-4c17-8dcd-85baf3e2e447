"""
项目管理API端点 - 使用POST + 函数式命名
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_project_service
from app.services.zentao.project_service import ProjectService
from typing import Any, Dict

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/zentao_get_all_projects", response_model=Dict[str, Any])
async def zentao_get_all_projects(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """
    获取所有项目列表

    Args:
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        project_service (ProjectService): 项目服务实例

    Returns:
        Dict[str, Any]: 包含项目列表的响应数据

    Raises:
        HTTPException: 当API调用失败时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_all_projects")

        response = await project_service.get_all_projects()

        logger.info(f"成功获取项目列表，响应码: {response.get('rsCode')}")
        return response

    except Exception as e:
        logger.error(f"获取所有项目列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有项目列表失败: {str(e)}"
        )


@router.post("/zentao_get_tasks_by_project", response_model=Dict[str, Any])
async def zentao_get_tasks_by_project(
    request_data: Dict[str, Any] = Body(...),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """
    根据项目ID查询任务列表

    Args:
        project_id (int): 项目ID，用于查询该项目下的所有任务
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        project_service (ProjectService): 项目服务实例

    Returns:
        Dict[str, Any]: 包含任务列表的响应数据

    Raises:
        HTTPException: 当API调用失败时抛出异常
    """
    try:
        project_id = request_data.get('project_id')
        if project_id is None:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="缺少必需参数: project_id"
            )

        logger.info(f"用户 {user_identifier} 调用 zentao_get_tasks_by_project，项目ID: {project_id}")

        response = await project_service.get_project_tasks(project_id)

        logger.info(f"成功获取项目 {project_id} 任务列表，响应码: {response.get('rsCode')}")
        return response

    except Exception as e:
        logger.error(f"根据项目ID查询任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询任务列表失败: {str(e)}"
        )


@router.post("/zentao_get_stories_by_project", response_model=Dict[str, Any])
async def zentao_get_stories_by_project(
    request_data: Dict[str, Any] = Body(...),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """
    根据项目ID查询需求列表

    Args:
        project_id (int): 项目ID，用于查询该项目下的所有需求
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        project_service (ProjectService): 项目服务实例

    Returns:
        Dict[str, Any]: 包含需求列表的响应数据

    Raises:
        HTTPException: 当API调用失败时抛出异常
    """
    try:
        project_id = request_data.get('project_id')
        if project_id is None:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="缺少必需参数: project_id"
            )

        logger.info(f"用户 {user_identifier} 调用 zentao_get_stories_by_project，项目ID: {project_id}")

        response = await project_service.get_project_stories(project_id)

        logger.info(f"成功获取项目 {project_id} 需求列表，响应码: {response.get('rsCode')}")
        return response

    except Exception as e:
        logger.error(f"根据项目ID查询需求列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询需求列表失败: {str(e)}"
        )


@router.post("/zentao_get_bugs_by_project", response_model=Dict[str, Any])
async def zentao_get_bugs_by_project(
    request_data: Dict[str, Any] = Body(...),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    project_service: ProjectService = Depends(get_project_service)
):
    """
    根据项目ID查询Bug列表

    Args:
        project_id (int): 项目ID，用于查询该项目下的所有Bug
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        project_service (ProjectService): 项目服务实例

    Returns:
        Dict[str, Any]: 包含Bug列表的响应数据

    Raises:
        HTTPException: 当API调用失败时抛出异常
    """
    try:
        project_id = request_data.get('project_id')
        if project_id is None:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="缺少必需参数: project_id"
            )

        logger.info(f"用户 {user_identifier} 调用 zentao_get_bugs_by_project，项目ID: {project_id}")

        response = await project_service.get_project_bugs(project_id)

        logger.info(f"成功获取项目 {project_id} Bug列表，响应码: {response.get('rsCode')}")
        return response

    except Exception as e:
        logger.error(f"根据项目ID查询Bug列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据项目ID查询Bug列表失败: {str(e)}"
        )
