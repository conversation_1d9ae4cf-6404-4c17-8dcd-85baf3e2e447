"""
数据分析API端点 - 使用POST + 函数式命名

提供Bug分析、项目分析、需求分析等增强功能
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_analysis_service
from app.services.zentao.analysis_service import AnalysisService
from typing import Any, Dict, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/zentao_get_project_analysis", response_model=Dict[str, Any])
async def zentao_get_project_analysis(
    project_id: int,
    start_date: str,
    end_date: str,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    获取项目分析数据

    Args:
        project_id (int): 项目ID，用于分析该项目的数据
        start_date (str): 开始日期，格式为 YYYY-MM-DD HH:MM:SS
        end_date (str): 结束日期，格式为 YYYY-MM-DD HH:MM:SS
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        analysis_service (AnalysisService): 分析服务实例

    Returns:
        Dict[str, Any]: 包含项目分析数据的响应

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_project_analysis，项目ID: {project_id}")
        response = await analysis_service.get_project_analysis(project_id, start_date, end_date)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取项目 {project_id} 分析数据，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目分析数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目分析数据失败: {str(e)}"
        )


@router.post("/analyze_bugs_by_dept_and_time", response_model=Dict[str, Any])
async def analyze_bugs_by_dept_and_time(
    start_date: str,
    end_date: str,
    dept_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    按部门和时间段统计Bug分析

    Args:
        start_date (str): 开始日期，格式为 YYYY-MM-DD
        end_date (str): 结束日期，格式为 YYYY-MM-DD
        dept_id (int): 部门ID，用于按部门统计Bug
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        analysis_service (AnalysisService): 分析服务实例

    Returns:
        Dict[str, Any]: 包含Bug分析统计的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 analyze_bugs_by_dept_and_time")
        response = await analysis_service.analyze_bugs_by_dept_and_time(start_date, end_date, dept_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功分析部门Bug统计，响应码: {response.get('success')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"按部门和时间段统计Bug分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"按部门和时间段统计Bug分析失败: {str(e)}"
        )


@router.post("/analyze_story_workload", response_model=Dict[str, Any])
async def analyze_story_workload(
    story_ids: Optional[List[int]] = None,
    project_id: Optional[int] = None,
    include_completed: bool = True,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    分析需求工时统计

    Args:
        story_ids (Optional[List[int]]): 需求ID列表，可选参数
        project_id (Optional[int]): 项目ID，可选参数
        include_completed (bool): 是否包含已完成的需求，默认为True
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        analysis_service (AnalysisService): 分析服务实例

    Returns:
        Dict[str, Any]: 包含需求工时统计的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 analyze_story_workload")
        response = await analysis_service.analyze_story_workload(story_ids, project_id, include_completed)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功分析需求工时统计，响应码: {response.get('success')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"分析需求工时统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"分析需求工时统计失败: {str(e)}"
        )


@router.post("/project_summary_analysis", response_model=Dict[str, Any])
async def project_summary_analysis(
    project_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    项目整体统计分析

    Args:
        project_id (int): 项目ID，用于分析该项目的整体统计
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        analysis_service (AnalysisService): 分析服务实例

    Returns:
        Dict[str, Any]: 包含项目整体统计分析的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 project_summary_analysis")
        response = await analysis_service.project_summary_analysis(project_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功分析项目整体统计，响应码: {response.get('success')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"项目整体统计分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"项目整体统计分析失败: {str(e)}"
        )


@router.post("/personnel_workload_analysis", response_model=Dict[str, Any])
async def personnel_workload_analysis(
    user_accounts: Optional[List[str]] = None,
    project_id: Optional[int] = None,
    dept_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    人员工作量统计和分析

    Args:
        user_accounts (Optional[List[str]]): 用户账号列表，可选参数
        project_id (Optional[int]): 项目ID，可选参数
        dept_id (Optional[int]): 部门ID，可选参数
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        analysis_service (AnalysisService): 分析服务实例

    Returns:
        Dict[str, Any]: 包含人员工作量统计分析的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 personnel_workload_analysis")
        response = await analysis_service.personnel_workload_analysis(user_accounts, project_id, dept_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功分析人员工作量，响应码: {response.get('success')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"人员工作量统计和分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"人员工作量统计和分析失败: {str(e)}"
        )


@router.post("/story_task_relation_query", response_model=Dict[str, Any])
async def story_task_relation_query(
    story_ids: Optional[List[int]] = None,
    project_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    需求-任务关联查询

    Args:
        story_ids (Optional[List[int]]): 需求ID列表，可选参数
        project_id (Optional[int]): 项目ID，可选参数
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        analysis_service (AnalysisService): 分析服务实例

    Returns:
        Dict[str, Any]: 包含需求任务关联查询的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 story_task_relation_query")
        response = await analysis_service.story_task_relation_query(story_ids, project_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功查询需求任务关联，响应码: {response.get('success')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"需求-任务关联查询失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"需求-任务关联查询失败: {str(e)}"
        )


@router.post("/bug_to_story_tracking", response_model=Dict[str, Any])
async def bug_to_story_tracking(
    bug_ids: Optional[List[int]] = None,
    project_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    analysis_service: AnalysisService = Depends(get_analysis_service)
):
    """
    Bug转需求追踪

    Args:
        bug_ids (Optional[List[int]]): Bug ID列表，可选参数
        project_id (Optional[int]): 项目ID，可选参数
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        analysis_service (AnalysisService): 分析服务实例

    Returns:
        Dict[str, Any]: 包含Bug转需求追踪的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 bug_to_story_tracking")
        response = await analysis_service.bug_to_story_tracking(bug_ids, project_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功追踪Bug转需求，响应码: {response.get('success')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Bug转需求追踪失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bug转需求追踪失败: {str(e)}"
        )