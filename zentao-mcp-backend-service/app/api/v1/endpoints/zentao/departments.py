"""
部门管理API端点 - 使用POST + 函数式命名
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_department_service
from app.services.zentao.department_service import DepartmentService
from typing import Any, Dict

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/zentao_get_all_departments", response_model=Dict[str, Any])
async def zentao_get_all_departments(
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    department_service: DepartmentService = Depends(get_department_service)
):
    """
    获取所有部门列表

    Args:
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        department_service (DepartmentService): 部门服务实例

    Returns:
        Dict[str, Any]: 包含部门列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_all_departments")
        response = await department_service.get_all_departments()

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取部门列表，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取所有部门列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取所有部门列表失败: {str(e)}"
        )


@router.post("/zentao_get_users_by_dept", response_model=Dict[str, Any])
async def zentao_get_users_by_dept(
    request_data: Dict[str, Any] = Body(...),
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    department_service: DepartmentService = Depends(get_department_service)
):
    """
    根据部门ID查询用户列表

    Args:
        dept_id (int): 部门ID，用于查询该部门下的所有用户
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        department_service (DepartmentService): 部门服务实例

    Returns:
        Dict[str, Any]: 包含用户列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        dept_id = request_data.get('dept_id')
        if dept_id is None:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="缺少必需参数: dept_id"
            )

        logger.info(f"用户 {user_identifier} 调用 zentao_get_users_by_dept，部门ID: {dept_id}")
        response = await department_service.get_users_by_dept(dept_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取部门 {dept_id} 用户列表，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据部门获取用户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据部门获取用户列表失败: {str(e)}"
        )