"""
Bug管理API端点 - 使用POST + 函数式命名
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_bug_service
from app.services.zentao.bug_service import BugService
from typing import Any, Dict, Optional

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/zentao_get_bugs_by_time_range", response_model=Dict[str, Any])
async def zentao_get_bugs_by_time_range(
    start_date: str,
    end_date: str,
    dept_id: Optional[int] = None,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """
    根据时间段查询Bug列表

    Args:
        start_date (str): 开始日期，格式为 YYYY-MM-DD HH:MM:SS
        end_date (str): 结束日期，格式为 YYYY-MM-DD HH:MM:SS
        dept_id (Optional[int]): 部门ID，可选参数用于按部门过滤
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        bug_service (BugService): Bug服务实例

    Returns:
        Dict[str, Any]: 包含Bug列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_bugs_by_time_range")
        if dept_id is not None:
            response = await bug_service.get_bugs_by_time_range_and_dept(start_date, end_date, dept_id)
        else:
            response = await bug_service.get_bugs_by_time_range(start_date, end_date)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取Bug列表，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据时间范围获取Bug列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据时间范围获取Bug列表失败: {str(e)}"
        )


@router.post("/zentao_get_bug_detail", response_model=Dict[str, Any])
async def zentao_get_bug_detail(
    bug_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """
    根据Bug ID查询Bug详情

    Args:
        bug_id (int): Bug ID，用于查询Bug的详细信息
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        bug_service (BugService): Bug服务实例

    Returns:
        Dict[str, Any]: 包含Bug详情的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_bug_detail，Bug ID: {bug_id}")
        response = await bug_service.get_bug_detail(bug_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取Bug {bug_id} 详情，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取Bug详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Bug详情失败: {str(e)}"
        )


@router.post("/zentao_get_personal_bugs", response_model=Dict[str, Any])
async def zentao_get_personal_bugs(
    account: str,
    status: str,
    start_date: str,
    end_date: str,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """
    查询个人Bug

    Args:
        account (str): 用户账号，用于查询该用户的Bug列表
        status (str): Bug状态，如 active/resolved/released/closed
        start_date (str): 开始日期，格式为 YYYY-MM-DD HH:MM:SS
        end_date (str): 结束日期，格式为 YYYY-MM-DD HH:MM:SS
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        bug_service (BugService): Bug服务实例

    Returns:
        Dict[str, Any]: 包含个人Bug列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_personal_bugs，账号: {account}")
        response = await bug_service.get_personal_bugs(account, status, start_date, end_date)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取账号 {account} 个人Bug，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取个人Bug失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取个人Bug失败: {str(e)}"
        )


@router.post("/zentao_get_bugs_by_time_and_dept", response_model=Dict[str, Any])
async def zentao_get_bugs_by_time_and_dept(
    start_date: str,
    end_date: str,
    dept_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    bug_service: BugService = Depends(get_bug_service)
):
    """
    根据时间段和部门查询Bug

    Args:
        start_date (str): 开始日期，格式为 YYYY-MM-DD HH:MM:SS
        end_date (str): 结束日期，格式为 YYYY-MM-DD HH:MM:SS
        dept_id (int): 部门ID，用于按部门过滤Bug
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        bug_service (BugService): Bug服务实例

    Returns:
        Dict[str, Any]: 包含Bug列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_bugs_by_time_and_dept")
        response = await bug_service.get_bugs_by_time_and_dept(start_date, end_date, dept_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功根据时间段和部门查询Bug，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"根据时间段和部门查询Bug失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"根据时间段和部门查询Bug失败: {str(e)}"
        )
