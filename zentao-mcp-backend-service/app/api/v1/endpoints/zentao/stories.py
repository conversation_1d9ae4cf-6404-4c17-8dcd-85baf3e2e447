"""
需求管理API端点 - 使用POST + 函数式命名
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body, Query
from sqlalchemy.orm import Session
from app.core.database import get_db
from app.core.security import verify_api_key
from app.core.dependencies import get_story_service
from app.services.zentao.story_service import StoryService
from typing import Any, Dict, List

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/zentao_get_story_info", response_model=Dict[str, Any])
async def zentao_get_story_info(
    story_ids: List[str],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """
    批量获取产品需求工时信息

    Args:
        story_ids (List[str]): 需求ID列表，用于批量查询需求信息
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        story_service (StoryService): 需求服务实例

    Returns:
        Dict[str, Any]: 包含需求信息的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_story_info，需求IDs: {story_ids}")
        response = await story_service.get_story_info(story_ids)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取需求信息，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取需求信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求信息失败: {str(e)}"
        )


@router.post("/zentao_get_story_detail", response_model=Dict[str, Any])
async def zentao_get_story_detail(
    story_id: int,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """
    根据需求ID查询需求详情

    Args:
        story_id (int): 需求ID，用于查询需求的详细信息
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        story_service (StoryService): 需求服务实例

    Returns:
        Dict[str, Any]: 包含需求详情的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_story_detail，需求ID: {story_id}")
        response = await story_service.get_story_by_id(story_id)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取需求 {story_id} 详情，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取需求详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求详情失败: {str(e)}"
        )


@router.post("/zentao_get_stories_end_info", response_model=Dict[str, Any])
async def zentao_get_stories_end_info(
    story_ids: List[str],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """
    获取需求结束信息

    Args:
        story_ids (List[str]): 需求ID列表，用于批量查询需求结束信息
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        story_service (StoryService): 需求服务实例

    Returns:
        Dict[str, Any]: 包含需求结束信息的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_stories_end_info，需求IDs: {story_ids}")
        response = await story_service.get_stories_end_info(story_ids)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功获取需求结束信息，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取需求结束信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取需求结束信息失败: {str(e)}"
        )


@router.post("/zentao_check_story_exists", response_model=Dict[str, Any])
async def zentao_check_story_exists(
    story_ids: List[str],
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """
    检查需求是否存在

    Args:
        story_ids (List[str]): 需求ID列表，用于批量检查需求是否存在
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        story_service (StoryService): 需求服务实例

    Returns:
        Dict[str, Any]: 包含需求存在性检查结果的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_check_story_exists，需求IDs: {story_ids}")
        response = await story_service.check_stories_exist(story_ids)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功检查需求存在性，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查需求存在性失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"检查需求存在性失败: {str(e)}"
        )


@router.post("/zentao_get_stories_by_time", response_model=Dict[str, Any])
async def zentao_get_stories_by_time(
    status: str,
    start_date: str,
    end_date: str,
    user_identifier: str = Depends(verify_api_key),
    db: Session = Depends(get_db),
    story_service: StoryService = Depends(get_story_service)
):
    """
    根据时间段和状态获取需求列表

    Args:
        status (str): 需求状态，用于筛选特定状态的需求
        start_date (str): 开始日期，格式为 YYYY-MM-DD HH:MM:SS
        end_date (str): 结束日期，格式为 YYYY-MM-DD HH:MM:SS
        user_identifier (str): API Key验证后的用户标识
        db (Session): 数据库会话
        story_service (StoryService): 需求服务实例

    Returns:
        Dict[str, Any]: 包含需求列表的响应数据

    Raises:
        HTTPException: 当API调用失败或响应为空时抛出异常
    """
    try:
        logger.info(f"用户 {user_identifier} 调用 zentao_get_stories_by_time，状态: {status}")
        response = await story_service.get_stories_by_time(status, start_date, end_date)

        if not response:
            raise HTTPException(
                status_code=status.HTTP_502_BAD_GATEWAY,
                detail="禅道API响应为空"
            )

        logger.info(f"成功按时间查询需求，响应码: {response.get('rsCode')}")
        return response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"按时间查询需求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"按时间查询需求失败: {str(e)}"
        )
