"""
基础服务类

提供服务层的通用功能和依赖注入支持
"""

import logging
from typing import Any
from functools import wraps
from app.zentao_engine.client import ZentaoService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class BaseService:
    """服务层基类"""
    
    def __init__(self, zentao_service: ZentaoService):
        """
        初始化基础服务
        
        Args:
            zentao_service: 禅道服务实例
        """
        self.zentao_service = zentao_service
        self.logger = logger.getChild(self.__class__.__name__)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        pass
    
    def handle_api_call(self, operation_name: str):
        """统一的API调用异常处理装饰器"""
        def decorator(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                try:
                    self.logger.info(f"执行操作: {operation_name}")
                    response = await func(*args, **kwargs)
                    
                    if not response:
                        raise ZentaoEngineError("禅道API响应为空")
                    
                    return response
                    
                except Exception as e:
                    self.logger.error(f"{operation_name}失败: {e}")
                    raise ZentaoEngineError(f"{operation_name}失败: {str(e)}")
            
            return wrapper
        return decorator