"""
需求管理服务

封装需求相关的业务逻辑
"""

import logging
from typing import Dict, Any, List
from .base import BaseService
from app.zentao_engine.exceptions import ZentaoEngineError

logger = logging.getLogger(__name__)


class StoryService(BaseService):
    """需求管理服务类"""
    
    async def get_stories_by_project(self, project_id: int) -> Dict[str, Any]:
        """
        根据项目ID获取需求列表
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据项目ID {project_id} 获取需求列表")
            
            response = await self.zentao_service.get_stories_by_project(project_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取项目 {project_id} 需求列表，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据项目ID获取需求列表失败: {e}")
            raise ZentaoEngineError(f"根据项目ID获取需求列表失败: {str(e)}")
    
    async def get_story_info(self, story_id: int) -> Dict[str, Any]:
        """
        根据需求ID获取需求详情
        
        Args:
            story_id: 需求ID
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API根据需求ID {story_id} 获取需求详情")
            
            response = await self.zentao_service.get_story_info(story_id)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取需求 {story_id} 详情，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"根据需求ID获取需求详情失败: {e}")
            raise ZentaoEngineError(f"根据需求ID获取需求详情失败: {str(e)}")
    
    async def get_stories_end_info(self, story_ids: List[int]) -> Dict[str, Any]:
        """
        获取需求结束信息
        
        Args:
            story_ids: 需求ID列表
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API获取需求结束信息: {story_ids}")
            
            response = await self.zentao_service.get_stories_end_info(story_ids)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功获取需求结束信息，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"获取需求结束信息失败: {e}")
            raise ZentaoEngineError(f"获取需求结束信息失败: {str(e)}")
    
    async def check_stories_exist(self, story_ids: List[int]) -> Dict[str, Any]:
        """
        检查需求是否存在
        
        Args:
            story_ids: 需求ID列表
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API检查需求存在性: {story_ids}")
            
            response = await self.zentao_service.check_stories_exist(story_ids)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功检查需求存在性，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"检查需求存在性失败: {e}")
            raise ZentaoEngineError(f"检查需求存在性失败: {str(e)}")
    
    async def get_stories_by_time_range(self, start_time: str, end_time: str, project_id: int = None) -> Dict[str, Any]:
        """
        按时间范围查询需求
        
        Args:
            start_time: 开始时间
            end_time: 结束时间
            project_id: 项目ID（可选）
            
        Returns:
            Dict: 禅道API原始响应数据
        """
        try:
            self.logger.info(f"调用禅道API按时间范围查询需求: {start_time} - {end_time}")
            
            response = await self.zentao_service.get_stories_by_time_range(start_time, end_time)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            self.logger.info(f"成功按时间范围查询需求，响应码: {response.get('rsCode')}")
            return response
            
        except Exception as e:
            self.logger.error(f"按时间范围查询需求失败: {e}")
            raise ZentaoEngineError(f"按时间范围查询需求失败: {str(e)}")
    
    async def get_story_completion_rate(self, project_id: int) -> Dict[str, Any]:
        """
        获取项目需求完成率分析
        
        Args:
            project_id: 项目ID
            
        Returns:
            Dict: 需求完成率分析数据
        """
        try:
            self.logger.info(f"分析项目 {project_id} 需求完成率")
            
            # 获取项目所有需求
            stories_response = await self.zentao_service.get_stories_by_project(project_id)
            
            if not stories_response or stories_response.get("rsCode") != "00000000":
                raise ZentaoEngineError(f"获取项目需求失败: {stories_response}")
            
            stories = stories_response.get("data", [])
            
            # 统计需求状态
            total_count = len(stories)
            completed_count = 0
            in_progress_count = 0
            pending_count = 0
            
            status_distribution = {}
            
            for story in stories:
                status = story.get("status", "")
                status_distribution[status] = status_distribution.get(status, 0) + 1
                
                if status in ["closed", "done"]:
                    completed_count += 1
                elif status in ["active", "developing"]:
                    in_progress_count += 1
                else:
                    pending_count += 1
            
            completion_rate = (completed_count / total_count * 100) if total_count > 0 else 0
            
            result = {
                "success": True,
                "project_id": project_id,
                "total_stories": total_count,
                "completed_stories": completed_count,
                "in_progress_stories": in_progress_count,
                "pending_stories": pending_count,
                "completion_rate": round(completion_rate, 2),
                "status_distribution": status_distribution
            }
            
            self.logger.info(f"项目 {project_id} 需求完成率分析完成: {completion_rate}%")
            return result
            
        except Exception as e:
            self.logger.error(f"需求完成率分析失败: {e}")
            raise ZentaoEngineError(f"需求完成率分析失败: {str(e)}")
    
    async def analyze_story_workload(self, story_ids: List[int] = None, project_id: int = None, include_completed: bool = True) -> Dict[str, Any]:
        """
        分析需求工时统计
        
        Args:
            story_ids: 需求ID列表
            project_id: 项目ID
            include_completed: 是否包含已完成需求
            
        Returns:
            Dict: 工时分析结果
        """
        try:
            self.logger.info(f"分析需求工时统计，需求ID: {story_ids}, 项目ID: {project_id}")
            
            stories_to_analyze = []
            
            if story_ids:
                # 批量获取指定需求
                for story_id in story_ids:
                    story_response = await self.zentao_service.get_story_by_id(story_id)
                    if story_response and story_response.get("rsCode") == "00000000":
                        story_data = story_response.get("body")
                        if story_data:
                            stories_to_analyze.append(story_data)
            elif project_id:
                # 获取项目需求
                stories_response = await self.zentao_service.get_stories_by_project(project_id)
                if stories_response and stories_response.get("rsCode") == "00000000":
                    stories_to_analyze = stories_response.get("body", [])
            else:
                raise ZentaoEngineError("必须提供story_ids或project_id参数")
            
            if not stories_to_analyze:
                return {
                    "success": False,
                    "message": "没有找到需要分析的需求",
                    "total_stories": 0,
                    "analysis": {}
                }
            
            # 进行工时分析
            total_stories = len(stories_to_analyze)
            total_estimate = 0.0
            completed_estimate = 0.0
            active_estimate = 0.0
            
            status_workload = {}
            stage_workload = {}
            priority_workload = {}
            
            for story in stories_to_analyze:
                estimate = float(story.get("estimate", 0.0))
                status = story.get("status", "active")
                stage = story.get("stage", "wait")
                priority = story.get("pri", 3)
                
                total_estimate += estimate
                
                # 状态统计
                if status not in status_workload:
                    status_workload[status] = {"count": 0, "estimate": 0.0}
                status_workload[status]["count"] += 1
                status_workload[status]["estimate"] += estimate
                
                if status == "closed":
                    completed_estimate += estimate
                else:
                    active_estimate += estimate
                
                # 阶段统计
                if stage not in stage_workload:
                    stage_workload[stage] = {"count": 0, "estimate": 0.0}
                stage_workload[stage]["count"] += 1
                stage_workload[stage]["estimate"] += estimate
                
                # 优先级统计
                if priority not in priority_workload:
                    priority_workload[priority] = {"count": 0, "estimate": 0.0}
                priority_workload[priority]["count"] += 1
                priority_workload[priority]["estimate"] += estimate
            
            # 计算完成率和平均工时
            completion_rate = (completed_estimate / total_estimate * 100) if total_estimate > 0 else 0
            avg_estimate = total_estimate / total_stories if total_stories > 0 else 0
            
            result = {
                "success": True,
                "message": f"工时分析完成，共分析 {total_stories} 个需求",
                "total_stories": total_stories,
                "analysis": {
                    "total_estimate_hours": round(total_estimate, 2),
                    "completed_estimate_hours": round(completed_estimate, 2),
                    "active_estimate_hours": round(active_estimate, 2),
                    "average_estimate_hours": round(avg_estimate, 2),
                    "completion_rate": round(completion_rate, 2),
                    "status_breakdown": {
                        status: {
                            "count": data["count"],
                            "estimate_hours": round(data["estimate"], 2),
                            "percentage": round((data["estimate"] / total_estimate * 100), 2) if total_estimate > 0 else 0
                        }
                        for status, data in status_workload.items()
                    },
                    "stage_breakdown": {
                        stage: {
                            "count": data["count"],
                            "estimate_hours": round(data["estimate"], 2),
                            "percentage": round((data["estimate"] / total_estimate * 100), 2) if total_estimate > 0 else 0
                        }
                        for stage, data in stage_workload.items()
                    },
                    "priority_breakdown": {
                        f"priority_{priority}": {
                            "count": data["count"],
                            "estimate_hours": round(data["estimate"], 2),
                            "percentage": round((data["estimate"] / total_estimate * 100), 2) if total_estimate > 0 else 0
                        }
                        for priority, data in priority_workload.items()
                    }
                }
            }
            
            self.logger.info(f"需求工时分析完成: 总工时 {result['analysis']['total_estimate_hours']} 小时")
            return result
            
        except Exception as e:
            self.logger.error(f"分析需求工时统计失败: {e}")
            raise ZentaoEngineError(f"分析需求工时统计失败: {str(e)}")
    
    async def batch_query_stories(self, story_ids: List[int]) -> Dict[str, Any]:
        """
        批量查询需求信息
        
        Args:
            story_ids: 需求ID列表
            
        Returns:
            Dict: 批量查询结果
        """
        try:
            self.logger.info(f"批量查询需求信息: {story_ids}")
            
            stories = []
            failed_ids = []
            
            for story_id in story_ids:
                try:
                    story_response = await self.zentao_service.get_story_by_id(story_id)
                    if story_response and story_response.get("rsCode") == "00000000":
                        story_data = story_response.get("body")
                        if story_data:
                            stories.append(story_data)
                        else:
                            failed_ids.append(story_id)
                    else:
                        failed_ids.append(story_id)
                except Exception as e:
                    self.logger.warning(f"查询需求 {story_id} 失败: {e}")
                    failed_ids.append(story_id)
            
            result = {
                "success": True,
                "message": f"批量查询完成，成功 {len(stories)} 个，失败 {len(failed_ids)} 个",
                "total_requested": len(story_ids),
                "total_found": len(stories),
                "total_failed": len(failed_ids),
                "failed_ids": failed_ids,
                "stories": stories
            }
            
            self.logger.info(f"批量查询需求完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"批量查询需求信息失败: {e}")
            raise ZentaoEngineError(f"批量查询需求信息失败: {str(e)}")
    
    async def validate_story_existence(self, story_ids: List[int]) -> Dict[str, Any]:
        """
        验证需求是否存在
        
        Args:
            story_ids: 需求ID列表
            
        Returns:
            Dict: 验证结果
        """
        try:
            self.logger.info(f"验证需求存在性: {story_ids}")
            
            # 转换为字符串列表（API要求）
            story_ids_str = [str(sid) for sid in story_ids]
            response = await self.zentao_service.check_stories_exist(story_ids_str)
            
            if not response:
                raise ZentaoEngineError("禅道API响应为空")
            
            if response.get("rsCode") == "00000000":
                existing_stories = response.get("body", [])
                existing_ids = [int(story.get("id", 0)) for story in existing_stories if story.get("id")]
                
                missing_ids = [sid for sid in story_ids if sid not in existing_ids]
                
                result = {
                    "success": True,
                    "message": f"验证完成，存在 {len(existing_ids)} 个，缺失 {len(missing_ids)} 个",
                    "total_checked": len(story_ids),
                    "existing_count": len(existing_ids),
                    "missing_count": len(missing_ids),
                    "existing_ids": existing_ids,
                    "missing_ids": missing_ids,
                    "validation_details": [
                        {
                            "story_id": sid,
                            "exists": sid in existing_ids
                        }
                        for sid in story_ids
                    ]
                }
            else:
                result = {
                    "success": False,
                    "message": f"验证失败: {response.get('msg', '未知错误')}",
                    "error": response
                }
            
            self.logger.info(f"需求存在性验证完成: {result['message']}")
            return result
            
        except Exception as e:
            self.logger.error(f"验证需求是否存在失败: {e}")
            raise ZentaoEngineError(f"验证需求是否存在失败: {str(e)}")