"""
会话管理服务
"""
import secrets
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timedelta, timezone

from app.models import UserSession, User
from app.core.exceptions import SessionExpiredError, InvalidTokenError


class SessionService:
    """会话管理服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.default_session_duration = timedelta(hours=8)  # 默认8小时
        self.max_sessions_per_user = 5  # 每个用户最多5个活跃会话
    
    def create_session(
        self,
        user: User,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        remember_me: bool = False
    ) -> UserSession:
        """
        创建新会话
        
        Args:
            user: 用户对象
            ip_address: IP地址
            user_agent: 用户代理
            remember_me: 是否记住登录状态
            
        Returns:
            UserSession: 创建的会话对象
        """
        # 清理过期会话
        self._cleanup_expired_sessions(user.id)
        
        # 检查活跃会话数量
        active_sessions = self._get_active_sessions(user.id)
        if len(active_sessions) >= self.max_sessions_per_user:
            # 删除最旧的会话
            oldest_session = min(active_sessions, key=lambda s: s.created_at)
            self.invalidate_session(oldest_session.session_token)
        
        # 生成会话令牌
        session_token = self._generate_session_token()
        
        # 设置过期时间
        if remember_me:
            expires_at = datetime.now(timezone.utc) + timedelta(days=30)  # 记住登录30天
        else:
            expires_at = datetime.now(timezone.utc) + self.default_session_duration
        
        # 创建会话
        session = UserSession(
            user_id=user.id,
            session_token=session_token,
            ip_address=ip_address,
            user_agent=user_agent,
            expires_at=expires_at,
            last_activity=datetime.now(timezone.utc)
        )
        
        self.db.add(session)
        self.db.commit()
        self.db.refresh(session)
        
        return session
    
    def get_session(self, session_token: str) -> Optional[UserSession]:
        """
        根据令牌获取会话
        
        Args:
            session_token: 会话令牌
            
        Returns:
            Optional[UserSession]: 会话对象，如果不存在返回None
        """
        return self.db.query(UserSession).filter(
            UserSession.session_token == session_token
        ).first()
    
    def validate_session(self, session_token: str) -> UserSession:
        """
        验证会话有效性
        
        Args:
            session_token: 会话令牌
            
        Returns:
            UserSession: 有效的会话对象
            
        Raises:
            InvalidTokenError: 无效的令牌
            SessionExpiredError: 会话已过期
        """
        session = self.get_session(session_token)
        
        if not session:
            raise InvalidTokenError("无效的会话令牌")
        
        if not session.is_active:
            raise SessionExpiredError("会话已被禁用")
        
        if session.is_expired:
            # 自动清理过期会话
            self.invalidate_session(session_token)
            raise SessionExpiredError("会话已过期")
        
        # 更新最后活动时间
        session.last_activity = datetime.now(timezone.utc)
        self.db.commit()
        
        return session
    
    def refresh_session(self, session_token: str) -> UserSession:
        """
        刷新会话过期时间
        
        Args:
            session_token: 会话令牌
            
        Returns:
            UserSession: 刷新后的会话对象
        """
        session = self.validate_session(session_token)
        
        # 延长过期时间
        session.expires_at = datetime.now(timezone.utc) + self.default_session_duration
        session.last_activity = datetime.now(timezone.utc)
        
        self.db.commit()
        self.db.refresh(session)
        
        return session
    
    def invalidate_session(self, session_token: str) -> bool:
        """
        使会话失效
        
        Args:
            session_token: 会话令牌
            
        Returns:
            bool: 是否成功使会话失效
        """
        session = self.get_session(session_token)
        
        if session:
            session.is_active = False
            self.db.commit()
            return True
        
        return False
    
    def invalidate_user_sessions(self, user_id: int, except_token: Optional[str] = None) -> int:
        """
        使用户的所有会话失效
        
        Args:
            user_id: 用户ID
            except_token: 排除的会话令牌（通常是当前会话）
            
        Returns:
            int: 失效的会话数量
        """
        query = self.db.query(UserSession).filter(
            UserSession.user_id == user_id,
            UserSession.is_active == True
        )
        
        if except_token:
            query = query.filter(UserSession.session_token != except_token)
        
        sessions = query.all()
        
        for session in sessions:
            session.is_active = False
        
        self.db.commit()
        
        return len(sessions)
    
    def get_user_sessions(self, user_id: int, active_only: bool = True) -> list[UserSession]:
        """
        获取用户的所有会话
        
        Args:
            user_id: 用户ID
            active_only: 是否只返回活跃会话
            
        Returns:
            list[UserSession]: 会话列表
        """
        query = self.db.query(UserSession).filter(UserSession.user_id == user_id)
        
        if active_only:
            query = query.filter(UserSession.is_active == True)
        
        return query.order_by(UserSession.last_activity.desc()).all()
    
    def cleanup_expired_sessions(self) -> int:
        """
        清理所有过期会话
        
        Returns:
            int: 清理的会话数量
        """
        now = datetime.now(timezone.utc)
        
        expired_sessions = self.db.query(UserSession).filter(
            UserSession.expires_at < now
        ).all()
        
        for session in expired_sessions:
            session.is_active = False
        
        self.db.commit()
        
        return len(expired_sessions)
    
    def get_session_info(self, session_token: str) -> Optional[Dict[str, Any]]:
        """
        获取会话详细信息
        
        Args:
            session_token: 会话令牌
            
        Returns:
            Optional[Dict[str, Any]]: 会话信息，如果不存在返回None
        """
        session = self.get_session(session_token)
        
        if not session:
            return None
        
        return {
            "session_id": session.id,
            "user_id": session.user_id,
            "username": session.user.username if session.user else None,
            "ip_address": session.ip_address,
            "user_agent": session.user_agent,
            "created_at": session.created_at,
            "last_activity": session.last_activity,
            "expires_at": session.expires_at,
            "is_active": session.is_active,
            "is_expired": session.is_expired
        }
    
    def update_session_activity(
        self, 
        session_token: str, 
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> bool:
        """
        更新会话活动信息
        
        Args:
            session_token: 会话令牌
            ip_address: 新的IP地址
            user_agent: 新的用户代理
            
        Returns:
            bool: 是否更新成功
        """
        session = self.get_session(session_token)
        
        if not session or not session.is_active:
            return False
        
        session.last_activity = datetime.now(timezone.utc)
        
        if ip_address:
            session.ip_address = ip_address
        
        if user_agent:
            session.user_agent = user_agent
        
        self.db.commit()
        
        return True
    
    def _generate_session_token(self) -> str:
        """生成安全的会话令牌"""
        return secrets.token_urlsafe(32)
    
    def _get_active_sessions(self, user_id: int) -> list[UserSession]:
        """获取用户的活跃会话"""
        return self.db.query(UserSession).filter(
            UserSession.user_id == user_id,
            UserSession.is_active == True
        ).all()
    
    def _cleanup_expired_sessions(self, user_id: int):
        """清理指定用户的过期会话"""
        now = datetime.now(timezone.utc)
        
        expired_sessions = self.db.query(UserSession).filter(
            UserSession.user_id == user_id,
            UserSession.expires_at < now
        ).all()
        
        for session in expired_sessions:
            session.is_active = False
        
        if expired_sessions:
            self.db.commit()