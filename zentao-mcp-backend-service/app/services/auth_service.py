"""
Authentication service for admin users
"""
import secrets
from datetime import datetime, timedelta, timezone
from typing import Op<PERSON>, <PERSON><PERSON>
from sqlalchemy.orm import Session
from app.models.admin import User, UserType
from app.services.password_service import PasswordService
from app.core.config import settings


def create_admin_user(
    db: Session,
    username: str,
    password: str,
    email: str,
    user_type: UserType = UserType.ADMIN
) -> User:
    """Create a new admin user"""
    password_hash = PasswordService.hash_password(password)

    admin_user = User(
        username=username,
        password_hash=password_hash,
        email=email,
        user_type=user_type
    )

    db.add(admin_user)
    db.commit()
    db.refresh(admin_user)
    
    return admin_user


def authenticate_admin(db: Session, username: str, password: str) -> Optional[User]:
    """Authenticate admin user"""
    admin_user = db.query(User).filter(
        User.username == username,
        User.is_active == True
    ).first()
    
    if not admin_user:
        return None
    
    if not PasswordService.verify_password(password, admin_user.password_hash):
        return None
    
    # Update last login
    admin_user.last_login = datetime.now(timezone.utc)
    db.commit()
    
    return admin_user


def get_admin_by_username(db: Session, username: str) -> Optional[User]:
    """Get admin user by username"""
    return db.query(User).filter(User.username == username).first()


def initialize_default_admin(db: Session) -> Tuple[User, bool]:
    """
    Initialize default admin user from environment variables
    
    Returns:
        Tuple[User, bool]: (admin_user, was_created)
    """
    # Check if admin already exists
    existing_admin = get_admin_by_username(db, settings.admin_username)
    if existing_admin:
        return existing_admin, False
    
    # Create default admin
    admin_user = create_admin_user(
        db=db,
        username=settings.admin_username,
        password=settings.admin_password,
        email=settings.admin_email,
        user_type=UserType.ADMIN
    )
    
    return admin_user, True


def generate_session_token() -> str:
    """Generate a secure session token"""
    return secrets.token_urlsafe(32)