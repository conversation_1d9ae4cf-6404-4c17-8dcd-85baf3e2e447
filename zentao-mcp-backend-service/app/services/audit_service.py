"""
审计日志服务
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc
from datetime import datetime, timedelta, timezone
import json

from app.models import AuditLog, User


class AuditService:
    """审计日志服务类"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def log_action(
        self,
        user_id: int,
        action: str,
        resource_type: str,
        resource_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None
    ) -> AuditLog:
        """
        记录操作日志
        
        Args:
            user_id: 操作用户ID
            action: 操作类型
            resource_type: 资源类型
            resource_id: 资源ID
            details: 详细信息
            ip_address: IP地址
            user_agent: 用户代理
            
        Returns:
            AuditLog: 创建的审计日志对象
        """
        # 脱敏处理敏感信息
        if details:
            details = self._sanitize_details(details)
        
        log = AuditLog(
            user_id=user_id,
            action=action,
            resource_type=resource_type,
            resource_id=resource_id,
            details=details,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        self.db.add(log)
        self.db.commit()
        self.db.refresh(log)
        
        return log
    
    def get_logs(
        self,
        user_id: Optional[int] = None,
        action: Optional[str] = None,
        resource_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[AuditLog]:
        """
        查询审计日志
        
        Args:
            user_id: 用户ID筛选
            action: 操作类型筛选
            resource_type: 资源类型筛选
            start_date: 开始时间
            end_date: 结束时间
            skip: 跳过的记录数
            limit: 返回的记录数限制
            
        Returns:
            List[AuditLog]: 审计日志列表
        """
        query = self.db.query(AuditLog)
        
        # 筛选条件
        if user_id:
            query = query.filter(AuditLog.user_id == user_id)
        
        if action:
            query = query.filter(AuditLog.action == action)
        
        if resource_type:
            query = query.filter(AuditLog.resource_type == resource_type)
        
        if start_date:
            query = query.filter(AuditLog.created_at >= start_date)
        
        if end_date:
            query = query.filter(AuditLog.created_at <= end_date)
        
        return query.order_by(desc(AuditLog.created_at)).offset(skip).limit(limit).all()
    
    def get_user_activity(self, user_id: int, days: int = 30) -> List[AuditLog]:
        """获取用户活动记录"""
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        return self.get_logs(user_id=user_id, start_date=start_date)
    
    def get_security_events(self, days: int = 7) -> List[AuditLog]:
        """获取安全相关事件"""
        start_date = datetime.now(timezone.utc) - timedelta(days=days)
        security_actions = [
            'login_failed', 'login_success', 'logout',
            'password_change', 'password_reset',
            'user_locked', 'user_unlocked',
            'create_user', 'delete_user',
            'permission_denied'
        ]
        
        query = self.db.query(AuditLog).filter(
            and_(
                AuditLog.action.in_(security_actions),
                AuditLog.created_at >= start_date
            )
        )
        
        return query.order_by(desc(AuditLog.created_at)).all()
    
    def get_failed_login_attempts(self, ip_address: str, hours: int = 1) -> int:
        """获取指定IP的失败登录次数"""
        start_time = datetime.now(timezone.utc) - timedelta(hours=hours)
        
        count = self.db.query(AuditLog).filter(
            and_(
                AuditLog.action == 'login_failed',
                AuditLog.ip_address == ip_address,
                AuditLog.created_at >= start_time
            )
        ).count()
        
        return count
    
    def cleanup_old_logs(self, days: int = 365) -> int:
        """清理旧的审计日志"""
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
        
        deleted_count = self.db.query(AuditLog).filter(
            AuditLog.created_at < cutoff_date
        ).delete()
        
        self.db.commit()
        return deleted_count
    
    def export_logs(
        self,
        start_date: datetime,
        end_date: datetime,
        format: str = 'json'
    ) -> str:
        """导出审计日志"""
        logs = self.get_logs(start_date=start_date, end_date=end_date, limit=10000)
        
        if format.lower() == 'json':
            return self._export_to_json(logs)
        elif format.lower() == 'csv':
            return self._export_to_csv(logs)
        else:
            raise ValueError(f"不支持的导出格式: {format}")
    
    def _sanitize_details(self, details: Dict[str, Any]) -> Dict[str, Any]:
        """脱敏处理敏感信息"""
        sensitive_keys = [
            'password', 'password_hash', 'token', 'secret',
            'api_key', 'private_key', 'access_token'
        ]
        
        sanitized = {}
        for key, value in details.items():
            if any(sensitive_key in key.lower() for sensitive_key in sensitive_keys):
                sanitized[key] = "***REDACTED***"
            else:
                sanitized[key] = value
        
        return sanitized
    
    def _export_to_json(self, logs: List[AuditLog]) -> str:
        """导出为JSON格式"""
        data = []
        for log in logs:
            data.append({
                'id': log.id,
                'user_id': log.user_id,
                'action': log.action,
                'resource_type': log.resource_type,
                'resource_id': log.resource_id,
                'details': log.details,
                'ip_address': log.ip_address,
                'user_agent': log.user_agent,
                'created_at': log.created_at.isoformat()
            })
        
        return json.dumps(data, indent=2, ensure_ascii=False)
    
    def _export_to_csv(self, logs: List[AuditLog]) -> str:
        """导出为CSV格式"""
        import csv
        import io
        
        output = io.StringIO()
        writer = csv.writer(output)
        
        # 写入标题行
        writer.writerow([
            'ID', '用户ID', '操作', '资源类型', '资源ID',
            'IP地址', '用户代理', '创建时间', '详细信息'
        ])
        
        # 写入数据行
        for log in logs:
            writer.writerow([
                log.id,
                log.user_id,
                log.action,
                log.resource_type,
                log.resource_id or '',
                log.ip_address or '',
                log.user_agent or '',
                log.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                json.dumps(log.details, ensure_ascii=False) if log.details else ''
            ])
        
        return output.getvalue()