"""
密码管理服务
"""
import re
import bcrypt
from typing import Optional
from datetime import datetime, timedelta, timezone


class PasswordService:
    """密码管理服务类"""
    
    @staticmethod
    def hash_password(password: str) -> str:
        """
        使用 bcrypt 加密密码
        
        Args:
            password: 明文密码
            
        Returns:
            str: 加密后的密码哈希
        """
        # 生成盐并加密密码
        salt = bcrypt.gensalt()
        password_hash = bcrypt.hashpw(password.encode('utf-8'), salt)
        return password_hash.decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, password_hash: str) -> bool:
        """
        验证密码
        
        Args:
            password: 明文密码
            password_hash: 存储的密码哈希
            
        Returns:
            bool: 密码是否正确
        """
        try:
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except Exception:
            return False
    
    @staticmethod
    def validate_password_strength(password: str) -> tuple[bool, list[str]]:
        """
        验证密码强度
        
        Args:
            password: 待验证的密码
            
        Returns:
            tuple[bool, list[str]]: (是否通过验证, 错误信息列表)
        """
        errors = []
        
        # 长度检查
        if len(password) < 8:
            errors.append("密码长度至少8位")
        
        if len(password) > 128:
            errors.append("密码长度不能超过128位")
        
        # 复杂度检查
        if not re.search(r'[a-z]', password):
            errors.append("密码必须包含小写字母")
        
        if not re.search(r'[A-Z]', password):
            errors.append("密码必须包含大写字母")
        
        if not re.search(r'\d', password):
            errors.append("密码必须包含数字")
        
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            errors.append("密码必须包含特殊字符")
        
        # 常见弱密码检查
        weak_passwords = [
            'password', '12345678', 'qwerty123', 'admin123',
            'password123', '123456789', 'qwertyuiop'
        ]
        
        if password.lower() in weak_passwords:
            errors.append("密码过于简单，请使用更复杂的密码")
        
        return len(errors) == 0, errors
    
    @staticmethod
    def is_password_expired(last_change: Optional[datetime], max_age_days: int = 90) -> bool:
        """
        检查密码是否过期
        
        Args:
            last_change: 上次密码修改时间
            max_age_days: 密码最大有效天数
            
        Returns:
            bool: 密码是否过期
        """
        if last_change is None:
            return True
        
        expiry_date = last_change + timedelta(days=max_age_days)
        return datetime.now(timezone.utc) > expiry_date
    
    @staticmethod
    def generate_temporary_password(length: int = 12) -> str:
        """
        生成临时密码
        
        Args:
            length: 密码长度
            
        Returns:
            str: 生成的临时密码
        """
        import secrets
        import string
        
        # 确保包含各种字符类型
        lowercase = string.ascii_lowercase
        uppercase = string.ascii_uppercase
        digits = string.digits
        special = "!@#$%^&*"
        
        # 至少包含一个每种类型的字符
        password = [
            secrets.choice(lowercase),
            secrets.choice(uppercase),
            secrets.choice(digits),
            secrets.choice(special)
        ]
        
        # 填充剩余长度
        all_chars = lowercase + uppercase + digits + special
        for _ in range(length - 4):
            password.append(secrets.choice(all_chars))
        
        # 打乱顺序
        secrets.SystemRandom().shuffle(password)
        
        return ''.join(password)
    
    @staticmethod
    def is_sha256_hash(password_hash: str) -> bool:
        """
        检查是否为旧的 SHA-256 哈希
        
        Args:
            password_hash: 密码哈希
            
        Returns:
            bool: 是否为 SHA-256 哈希
        """
        # SHA-256 哈希长度为64个十六进制字符
        return len(password_hash) == 64 and all(c in '0123456789abcdef' for c in password_hash.lower())
    
    @staticmethod
    def migrate_sha256_to_bcrypt(password: str, old_hash: str) -> Optional[str]:
        """
        将 SHA-256 哈希迁移到 bcrypt
        
        Args:
            password: 用户输入的明文密码
            old_hash: 旧的 SHA-256 哈希
            
        Returns:
            Optional[str]: 新的 bcrypt 哈希，如果验证失败返回 None
        """
        import hashlib
        
        # 验证旧密码
        sha256_hash = hashlib.sha256(password.encode()).hexdigest()
        if sha256_hash == old_hash:
            # 验证通过，生成新的 bcrypt 哈希
            return PasswordService.hash_password(password)
        
        return None