"""
权限验证服务
"""
from typing import Optional, List, Dict, Any
from enum import Enum
from functools import wraps
from sqlalchemy.orm import Session

from app.models import User, UserType, APIKey
from app.core.exceptions import PermissionDeniedError, UserNotFoundError


class Permission(Enum):
    """权限枚举"""
    # 用户管理权限
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_LIST = "user:list"
    
    # 管理员权限
    ADMIN_CREATE = "admin:create"
    ADMIN_DELETE = "admin:delete"
    ADMIN_MANAGE = "admin:manage"
    
    # API Key权限
    APIKEY_CREATE = "apikey:create"
    APIKEY_READ = "apikey:read"
    APIKEY_UPDATE = "apikey:update"
    APIKEY_DELETE = "apikey:delete"
    APIKEY_LIST = "apikey:list"
    
    # 系统管理权限
    SYSTEM_CONFIG = "system:config"
    SYSTEM_AUDIT = "system:audit"
    SYSTEM_BACKUP = "system:backup"
    
    # 禅道相关权限
    ZENTAO_READ = "zentao:read"
    ZENTAO_WRITE = "zentao:write"
    ZENTAO_ADMIN = "zentao:admin"


class PermissionService:
    """权限验证服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self._init_permission_matrix()
    
    def _init_permission_matrix(self):
        """初始化权限矩阵"""
        self.permission_matrix = {
            UserType.USER: [
                # 普通用户只能管理自己的API Key
                Permission.APIKEY_READ,
                Permission.ZENTAO_READ,
                Permission.ZENTAO_WRITE,
            ],
            UserType.ADMIN: [
                # 管理员拥有所有权限
                *list(Permission)
            ]
        }
    
    def has_permission(self, user: User, permission: Permission) -> bool:
        """
        检查用户是否有指定权限
        
        Args:
            user: 用户对象
            permission: 权限枚举
            
        Returns:
            bool: 是否有权限
        """
        if not user or not user.is_active:
            return False
        
        # 检查账户是否被锁定
        if user.is_locked:
            return False
        
        # 获取用户权限列表
        user_permissions = self.permission_matrix.get(user.user_type, [])
        
        return permission in user_permissions
    
    def check_permission(self, user: User, permission: Permission):
        """
        检查权限，如果没有权限则抛出异常
        
        Args:
            user: 用户对象
            permission: 权限枚举
            
        Raises:
            PermissionDeniedError: 权限不足
        """
        if not self.has_permission(user, permission):
            raise PermissionDeniedError(f"用户 {user.username} 没有权限: {permission.value}")
    
    def can_manage_user(self, operator: User, target_user: User) -> bool:
        """
        检查操作者是否可以管理目标用户
        
        Args:
            operator: 操作者
            target_user: 目标用户
            
        Returns:
            bool: 是否可以管理
        """
        # 不能管理自己
        if operator.id == target_user.id:
            return False
        
        # 管理员可以管理所有其他用户
        if operator.user_type == UserType.ADMIN:
            return True
        
        # 普通用户不能管理任何人
        return False
    
    def can_access_resource(
        self, 
        user: User, 
        resource_type: str, 
        resource_id: Optional[str] = None,
        action: str = "read"
    ) -> bool:
        """
        检查用户是否可以访问指定资源
        
        Args:
            user: 用户对象
            resource_type: 资源类型
            resource_id: 资源ID
            action: 操作类型
            
        Returns:
            bool: 是否可以访问
        """
        # 构造权限字符串
        permission_str = f"{resource_type}:{action}"
        
        try:
            permission = Permission(permission_str)
            return self.has_permission(user, permission)
        except ValueError:
            # 如果权限不存在，默认拒绝
            return False
    
    def get_user_permissions(self, user: User) -> List[str]:
        """
        获取用户的所有权限
        
        Args:
            user: 用户对象
            
        Returns:
            List[str]: 权限列表
        """
        if not user or not user.is_active:
            return []
        
        user_permissions = self.permission_matrix.get(user.user_type, [])
        return [perm.value for perm in user_permissions]
    
    def filter_accessible_users(
        self, 
        operator: User, 
        users: List[User]
    ) -> List[User]:
        """
        过滤用户可以访问的用户列表
        
        Args:
            operator: 操作者
            users: 用户列表
            
        Returns:
            List[User]: 可访问的用户列表
        """
        if operator.user_type == UserType.ADMIN:
            # 管理员可以看到所有用户
            return users
        else:
            # 普通用户只能看到自己
            return [u for u in users if u.id == operator.id]
    
    def validate_api_key_permissions(
        self, 
        api_key: APIKey, 
        required_permission: str
    ) -> bool:
        """
        验证API Key是否有指定权限
        
        Args:
            api_key: API Key对象
            required_permission: 需要的权限
            
        Returns:
            bool: 是否有权限
        """
        if not api_key or not api_key.is_active or api_key.is_expired():
            return False
        
        # 检查API Key的权限列表
        if api_key.permissions:
            return required_permission in api_key.permissions
        
        # 如果没有设置权限列表，根据用户类型判断
        if api_key.user:
            try:
                permission = Permission(required_permission)
                return self.has_permission(api_key.user, permission)
            except ValueError:
                return False
        
        return False


def require_permission(permission: Permission):
    """
    权限装饰器
    
    Args:
        permission: 需要的权限
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            # 从参数中获取用户对象
            user = None
            db = None
            
            # 尝试从不同位置获取用户和数据库连接
            for arg in args:
                if isinstance(arg, User):
                    user = arg
                elif hasattr(arg, 'db') and hasattr(arg.db, 'query'):
                    db = arg.db
            
            if 'current_user' in kwargs:
                user = kwargs['current_user']
            
            if 'db' in kwargs:
                db = kwargs['db']
            
            if not user:
                raise PermissionDeniedError("未找到用户信息")
            
            if not db:
                raise PermissionDeniedError("未找到数据库连接")
            
            # 检查权限
            permission_service = PermissionService(db)
            permission_service.check_permission(user, permission)
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator


def require_user_type(*allowed_types: UserType):
    """
    用户类型装饰器
    
    Args:
        allowed_types: 允许的用户类型
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            user = None
            
            # 尝试从参数中获取用户对象
            for arg in args:
                if isinstance(arg, User):
                    user = arg
                    break
            
            if 'current_user' in kwargs:
                user = kwargs['current_user']
            
            if not user:
                raise PermissionDeniedError("未找到用户信息")
            
            if user.user_type not in allowed_types:
                allowed_names = [t.value for t in allowed_types]
                raise PermissionDeniedError(f"需要用户类型: {', '.join(allowed_names)}")
            
            return func(*args, **kwargs)
        
        return wrapper
    return decorator