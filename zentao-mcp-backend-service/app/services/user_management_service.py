"""
用户管理服务
"""
from typing import Optional, List, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime, timedelta, timezone

from app.models import User, UserType, APIKey, UserSession
from app.schemas.users import UserCreateRequest, UserUpdateRequest
from app.services.password_service import PasswordService
from app.services.audit_service import AuditService
from app.core.exceptions import (
    UserNotFoundError,
    PermissionDeniedError,
    LastAdminError,
    WeakPasswordError,
    UserAlreadyExistsError
)


class UserManagementService:
    """用户管理服务类"""
    
    def __init__(self, db: Session, audit_service: Optional[AuditService] = None):
        self.db = db
        self.audit_service = audit_service or AuditService(db)
    
    def create_user(
        self, 
        username: str,
        password: str,
        email: str,
        user_type: UserType,
        phone: Optional[str] = None,
        creator_id: Optional[int] = None
    ) -> User:
        """
        创建新用户
        
        Args:
            username: 用户名
            password: 密码
            email: 邮箱
            user_type: 用户类型
            phone: 电话号码
            creator_id: 创建者ID
            
        Returns:
            User: 创建的用户对象
            
        Raises:
            UserAlreadyExistsError: 用户已存在
            WeakPasswordError: 密码强度不足
        """
        # 检查用户名是否已存在
        existing_user = self.db.query(User).filter(
            or_(User.username == username, User.email == email)
        ).first()
        
        if existing_user:
            if existing_user.username == username:
                raise UserAlreadyExistsError(f"用户名 '{username}' 已存在")
            else:
                raise UserAlreadyExistsError(f"邮箱 '{email}' 已存在")
        
        # 验证密码强度
        is_valid, errors = PasswordService.validate_password_strength(password)
        if not is_valid:
            raise WeakPasswordError(f"密码强度不足: {', '.join(errors)}")
        
        # 加密密码
        password_hash = PasswordService.hash_password(password)
        
        # 创建用户
        user = User(
            username=username,
            password_hash=password_hash,
            email=email,
            user_type=user_type,
            phone=phone,
            last_password_change=datetime.now(timezone.utc)
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        # 记录审计日志
        if creator_id:
            self.audit_service.log_action(
                user_id=creator_id,
                action="create_user",
                resource_type="user",
                resource_id=str(user.id),
                details={
                    "username": username,
                    "email": email,
                    "user_type": user_type.value,
                    "phone": phone
                }
            )
        
        return user
    
    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """根据ID获取用户"""
        return self.db.query(User).filter(User.id == user_id).first()
    
    def get_user_by_username(self, username: str) -> Optional[User]:
        """根据用户名获取用户"""
        return self.db.query(User).filter(User.username == username).first()
    
    def get_users(
        self, 
        skip: int = 0, 
        limit: int = 100,
        user_type: Optional[UserType] = None,
        is_active: Optional[bool] = None,
        search: Optional[str] = None
    ) -> List[User]:
        """
        获取用户列表
        
        Args:
            skip: 跳过的记录数
            limit: 返回的记录数限制
            user_type: 用户类型筛选
            is_active: 活跃状态筛选
            search: 搜索关键词（用户名或邮箱）
            
        Returns:
            List[User]: 用户列表
        """
        query = self.db.query(User)
        
        # 筛选条件
        if user_type:
            query = query.filter(User.user_type == user_type)
        
        if is_active is not None:
            query = query.filter(User.is_active == is_active)
        
        if search:
            search_pattern = f"%{search}%"
            query = query.filter(
                or_(
                    User.username.ilike(search_pattern),
                    User.email.ilike(search_pattern)
                )
            )
        
        return query.offset(skip).limit(limit).all()
    
    async def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        验证用户凭据
    
        Args:
            username: 用户名
            password: 密码
    
        Returns:
            如果凭据有效，则返回 User 对象，否则返回 None
        """
        user = await self.get_user_by_username(username)
        if not user:
            return None
        
        if not self.password_service.verify_password(password, user.password_hash):
            return None
            
        return user

    def update_user(
        self, user_id: int, updates: Dict[str, Any], operator_id: Optional[int] = None
    ) -> Optional[User]:
        """
        更新用户信息
        
        Args:
            user_id: 用户ID
            updates: 更新的字段
            operator_id: 操作者ID
            
        Returns:
            User: 更新后的用户对象
            
        Raises:
            UserNotFoundError: 用户不存在
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError(f"用户ID {user_id} 不存在")
        
        # 记录原始值用于审计
        original_values = {}
        
        # 更新字段
        for field, value in updates.items():
            if hasattr(user, field):
                original_values[field] = getattr(user, field)
                setattr(user, field, value)
        
        
        self.db.commit()
        self.db.refresh(user)
        
        # 记录审计日志
        if operator_id:
            self.audit_service.log_action(
                user_id=operator_id,
                action="update_user",
                resource_type="user",
                resource_id=str(user_id),
                details={
                    "updates": updates,
                    "original_values": original_values
                }
            )
        
        return user
    
    def delete_user(self, user_id: int, operator_id: Optional[int] = None) -> bool:
        """
        删除用户
        
        Args:
            user_id: 用户ID
            operator_id: 操作者ID
            
        Returns:
            bool: 是否删除成功
            
        Raises:
            UserNotFoundError: 用户不存在
            LastAdminError: 不能删除最后一个活动的管理员
            PermissionDeniedError: 不能删除自己
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError(f"用户ID {user_id} 不存在")

        # 不能删除自己
        if operator_id and user_id == operator_id:
            raise PermissionDeniedError("不能删除自己的账户")

        # 检查是否为最后一个活动的管理员
        if user.user_type == UserType.ADMIN:
            admin_count = self.db.query(User).filter(
                and_(
                    User.user_type == UserType.ADMIN,
                    User.is_active == True,
                    User.id != user_id
                )
            ).count()

            if admin_count == 0:
                raise LastAdminError("不能删除最后一个活动的管理员")
        
        # 记录用户信息用于审计
        user_info = {
            "username": user.username,
            "email": user.email,
            "user_type": user.user_type.value,
            "phone": user.phone
        }
        
        # 删除用户（级联删除相关的 API Key 和会话）
        self.db.delete(user)
        self.db.commit()
        
        # 记录审计日志
        if operator_id:
            self.audit_service.log_action(
                user_id=operator_id,
                action="delete_user",
                resource_type="user",
                resource_id=str(user_id),
                details=user_info
            )
        
        return True
    
    def reset_password(
        self,
        user_id: int,
        new_password: str,
        operator_id: Optional[int] = None,
        force_change: bool = True
    ) -> str:
        """
        重置用户密码
        
        Args:
            user_id: 用户ID
            new_password: 新密码
            operator_id: 操作者ID
            force_change: 是否强制用户下次登录时修改密码
            
        Returns:
            str: 新密码（如果是生成的临时密码）
            
        Raises:
            UserNotFoundError: 用户不存在
            WeakPasswordError: 密码强度不足
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError(f"用户ID {user_id} 不存在")
        
        # 如果没有提供新密码，生成临时密码
        if not new_password:
            new_password = PasswordService.generate_temporary_password()
        
        # 验证密码强度
        is_valid, errors = PasswordService.validate_password_strength(new_password)
        if not is_valid:
            raise WeakPasswordError(f"密码强度不足: {', '.join(errors)}")
        
        # 更新密码
        user.password_hash = PasswordService.hash_password(new_password)
        user.last_password_change = datetime.now(timezone.utc)
        user.failed_login_attempts = 0  # 重置失败次数
        user.locked_until = None  # 解锁账户
        
        self.db.commit()
        
        # 清除用户所有会话（强制重新登录）
        self._invalidate_user_sessions(user_id)
        
        # 记录审计日志
        if operator_id:
            self.audit_service.log_action(
                user_id=operator_id,
                action="reset_password",
                resource_type="user",
                resource_id=str(user_id),
                details={
                    "target_username": user.username,
                    "force_change": force_change
                }
            )
        
        return new_password
    
    def change_password(
        self,
        user_id: int,
        old_password: str,
        new_password: str
    ) -> bool:
        """
        用户修改自己的密码
        
        Args:
            user_id: 用户ID
            old_password: 旧密码
            new_password: 新密码
            
        Returns:
            bool: 是否修改成功
            
        Raises:
            UserNotFoundError: 用户不存在
            PermissionDeniedError: 旧密码错误
            WeakPasswordError: 新密码强度不足
        """
        user = self.get_user_by_id(user_id)
        if not user:
            raise UserNotFoundError(f"用户ID {user_id} 不存在")
        
        # 验证旧密码
        if not self._verify_user_password(user, old_password):
            raise PermissionDeniedError("旧密码错误")
        
        # 验证新密码强度
        is_valid, errors = PasswordService.validate_password_strength(new_password)
        if not is_valid:
            raise WeakPasswordError(f"密码强度不足: {', '.join(errors)}")
        
        # 更新密码
        user.password_hash = PasswordService.hash_password(new_password)
        user.last_password_change = datetime.now(timezone.utc)
        
        self.db.commit()
        
        # 清除用户所有会话（强制重新登录）
        self._invalidate_user_sessions(user_id)
        
        # 记录审计日志
        self.audit_service.log_action(
            user_id=user_id,
            action="change_password",
            resource_type="user",
            resource_id=str(user_id),
            details={"username": user.username}
        )
        
        return True
    
    def lock_user(self, user_id: int, lock_duration_minutes: int = 30) -> bool:
        """锁定用户账户"""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.locked_until = datetime.now(timezone.utc) + timedelta(minutes=lock_duration_minutes)
        self.db.commit()
        
        # 清除用户所有会话
        self._invalidate_user_sessions(user_id)
        
        return True
    
    def unlock_user(self, user_id: int, operator_id: Optional[int] = None) -> bool:
        """解锁用户账户"""
        user = self.get_user_by_id(user_id)
        if not user:
            return False
        
        user.locked_until = None
        user.failed_login_attempts = 0
        self.db.commit()
        
        # 记录审计日志
        if operator_id:
            self.audit_service.log_action(
                user_id=operator_id,
                action="unlock_user",
                resource_type="user",
                resource_id=str(user_id),
                details={"target_username": user.username}
            )
        
        return True
    
    def _verify_user_password(self, user: User, password: str) -> bool:
        """验证用户密码（支持旧的SHA-256和新的bcrypt）"""
        # 如果是旧的 SHA-256 哈希，尝试迁移
        if PasswordService.is_sha256_hash(user.password_hash):
            new_hash = PasswordService.migrate_sha256_to_bcrypt(password, user.password_hash)
            if new_hash:
                # 迁移成功，更新密码哈希
                user.password_hash = new_hash
                user.last_password_change = datetime.now(timezone.utc)
                self.db.commit()
                return True
            return False
        
        # 使用 bcrypt 验证
        return PasswordService.verify_password(password, user.password_hash)
    
    def _invalidate_user_sessions(self, user_id: int):
        """清除用户所有会话"""
        self.db.query(UserSession).filter(
            UserSession.user_id == user_id
        ).update({"is_active": False})
        self.db.commit()