"""
Admin user database model
"""
from sqlalchemy import <PERSON>umn, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base
import enum


class UserType(enum.Enum):
    """用户类型枚举"""
    USER = "user"
    ADMIN = "admin"


class User(Base):
    """User model for web interface authentication"""
    
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    user_type = Column(Enum(UserType), default=UserType.USER, nullable=False, index=True)
    phone = Column(String(20), nullable=True)
    last_password_change = Column(DateTime(timezone=True), nullable=True)
    failed_login_attempts = Column(Integer, default=0, nullable=False)
    locked_until = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)
    
    # 关系
    api_keys = relationship("APIKey", back_populates="user", cascade="all, delete-orphan")
    audit_logs = relationship("AuditLog", back_populates="user")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    
    @property
    def is_locked(self) -> bool:
        """检查账户是否被锁定"""
        if self.locked_until is None:
            return False
        from datetime import datetime, timezone
        return datetime.now(timezone.utc) < self.locked_until
    
    def get_user_type_display(self) -> str:
        """获取用户类型显示名称"""
        type_map = {
            UserType.USER: "普通用户",
            UserType.ADMIN: "管理员"
        }
        return type_map.get(self.user_type, "未知")