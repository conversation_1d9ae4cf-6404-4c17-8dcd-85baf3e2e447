"""
User session database model
"""
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, DateTime, Foreign<PERSON>ey, <PERSON>olean, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class UserSession(Base):
    """User session model for tracking active sessions"""
    
    __tablename__ = "user_sessions"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    session_token = Column(String(255), unique=True, nullable=False, index=True)
    ip_address = Column(String(45), nullable=True)  # 支持IPv6
    user_agent = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=False, index=True)
    is_active = Column(Boolean, default=True, nullable=False)
    last_activity = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    user = relationship("User", back_populates="sessions")
    
    @property
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)
        expires_at = self.expires_at
        if expires_at.tzinfo is None:
            # 如果数据库中的时间是naive的，假设它是UTC时间
            expires_at = expires_at.replace(tzinfo=timezone.utc)
        return now > expires_at
    
    @property
    def is_valid(self) -> bool:
        """检查会话是否有效"""
        return self.is_active and not self.is_expired
    
    def __repr__(self):
        return f"<UserSession(id={self.id}, user_id={self.user_id}, is_active={self.is_active})>"