"""
API Key database model
"""
from sqlalchemy import Column, Integer, String, Boolean, DateTime, ForeignKey, JSON, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base


class APIKey(Base):
    """API Key model for authentication"""
    
    __tablename__ = "api_keys"
    
    id = Column(Integer, primary_key=True, index=True)
    key = Column(String(255), unique=True, index=True, nullable=False)  # 存储完整key用于验证
    key_value = Column(String(255), unique=True, index=True, nullable=False)  # API Key值
    key_hash = Column(String(255), unique=True, index=True, nullable=False)  # 保持向后兼容
    user_identifier = Column(String(100), nullable=False)  # 保持向后兼容
    user_id = Column(Integer, ForeignKey("users.id", ondelete="CASCADE"), nullable=True, index=True)
    name = Column(String(100), nullable=False, default="default")
    description = Column(Text, nullable=True)
    permissions = Column(JSON, nullable=True, default=list)  # 权限列表
    is_active = Column(Boolean, default=True, nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True, index=True)
    last_used_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    revoked_at = Column(DateTime(timezone=True), nullable=True)
    
    # 关系
    user = relationship("User", back_populates="api_keys")
    
    @property
    def is_expired(self) -> bool:
        """检查API Key是否过期"""
        if self.expires_at is None:
            return False
        # 确保比较的都是timezone-aware的datetime
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)
        expires_at = self.expires_at
        if expires_at.tzinfo is None:
            # 如果数据库中的时间是naive的，假设它是UTC时间
            expires_at = expires_at.replace(tzinfo=timezone.utc)
        return now > expires_at
    
    @property
    def is_valid(self) -> bool:
        """检查API Key是否有效"""
        return self.is_active and not self.is_expired and self.revoked_at is None