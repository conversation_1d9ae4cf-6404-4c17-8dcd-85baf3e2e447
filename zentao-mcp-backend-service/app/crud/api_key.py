"""
API Key CRUD operations
"""
import hashlib
import secrets
from sqlalchemy.orm import Session
from app.models.api_key import API<PERSON>ey
from app.schemas.api_key import APIKeyCreate
from typing import Optional, List


def generate_api_key() -> str:
    """Generate a new API key"""
    return secrets.token_urlsafe(32)


def hash_api_key(api_key: str) -> str:
    """Hash an API key for storage"""
    return hashlib.sha256(api_key.encode()).hexdigest()


def create_api_key(db: Session, api_key_create: APIKeyCreate) -> tuple[APIKey, str]:
    """Create a new API key"""
    plain_key = generate_api_key()
    key_hash = hash_api_key(plain_key)
    
    db_api_key = APIKey(
        key_hash=key_hash,
        user_identifier=api_key_create.user_identifier
    )
    db.add(db_api_key)
    db.commit()
    db.refresh(db_api_key)
    
    return db_api_key, plain_key


def get_api_key_by_hash(db: Session, key_hash: str) -> Optional[APIKey]:
    """Get API key by hash"""
    return db.query(APIKey).filter(APIKey.key_hash == key_hash).first()

def get_api_key_by_value(db: Session, key_value: str) -> Optional[APIKey]:
    """Backward-compatible: get API key by plain value (for legacy rows)"""
    return db.query(APIKey).filter(APIKey.key_value == key_value).first()


def get_api_keys(db: Session, skip: int = 0, limit: int = 100) -> List[APIKey]:
    """Get all API keys"""
    return db.query(APIKey).offset(skip).limit(limit).all()


def revoke_api_key(db: Session, api_key_id: int) -> Optional[APIKey]:
    """Revoke an API key"""
    db_api_key = db.query(APIKey).filter(APIKey.id == api_key_id).first()
    if db_api_key:
        db_api_key.is_active = False
        db.commit()
        db.refresh(db_api_key)
    return db_api_key


def activate_api_key(db: Session, api_key_id: int) -> Optional[APIKey]:
    """Activate an API key"""
    db_api_key = db.query(APIKey).filter(APIKey.id == api_key_id).first()
    if db_api_key:
        db_api_key.is_active = True
        db.commit()
        db.refresh(db_api_key)
    return db_api_key