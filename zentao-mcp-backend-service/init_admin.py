#!/usr/bin/env python3
"""
初始化管理员用户脚本
"""
import sys
from pathlib import Path

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))

from app.core.database import get_db
from app.services.user_management_service import UserManagementService
from app.models import UserType

def init_admin_user():
    """初始化管理员用户"""
    print("👤 初始化管理员用户...")
    
    db = next(get_db())
    user_service = UserManagementService(db)
    
    try:
        # 检查是否已有管理员
        existing_admins = user_service.get_users(user_type=UserType.ADMIN)
        
        if existing_admins:
            print(f"✅ 已存在 {len(existing_admins)} 个管理员:")
            for admin in existing_admins:
                print(f"   - {admin.username} ({admin.email})")
            return True
        
        # 创建默认管理员
        admin_user = user_service.create_user(
            username="admin",
            password="Admin123!",
            email="<EMAIL>",
            user_type=UserType.ADMIN,
            phone=None
        )
        
        print(f"✅ 管理员创建成功:")
        print(f"   用户名: {admin_user.username}")
        print(f"   邮箱: {admin_user.email}")
        print(f"   用户类型: {admin_user.get_user_type_display()}")
        print(f"   默认密码: Admin123!")
        print(f"   ⚠️  请登录后立即修改密码!")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建管理员用户失败: {e}")
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 权限系统初始化")
    print("=" * 40)
    
    success = init_admin_user()
    
    print("=" * 40)
    if success:
        print("🎉 初始化完成!")
        print("\n📝 下一步:")
        print("1. 启动服务器: python start_server.py")
        print("2. 访问API文档: http://localhost:8000/docs")
        print("3. 使用管理员账户登录测试")
    else:
        print("❌ 初始化失败!")
        sys.exit(1)