"""
测试运行脚本
"""
import subprocess
import sys
import os
from pathlib import Path

def run_tests():
    """运行所有测试"""
    print("🚀 开始运行测试套件...")
    print("=" * 60)
    
    # 确保在正确的目录
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)
    
    # 安装测试依赖 (使用uv)
    print("📦 安装测试依赖...")
    subprocess.run(["uv", "sync", "--group", "dev"], check=True)
    
    # 运行不同类型的测试 (使用uv run)
    test_commands = [
        ("单元测试", ["uv", "run", "pytest", "tests/unit/", "-v"]),
        ("API测试", ["uv", "run", "pytest", "tests/api/", "-v"]),
        ("集成测试", ["uv", "run", "pytest", "tests/integration/", "-v"]),
        ("所有测试", ["uv", "run", "pytest", "tests/", "-v", "--tb=short"])
    ]
    
    results = {}
    
    for test_name, command in test_commands:
        print(f"\n🧪 运行{test_name}...")
        print("-" * 40)
        
        try:
            result = subprocess.run(command, capture_output=True, text=True, timeout=300)
            results[test_name] = {
                "returncode": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr
            }
            
            if result.returncode == 0:
                print(f"✅ {test_name}通过")
                if result.stdout:
                    print("输出:")
                    print(result.stdout[-500:])  # 显示最后500字符
            else:
                print(f"❌ {test_name}失败")
                print("标准输出:")
                print(result.stdout[-1000:] if result.stdout else "无输出")
                print("错误输出:")
                print(result.stderr[-1000:] if result.stderr else "无错误输出")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_name}超时")
            results[test_name] = {"error": "测试超时"}
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            results[test_name] = {"error": str(e)}
    
    # 打印总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    passed = 0
    total = len(test_commands)
    
    for test_name, result in results.items():
        if "error" in result:
            print(f"❌ {test_name}: 执行异常")
        elif result["returncode"] == 0:
            print(f"✅ {test_name}: 通过")
            passed += 1
        else:
            print(f"❌ {test_name}: 失败")
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试套件通过")
    
    if passed == total:
        print("🎉 所有测试通过!")
        return True
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)