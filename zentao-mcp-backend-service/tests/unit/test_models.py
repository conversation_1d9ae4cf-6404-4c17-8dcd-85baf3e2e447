"""
数据库模型单元测试
"""
import pytest
from datetime import datetime, timedelta, timezone
import secrets

from app.models.admin import User, UserType
from app.models.api_key import APIKey
from app.models.audit_log import AuditLog
from app.models.user_session import UserSession
from app.services.password_service import PasswordService


class TestUser:
    """User模型测试类"""
    
    def test_create_admin_user(self, db_session):
        """测试创建管理员用户"""
        user = User(
            username="test_admin",
            password_hash=PasswordService.hash_password("TestPass123!"),
            email="<EMAIL>",
            user_type=UserType.ADMIN,
            phone="13800138000",
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        assert user.username == "test_admin"
        assert user.email == "<EMAIL>"
        assert user.user_type == UserType.ADMIN
        assert user.phone == "13800138000"
        assert user.is_active is True
        assert user.is_locked is False
    
    def test_user_type_display(self, db_session):
        """测试用户类型显示"""
        user = User(
            username="test_user",
            password_hash="hashed_password",
            email="<EMAIL>",
            user_type=UserType.USER,
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        assert user.get_user_type_display() == "普通用户"
        
        user.user_type = UserType.ADMIN
        assert user.get_user_type_display() == "管理员"
    
    def test_user_validation(self, db_session):
        """测试用户验证"""
        user = User(
            username="test_user",
            password_hash="hashed_password",
            email="<EMAIL>",
            user_type=UserType.USER,
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        # 测试活跃状态
        assert user.is_active is True
        user.is_active = False
        assert user.is_active is False
        
        # 测试锁定状态
        assert user.is_locked is False
        user.failed_login_attempts = 5
        user.locked_until = datetime.now(timezone.utc) + timedelta(minutes=30)
        assert user.is_locked is True


class TestAPIKey:
    """APIKey模型测试类"""
    
    def test_create_api_key(self, db_session):
        """测试创建API Key"""
        api_key = APIKey(
            key="test_key_123",
            key_value="test_key_123",
            key_hash="hashed_key",
            user_identifier="test_user",
            name="测试密钥",
            description="这是一个测试API密钥",
            permissions=["read", "write"],
            expires_at=datetime.now(timezone.utc) + timedelta(days=30),
            is_active=True
        )
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)
        
        assert api_key.key == "test_key_123"
        assert api_key.user_identifier == "test_user"
        assert api_key.name == "测试密钥"
        assert api_key.permissions == ["read", "write"]
        assert api_key.is_active is True
    
    def test_api_key_expiration(self, db_session):
        """测试API Key过期"""
        # 未过期的Key
        future_key = APIKey(
            key="future_key",
            key_value="future_key",
            key_hash="hashed_key",
            user_identifier="test_user",
            expires_at=datetime.now(timezone.utc) + timedelta(days=30),
            is_active=True
        )
        db_session.add(future_key)
        db_session.commit()
        db_session.refresh(future_key)
        assert future_key.is_expired is False
        assert future_key.is_valid is True
        
        # 已过期的Key
        expired_key = APIKey(
            key="expired_key",
            key_hash="hashed_key",
            user_identifier="test_user",
            expires_at=datetime.now(timezone.utc) - timedelta(days=1)
        )
        assert expired_key.is_expired is True
        assert expired_key.is_valid is False
        
        # 无过期时间的Key
        no_expiry_key = APIKey(
            key="no_expiry_key",
            key_hash="hashed_key",
            user_identifier="test_user",
            expires_at=None
        )
        assert no_expiry_key.is_expired is False
        assert no_expiry_key.is_valid is True
    
    def test_api_key_status(self, db_session):
        """测试API Key状态"""
        api_key = APIKey(
            key="test_key",
            key_value="test_key",
            key_hash="hashed_key",
            user_identifier="test_user",
            is_active=True
        )
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)
        
        # 测试活跃状态
        assert api_key.is_valid is True
        api_key.is_active = False
        assert api_key.is_valid is False


class TestAuditLog:
    """AuditLog模型测试类"""
    
    def test_create_audit_log(self, db_session):
        """测试创建审计日志"""
        audit_log = AuditLog(
            action="create_user",
            resource_type="user",
            resource_id="123",
            details={"username": "test_user", "email": "<EMAIL>"},
            ip_address="***********",
            user_agent="Test Agent"
        )
        db_session.add(audit_log)
        db_session.commit()
        db_session.refresh(audit_log)
        
        assert audit_log.action == "create_user"
        assert audit_log.resource_type == "user"
        assert audit_log.resource_id == "123"
        assert audit_log.details == {"username": "test_user", "email": "<EMAIL>"}
        assert audit_log.ip_address == "***********"
        assert audit_log.user_agent == "Test Agent"
        assert audit_log.created_at is not None


class TestUserSession:
    """UserSession模型测试类"""
    
    def test_create_user_session(self, db_session):
        """测试创建用户会话"""
        session = UserSession(
            session_token=secrets.token_urlsafe(32),
            ip_address="***********",
            user_agent="Test Browser",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=8),
            is_active=True
        )
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)
        
        assert len(session.session_token) > 30
        assert session.ip_address == "***********"
        assert session.user_agent == "Test Browser"
        assert session.is_active is True
    
    def test_session_expiration(self, db_session):
        """测试会话过期"""
        # 未过期的会话
        active_session = UserSession(
            session_token=secrets.token_urlsafe(32),
            ip_address="***********",
            user_agent="Test Browser",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=8),
            is_active=True
        )
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)
        assert active_session.is_expired is False
        assert active_session.is_valid is True
        
        # 已过期的会话
        expired_session = UserSession(
            session_token=secrets.token_urlsafe(32),
            ip_address="***********",
            user_agent="Test Browser",
            expires_at=datetime.now(timezone.utc) - timedelta(hours=1)
        )
        assert expired_session.is_expired is True
        assert expired_session.is_valid is False
    
    def test_session_status(self, db_session):
        """测试会话状态"""
        session = UserSession(
            session_token=secrets.token_urlsafe(32),
            ip_address="***********",
            user_agent="Test Browser",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=8),
            is_active=True
        )
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)
        
        # 测试活跃状态
        assert session.is_valid is True
        session.is_active = False
        assert session.is_valid is False


class TestUserType:
    """UserType枚举测试类"""
    
    def test_user_type_values(self):
        """测试用户类型枚举值"""
        assert UserType.USER.value == "user"
        assert UserType.ADMIN.value == "admin"
    
    def test_user_type_comparison(self):
        """测试用户类型比较"""
        assert UserType.USER != UserType.ADMIN
        assert UserType.USER == UserType.USER


class TestModelRelationships:
    """模型关系测试类"""
    
    def test_user_api_key_relationship(self):
        """测试用户和API Key关系"""
        user = User(
            username="test_user",
            password_hash="hashed_password",
            email="<EMAIL>",
            user_type=UserType.USER,
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        api_key = APIKey(
            key="test_key",
            key_hash="hashed_key",
            user_identifier="test_user",
            name="测试密钥"
        )
        
        # 设置关系
        api_key.user_id = user.id
        db_session.add(api_key)
        db_session.commit()
        db_session.refresh(api_key)
        assert api_key.user.username == "test_user"
    
    def test_user_audit_log_relationship(self):
        """测试用户和审计日志关系"""
        user = User(
            username="test_user",
            password_hash="hashed_password",
            email="<EMAIL>",
            user_type=UserType.USER,
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        audit_log = AuditLog(
            action="login",
            resource_type="session",
            resource_id="123",
            details={"action": "user_login"},
            ip_address="***********"
        )
        
        # 设置关系
        audit_log.user_id = user.id
        db_session.add(audit_log)
        db_session.commit()
        db_session.refresh(audit_log)
        assert audit_log.user.username == "test_user"
    
    def test_user_session_relationship(self):
        """测试用户和会话关系"""
        user = User(
            username="test_user",
            password_hash="hashed_password",
            email="<EMAIL>",
            user_type=UserType.USER,
            is_active=True
        )
        db_session.add(user)
        db_session.commit()
        db_session.refresh(user)
        
        session = UserSession(
            session_token=secrets.token_urlsafe(32),
            ip_address="***********",
            user_agent="Test Browser",
            expires_at=datetime.now(timezone.utc) + timedelta(hours=8),
            is_active=True
        )
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)
        
        # 设置关系
        session.user_id = user.id
        db_session.add(session)
        db_session.commit()
        db_session.refresh(session)
        assert session.user.username == "test_user"