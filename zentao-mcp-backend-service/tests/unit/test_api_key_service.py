"""
API Key服务单元测试
"""
import pytest
from datetime import datetime, timedelta
from app.services.api_key_service import APIKeyService
from app.models import APIKey, User, UserType

class TestAPIKeyService:
    """API Key服务测试类"""
    
    def test_create_api_key(self, db_session, admin_user):
        """测试创建API Key"""
        service = APIKeyService(db_session)
        
        api_key = service.create_api_key(
            user_id=admin_user.id,
            name="测试Key",
            description="测试用API Key",
            permissions=["read", "write"]
        )
        
        assert api_key is not None
        assert api_key.name == "测试Key"
        assert api_key.permissions == ["read", "write"]
        assert api_key.is_active is True
    
    def test_get_api_key_by_key(self, db_session, api_key):
        """测试通过key获取API Key"""
        service = APIKeyService(db_session)
        
        found_key = service.get_api_key_by_key(api_key.key)
        
        assert found_key is not None
        assert found_key.id == api_key.id
        assert found_key.name == api_key.name
    
    def test_get_api_key_by_invalid_key(self, db_session):
        """测试通过无效key获取API Key"""
        service = APIKeyService(db_session)
        
        found_key = service.get_api_key_by_key("invalid_key")
        
        assert found_key is None
    
    def test_validate_api_key_valid(self, db_session, api_key):
        """测试验证有效API Key"""
        service = APIKeyService(db_session)
        
        is_valid = service.validate_api_key(api_key.key)
        
        assert is_valid is True
    
    def test_validate_api_key_invalid(self, db_session):
        """测试验证无效API Key"""
        service = APIKeyService(db_session)
        
        is_valid = service.validate_api_key("invalid_key")
        
        assert is_valid is False
    
    def test_deactivate_api_key(self, db_session, api_key):
        """测试停用API Key"""
        service = APIKeyService(db_session)
        
        result = service.deactivate_api_key(api_key.id)
        
        assert result is True
        
        # 验证已停用
        updated_key = service.get_api_key_by_id(api_key.id)
        assert updated_key.is_active is False
    
    def test_get_user_api_keys(self, db_session, admin_user, api_key):
        """测试获取用户API Keys"""
        service = APIKeyService(db_session)
        
        keys = service.get_user_api_keys(admin_user.id)
        
        assert len(keys) >= 1
        assert any(key.id == api_key.id for key in keys)