"""
密码服务单元测试
"""
import pytest
import hashlib
from app.services.password_service import PasswordService

class TestPasswordService:
    """密码服务测试类"""
    
    def test_hash_password(self):
        """测试密码加密"""
        password = "TestPassword123!"
        hashed = PasswordService.hash_password(password)
        
        assert hashed is not None
        assert len(hashed) > 50  # bcrypt哈希长度
        assert hashed != password  # 确保已加密
    
    def test_verify_password_correct(self):
        """测试正确密码验证"""
        password = "TestPassword123!"
        hashed = PasswordService.hash_password(password)
        
        assert PasswordService.verify_password(password, hashed) is True
    
    def test_verify_password_incorrect(self):
        """测试错误密码验证"""
        password = "TestPassword123!"
        wrong_password = "WrongPassword123!"
        hashed = PasswordService.hash_password(password)
        
        assert PasswordService.verify_password(wrong_password, hashed) is False
    
    def test_validate_password_strength_strong(self):
        """测试强密码验证"""
        strong_passwords = [
            "StrongPass123!",
            "MySecure@Pass2024",
            "Complex#Password1"
        ]
        
        for password in strong_passwords:
            is_strong, errors = PasswordService.validate_password_strength(password)
            assert is_strong is True
            assert len(errors) == 0
    
    def test_validate_password_strength_weak(self):
        """测试弱密码验证"""
        weak_passwords = [
            "123",  # 太短
            "password",  # 无数字和特殊字符
            "Password123",  # 无特殊字符
            "password123!"  # 无大写字母
        ]
        
        for password in weak_passwords:
            is_strong, errors = PasswordService.validate_password_strength(password)
            assert is_strong is False
            assert len(errors) > 0
    
    def test_generate_temporary_password(self):
        """测试临时密码生成"""
        temp_password = PasswordService.generate_temporary_password()
        
        assert temp_password is not None
        assert len(temp_password) >= 12
        
        # 验证临时密码强度
        is_strong, errors = PasswordService.validate_password_strength(temp_password)
        assert is_strong is True
    
    def test_is_sha256_hash(self):
        """测试SHA-256哈希识别"""
        password = "testpassword"
        sha256_hash = hashlib.sha256(password.encode()).hexdigest()
        bcrypt_hash = PasswordService.hash_password(password)
        
        assert PasswordService.is_sha256_hash(sha256_hash) is True
        assert PasswordService.is_sha256_hash(bcrypt_hash) is False
        assert PasswordService.is_sha256_hash("invalid_hash") is False
    
    def test_migrate_sha256_to_bcrypt(self):
        """测试SHA-256到bcrypt迁移"""
        password = "testpassword"
        sha256_hash = hashlib.sha256(password.encode()).hexdigest()
        
        # 测试正确迁移
        new_hash = PasswordService.migrate_sha256_to_bcrypt(password, sha256_hash)
        assert new_hash is not None
        assert PasswordService.verify_password(password, new_hash) is True
        
        # 测试错误密码迁移
        wrong_hash = PasswordService.migrate_sha256_to_bcrypt("wrongpassword", sha256_hash)
        assert wrong_hash is None