"""
认证流程集成测试
"""
import pytest
from fastapi.testclient import TestClient

class TestAuthFlow:
    """认证流程测试类"""
    
    def test_complete_auth_flow(self, client: TestClient, admin_user):
        """测试完整认证流程"""
        # 1. 登录获取token
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        login_response = client.post("/api/v1/auth/login", json=login_data)
        assert login_response.status_code == 200
        
        token_data = login_response.json()
        access_token = token_data["access_token"]
        assert access_token is not None
        
        # 2. 使用token访问受保护资源
        headers = {"Authorization": f"Bearer {access_token}"}
        me_response = client.get("/api/v1/auth/me", headers=headers)
        assert me_response.status_code == 200
        
        user_data = me_response.json()
        assert user_data["username"] == "test_admin"
        
        # 3. 登出
        logout_response = client.post("/api/v1/auth/logout", headers=headers)
        assert logout_response.status_code == 200
        
        # 4. 验证token已失效（可选，取决于实现）
        # me_response_after_logout = client.get("/api/v1/auth/me", headers=headers)
        # assert me_response_after_logout.status_code == 401
    
    def test_api_key_creation_flow(self, client: TestClient, admin_user):
        """测试API Key创建流程"""
        # 1. 登录获取管理员token
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        login_response = client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["access_token"]
        headers = {"Authorization": f"Bearer {token}"}
        
        # 2. 创建API Key
        key_data = {
            "user_identifier": "integration_test_user",
            "name": "集成测试Key",
            "description": "用于集成测试的API Key"
        }
        create_response = client.post("/api/v1/admin/keys", json=key_data, headers=headers)
        assert create_response.status_code in [200, 201]
        
        created_key = create_response.json()
        key_id = created_key["id"]
        
        # 3. 获取API Key列表验证创建成功
        list_response = client.get("/api/v1/admin/keys", headers=headers)
        assert list_response.status_code == 200
        
        keys = list_response.json()
        assert any(key["id"] == key_id for key in keys)
        
        # 4. 删除创建的API Key
        delete_response = client.delete(f"/api/v1/admin/keys/{key_id}", headers=headers)
        assert delete_response.status_code in [200, 204]