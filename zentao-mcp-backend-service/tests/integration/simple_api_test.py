#!/usr/bin/env python3
import requests
import json

# 管理员API Key
admin_key = "admin_super_secret_key_12345"
base_url = "http://localhost:8000"

print("🔑 测试API Key创建...")

# 创建API Key
response = requests.post(
    f"{base_url}/api/v1/admin/keys",
    headers={"Authorization": f"Bearer {admin_key}", "Content-Type": "application/json"},
    json={"user_identifier": "test_user", "description": "测试Key"}
)

print(f"状态: {response.status_code}")
if response.status_code in [200, 201]:
    data = response.json()
    print("✅ 创建成功!")
    print(f"新Key: {data.get('plain_key', 'N/A')}")
else:
    print(f"❌ 失败: {response.text}")

# 获取Key列表
response = requests.get(
    f"{base_url}/api/v1/admin/keys",
    headers={"Authorization": f"Bearer {admin_key}"}
)

print(f"\n📋 Key列表: {response.status_code}")
if response.status_code == 200:
    keys = response.json()
    print(f"总数: {len(keys)}")
    for key in keys:
        print(f"- ID:{key['id']} 用户:{key['user_identifier']} 状态:{'活跃' if key['is_active'] else '禁用'}")