# Zentao MCP Backend Service 测试套件

## 测试结构

```
tests/
├── __init__.py              # 测试包初始化
├── conftest.py             # pytest配置和fixtures
├── run_tests.py            # 测试运行脚本
├── README.md               # 测试文档
├── unit/                   # 单元测试
│   ├── test_models.py      # 数据模型测试
│   ├── test_services.py    # 服务层测试
│   ├── test_password_service.py  # 密码服务测试
│   └── test_api_key_service.py   # API Key服务测试
├── api/                    # API端点测试
│   ├── test_auth_endpoints.py    # 认证端点测试
│   ├── test_admin_endpoints.py   # 管理员端点测试
│   ├── test_project_endpoints.py # 项目端点测试
│   ├── test_user_endpoints.py    # 用户端点测试
│   ├── test_system_endpoints.py  # 系统端点测试
│   └── test_zentao_endpoints.py  # 禅道业务端点测试
└── integration/            # 集成测试
    ├── test_admin_correct.py     # 管理员功能集成测试
    ├── test_complete.py          # 完整功能集成测试
    └── test_auth_flow.py         # 认证流程集成测试
```

## 运行测试

### 运行所有测试
```bash
python tests/run_tests.py
```

### 运行特定类型的测试
```bash
# 单元测试
pytest tests/unit/ -v

# API测试
pytest tests/api/ -v

# 集成测试
pytest tests/integration/ -v
```

### 运行特定测试文件
```bash
pytest tests/unit/test_password_service.py -v
```

### 运行特定测试方法
```bash
pytest tests/unit/test_password_service.py::TestPasswordService::test_hash_password -v
```

## 测试覆盖范围

### 单元测试 (Unit Tests)
- **数据模型测试**: 验证数据库模型的定义和关系
- **服务层测试**: 验证业务逻辑服务的功能
- **密码服务测试**: 验证密码加密、验证和强度检查
- **API Key服务测试**: 验证API Key的创建、验证和管理

### API端点测试 (API Tests)
- **认证端点**: 登录、登出、获取用户信息
- **管理员端点**: API Key管理、用户管理
- **项目端点**: 项目列表、项目详情
- **用户端点**: 用户列表、用户创建
- **系统端点**: 系统信息、系统状态
- **禅道业务端点**: 需求、任务、缺陷、部门、分析

### 集成测试 (Integration Tests)
- **认证流程**: 完整的登录-访问-登出流程
- **API Key管理流程**: 创建-使用-删除API Key流程
- **管理员功能**: 管理员权限和功能测试
- **完整功能**: 端到端功能测试

## 测试配置

### Fixtures
- `db_session`: 测试数据库会话
- `client`: FastAPI测试客户端
- `admin_user`: 测试管理员用户
- `api_key`: 测试API Key

### 测试数据库
测试使用SQLite内存数据库，每个测试函数都会创建独立的数据库实例，确保测试之间的隔离。

## 测试最佳实践

1. **测试隔离**: 每个测试都应该独立运行，不依赖其他测试的结果
2. **数据清理**: 使用fixtures自动创建和清理测试数据
3. **断言明确**: 使用明确的断言，确保测试意图清晰
4. **错误测试**: 不仅测试正常流程，也要测试错误情况
5. **文档化**: 为复杂的测试添加注释说明

## 持续集成

测试套件设计为可以在CI/CD环境中运行，支持：
- 自动依赖安装
- 详细的测试报告
- 失败时的错误信息
- 测试覆盖率报告

## 故障排除

### 常见问题
1. **数据库连接错误**: 确保测试数据库配置正确
2. **导入错误**: 确保项目路径在Python路径中
3. **权限错误**: 确保测试用户有足够的权限
4. **端口冲突**: 确保测试端口没有被占用

### 调试技巧
- 使用 `-s` 参数查看print输出
- 使用 `--pdb` 参数在失败时进入调试器
- 使用 `--tb=long` 参数查看详细的错误堆栈