"""
pytest配置文件
"""
import pytest
import asyncio
from typing import Generator
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from fastapi.testclient import TestClient

from app.core.database import get_db, Base
from main import app

# 测试数据库配置
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环"""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture(scope="function")
def db_session():
    """创建测试数据库会话"""
    Base.metadata.create_all(bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.close()
        Base.metadata.drop_all(bind=engine)

@pytest.fixture(scope="function")
def client(db_session):
    """创建测试客户端"""
    
    original_startup = app.router.on_startup
    original_shutdown = app.router.on_shutdown
    
    app.router.on_startup = []
    app.router.on_shutdown = []

    def override_get_db():
        try:
            yield db_session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    with TestClient(app) as test_client:
        yield test_client
        
    app.dependency_overrides.clear()
    
    app.router.on_startup = original_startup
    app.router.on_shutdown = original_shutdown

@pytest.fixture
def admin_user(db_session):
    """创建测试管理员用户"""
    from app.models import User, UserType
    from app.services.password_service import PasswordService
    
    user = User(
        username="test_admin",
        password_hash=PasswordService.hash_password("admin123"),
        email="<EMAIL>",
        user_type=UserType.ADMIN,
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def normal_user(db_session):
    """创建测试普通用户"""
    from app.models import User, UserType
    from app.services.password_service import PasswordService
    
    user = User(
        username="test_user",
        password_hash=PasswordService.hash_password("user123"),
        email="<EMAIL>",
        user_type=UserType.USER,
        is_active=True
    )
    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)
    return user


@pytest.fixture
def api_key(db_session, admin_user):
    """创建测试API Key"""
    from app.models import APIKey
    from datetime import datetime, timedelta, timezone
    
    key = APIKey(
        key="test_api_key_123",
        key_value="test_api_key_123",
        key_hash="hashed_test_key",
        user_identifier="test_user",
        name="测试API Key",
        description="用于测试的API Key",
        permissions=["read", "write"],
        expires_at=datetime.now(timezone.utc) + timedelta(days=30),
        user_id=admin_user.id,
        is_active=True
    )
    db_session.add(key)
    db_session.commit()
    db_session.refresh(key)
    return key