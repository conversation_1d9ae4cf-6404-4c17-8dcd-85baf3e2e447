"""
管理员相关API端点测试
"""
import pytest
from fastapi.testclient import TestClient

class TestAdminEndpoints:
    """管理员端点测试类"""
    
    def get_admin_token(self, client: TestClient) -> str:
        """获取管理员token"""
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        return response.json()["access_token"]
    
    def test_create_api_key_unauthorized(self, client: TestClient):
        """测试未授权创建API Key"""
        key_data = {
            "user_identifier": "test_user",
            "name": "测试Key",
            "description": "测试用API Key"
        }
        response = client.post("/api/v1/admin/keys", json=key_data)
        assert response.status_code == 401
    
    def test_create_api_key_authorized(self, client: TestClient, admin_user):
        """测试授权创建API Key"""
        token = self.get_admin_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        key_data = {
            "user_identifier": "test_user_001",
            "name": "测试API Key",
            "description": "用于测试的API Key"
        }
        response = client.post("/api/v1/admin/keys", json=key_data, headers=headers)
        assert response.status_code in [200, 201]
        data = response.json()
        assert "id" in data
        assert data["name"] == "测试API Key"
    
    def test_get_api_keys_unauthorized(self, client: TestClient):
        """测试未授权获取API Key列表"""
        response = client.get("/api/v1/admin/keys")
        assert response.status_code == 401
    
    def test_get_api_keys_authorized(self, client: TestClient, admin_user, api_key):
        """测试授权获取API Key列表"""
        token = self.get_admin_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/admin/keys", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
    
    def test_delete_api_key_unauthorized(self, client: TestClient, api_key):
        """测试未授权删除API Key"""
        response = client.delete(f"/api/v1/admin/keys/{api_key.id}")
        assert response.status_code == 401
    
    def test_delete_api_key_authorized(self, client: TestClient, admin_user, api_key):
        """测试授权删除API Key"""
        token = self.get_admin_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.delete(f"/api/v1/admin/keys/{api_key.id}", headers=headers)
        assert response.status_code in [200, 204]