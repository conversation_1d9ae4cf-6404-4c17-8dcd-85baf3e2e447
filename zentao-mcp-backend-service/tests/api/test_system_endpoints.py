"""
系统相关API端点测试
"""
import pytest
from fastapi.testclient import TestClient

class TestSystemEndpoints:
    """系统端点测试类"""
    
    def get_api_token(self, client: TestClient) -> str:
        """获取API token"""
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        return response.json()["access_token"]
    
    def test_get_system_info_unauthorized(self, client: TestClient):
        """测试未授权获取系统信息"""
        response = client.get("/api/v1/system/info")
        assert response.status_code == 401
    
    def test_get_system_info_authorized(self, client: TestClient, admin_user):
        """测试授权获取系统信息"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/system/info", headers=headers)
        assert response.status_code in [200, 422]
    
    def test_get_system_status_unauthorized(self, client: TestClient):
        """测试未授权获取系统状态"""
        response = client.get("/api/v1/system/status")
        assert response.status_code == 401
    
    def test_get_system_status_authorized(self, client: TestClient, admin_user):
        """测试授权获取系统状态"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/system/status", headers=headers)
        assert response.status_code in [200, 422]