"""
用户相关API端点测试
"""
import pytest
from fastapi.testclient import TestClient
from app.models import UserType


class TestUserEndpoints:
    """用户端点测试类"""

    @pytest.fixture(autouse=True)
    def setup(self, client: TestClient, admin_user):
        """为测试设置共享客户端和认证头"""
        self.client = client
        self.admin_user = admin_user
        login_data = {"username": "test_admin", "password": "admin123"}
        response = self.client.post("/api/v1/admin/auth/login", json=login_data)
        assert response.status_code == 200, "登录失败，无法获取token"
        self.token = response.json()["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}

    def test_get_users_unauthorized(self):
        """测试未授权获取用户列表"""
        response = self.client.get("/api/v1/admin/users/")
        assert response.status_code == 403

    def test_get_users_authorized(self):
        """测试授权获取用户列表"""
        response = self.client.get("/api/v1/admin/users/", headers=self.headers)
        assert response.status_code == 200
        data = response.json()
        assert "users" in data
        assert "total" in data
        assert len(data["users"]) >= 1

    def test_create_user_unauthorized(self):
        """测试未授权创建用户"""
        user_data = {
            "username": "new_user",
            "password": "NewUser123!",
            "email": "<EMAIL>",
            "user_type": "user",
        }
        response = self.client.post("/api/v1/admin/users/", json=user_data)
        assert response.status_code == 403

    def test_create_user_by_admin(self):
        """测试管理员创建普通用户"""
        user_data = {
            "username": "test_user_by_admin",
            "password": "NewUser123!",
            "email": "<EMAIL>",
            "user_type": "user",
        }
        response = self.client.post("/api/v1/admin/users/", json=user_data, headers=self.headers)
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "test_user_by_admin"
        assert data["user_type"] == "user"

    def test_create_admin_by_admin(self):
        """测试管理员创建另一个管理员"""
        user_data = {
            "username": "new_admin_by_admin",
            "password": "NewAdmin123!",
            "email": "<EMAIL>",
            "user_type": "admin",
        }
        response = self.client.post("/api/v1/admin/users/", json=user_data, headers=self.headers)
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "new_admin_by_admin"
        assert data["user_type"] == "admin"

    def test_create_user_duplicate_username(self):
        """测试创建重复用户名的用户"""
        user_data = {
            "username": "test_admin",  # existing username
            "password": "NewUser123!",
            "email": "<EMAIL>",
            "user_type": "user",
        }
        response = self.client.post("/api/v1/admin/users/", json=user_data, headers=self.headers)
        assert response.status_code == 400
        assert "用户名 'test_admin' 已存在" in response.text

    def test_update_user_by_admin(self):
        """测试管理员更新用户信息"""
        user_data = {
            "username": "user_to_update",
            "password": "Password123!",
            "email": "<EMAIL>",
            "user_type": "user",
        }
        response = self.client.post("/api/v1/admin/users/", json=user_data, headers=self.headers)
        assert response.status_code == 200
        user_id = response.json()["id"]

        update_data = {"email": "<EMAIL>"}
        response = self.client.put(
            f"/api/v1/admin/users/{user_id}", json=update_data, headers=self.headers
        )
        assert response.status_code == 200
        data = response.json()
        assert data["email"] == "<EMAIL>"

    def test_delete_user_by_admin(self):
        """测试管理员删除用户"""
        user_data = {
            "username": "user_to_delete",
            "password": "Password123!",
            "email": "<EMAIL>",
            "user_type": "user",
        }
        response = self.client.post("/api/v1/admin/users/", json=user_data, headers=self.headers)
        assert response.status_code == 200
        user_id = response.json()["id"]

        response = self.client.delete(f"/api/v1/admin/users/{user_id}", headers=self.headers)
        assert response.status_code == 200
        assert "已删除" in response.text

        response = self.client.get(f"/api/v1/admin/users/{user_id}", headers=self.headers)
        assert response.status_code == 404

    def test_delete_last_admin_fails(self):
        """测试删除最后一个管理员失败（通过尝试删除自己来模拟）"""
        user_id = self.admin_user.id
        response = self.client.delete(f"/api/v1/admin/users/{user_id}", headers=self.headers)
        assert response.status_code == 403
        assert "没有权限删除该用户" in response.text


class TestUserPermission:
    """普通用户权限测试"""

    @pytest.fixture(autouse=True)
    def setup(self, client: TestClient, normal_user, admin_user):
        """为测试设置共享客户端和普通用户认证头"""
        self.client = client
        self.normal_user = normal_user
        self.admin_user = admin_user  # For targeting other users
        login_data = {"username": "test_user", "password": "user123"}
        response = self.client.post("/api/v1/admin/auth/login", json=login_data)
        assert response.status_code == 200, "普通用户登录失败，无法获取token"
        self.token = response.json()["access_token"]
        self.headers = {"Authorization": f"Bearer {self.token}"}

    def test_get_users_forbidden(self):
        """测试普通用户获取用户列表被禁止"""
        response = self.client.get("/api/v1/admin/users/", headers=self.headers)
        assert response.status_code == 403
        assert "没有权限: user:list" in response.text

    def test_create_user_forbidden(self):
        """测试普通用户创建用户被禁止"""
        user_data = {
            "username": "forbidden_user",
            "password": "Password123!",
            "email": "<EMAIL>",
            "user_type": "user",
        }
        response = self.client.post("/api/v1/admin/users/", json=user_data, headers=self.headers)
        assert response.status_code == 403
        assert "没有权限: user:create" in response.text

    def test_update_other_user_forbidden(self):
        """测试普通用户更新其他用户信息被禁止"""
        update_data = {"email": "<EMAIL>"}
        # Attempt to update admin user's info
        response = self.client.put(
            f"/api/v1/admin/users/{self.admin_user.id}", json=update_data, headers=self.headers
        )
        assert response.status_code == 403
        assert "没有权限: user:update" in response.text

    def test_delete_user_forbidden(self):
        """测试普通用户删除其他用户被禁止"""
        # Attempt to delete admin user
        response = self.client.delete(f"/api/v1/admin/users/{self.admin_user.id}", headers=self.headers)
        assert response.status_code == 403
        assert "没有权限: user:delete" in response.text