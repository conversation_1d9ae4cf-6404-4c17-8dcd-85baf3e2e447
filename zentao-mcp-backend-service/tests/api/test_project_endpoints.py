"""
项目相关API端点测试
"""
import pytest
from fastapi.testclient import TestClient

class TestProjectEndpoints:
    """项目端点测试类"""
    
    def get_api_token(self, client: TestClient) -> str:
        """获取API token"""
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        return response.json()["access_token"]
    
    def test_get_projects_unauthorized(self, client: TestClient):
        """测试未授权获取项目列表"""
        response = client.get("/api/v1/projects/")
        assert response.status_code == 401
    
    def test_get_projects_authorized(self, client: TestClient, admin_user):
        """测试授权获取项目列表"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/projects/", headers=headers)
        # 可能返回200(有数据)或422(需要zentao配置)
        assert response.status_code in [200, 422]
    
    def test_get_project_by_id_unauthorized(self, client: TestClient):
        """测试未授权获取项目详情"""
        response = client.get("/api/v1/projects/1")
        assert response.status_code == 401
    
    def test_get_project_by_id_authorized(self, client: TestClient, admin_user):
        """测试授权获取项目详情"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/projects/1", headers=headers)
        # 可能返回200(找到)、404(未找到)或422(需要zentao配置)
        assert response.status_code in [200, 404, 422]