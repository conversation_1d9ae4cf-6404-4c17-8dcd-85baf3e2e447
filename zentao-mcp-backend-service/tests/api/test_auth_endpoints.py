"""
认证相关API端点测试
"""
import pytest
from fastapi.testclient import TestClient

class TestAuthEndpoints:
    """认证端点测试类"""
    
    def test_health_check(self, client: TestClient):
        """测试健康检查端点"""
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        assert "service" in data
        assert "version" in data
    
    def test_login_invalid_credentials(self, client: TestClient):
        """测试无效凭据登录"""
        login_data = {
            "username": "invalid_user",
            "password": "invalid_password"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 401
    
    def test_login_valid_credentials(self, client: TestClient, admin_user):
        """测试有效凭据登录"""
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        assert response.status_code == 200
        data = response.json()
        assert "access_token" in data
        assert "token_type" in data
    
    def test_get_current_user_unauthorized(self, client: TestClient):
        """测试未授权获取当前用户"""
        response = client.get("/api/v1/auth/me")
        assert response.status_code == 401
    
    def test_get_current_user_authorized(self, client: TestClient, admin_user):
        """测试授权获取当前用户"""
        # 先登录获取token
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        login_response = client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["access_token"]
        
        # 使用token获取用户信息
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 200
        data = response.json()
        assert data["username"] == "test_admin"
    
    def test_logout(self, client: TestClient, admin_user):
        """测试登出"""
        # 先登录
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        login_response = client.post("/api/v1/auth/login", json=login_data)
        token = login_response.json()["access_token"]
        
        # 登出
        headers = {"Authorization": f"Bearer {token}"}
        response = client.post("/api/v1/auth/logout", headers=headers)
        assert response.status_code == 200