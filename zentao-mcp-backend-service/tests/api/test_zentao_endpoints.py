"""
禅道业务相关API端点测试
"""
import pytest
from fastapi.testclient import TestClient

class TestZentaoEndpoints:
    """禅道业务端点测试类"""
    
    def get_api_token(self, client: TestClient) -> str:
        """获取API token"""
        login_data = {
            "username": "test_admin",
            "password": "admin123"
        }
        response = client.post("/api/v1/auth/login", json=login_data)
        return response.json()["access_token"]
    
    def test_get_stories_unauthorized(self, client: TestClient):
        """测试未授权获取需求列表"""
        response = client.get("/api/v1/stories/")
        assert response.status_code == 401
    
    def test_get_stories_authorized(self, client: TestClient, admin_user):
        """测试授权获取需求列表"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/stories/", headers=headers)
        assert response.status_code in [200, 422]
    
    def test_get_tasks_unauthorized(self, client: TestClient):
        """测试未授权获取任务列表"""
        response = client.get("/api/v1/tasks/")
        assert response.status_code == 401
    
    def test_get_tasks_authorized(self, client: TestClient, admin_user):
        """测试授权获取任务列表"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/tasks/", headers=headers)
        assert response.status_code in [200, 422]
    
    def test_get_bugs_unauthorized(self, client: TestClient):
        """测试未授权获取缺陷列表"""
        response = client.get("/api/v1/bugs/")
        assert response.status_code == 401
    
    def test_get_bugs_authorized(self, client: TestClient, admin_user):
        """测试授权获取缺陷列表"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/bugs/", headers=headers)
        assert response.status_code in [200, 422]
    
    def test_get_departments_unauthorized(self, client: TestClient):
        """测试未授权获取部门列表"""
        response = client.get("/api/v1/departments/")
        assert response.status_code == 401
    
    def test_get_departments_authorized(self, client: TestClient, admin_user):
        """测试授权获取部门列表"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/departments/", headers=headers)
        assert response.status_code in [200, 422]
    
    def test_get_analysis_summary_unauthorized(self, client: TestClient):
        """测试未授权获取分析摘要"""
        response = client.get("/api/v1/analysis/summary")
        assert response.status_code == 401
    
    def test_get_analysis_summary_authorized(self, client: TestClient, admin_user):
        """测试授权获取分析摘要"""
        token = self.get_api_token(client)
        headers = {"Authorization": f"Bearer {token}"}
        
        response = client.get("/api/v1/analysis/summary", headers=headers)
        assert response.status_code in [200, 422]