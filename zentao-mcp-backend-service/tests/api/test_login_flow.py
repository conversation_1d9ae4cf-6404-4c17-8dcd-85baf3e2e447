import pytest
from fastapi.testclient import TestClient
from main import app

client = TestClient(app)

def test_admin_login():
    """
    Test admin login with correct credentials.
    """
    response = client.post(
        "/api/v1/admin/auth/login",
        json={"username": "admin", "password": "Admin123!"}
    )
    assert response.status_code == 200, f"Expected status code 200, but got {response.status_code}. Response: {response.text}"
    response_data = response.json()
    assert "session_token" in response_data
    assert "user" in response_data
    assert response_data["user"]["username"] == "admin"

def test_admin_login_invalid_password():
    """
    Test admin login with incorrect password.
    """
    response = client.post(
        "/api/v1/admin/auth/login",
        json={"username": "admin", "password": "wrongpassword"}
    )
    assert response.status_code == 401, f"Expected status code 401, but got {response.status_code}. Response: {response.text}"
    response_data = response.json()
    assert response_data["detail"] == "用户名或密码错误"

def test_admin_login_nonexistent_user():
    """
    Test admin login with a username that does not exist.
    """
    response = client.post(
        "/api/v1/admin/auth/login",
        json={"username": "nonexistentuser", "password": "somepassword"}
    )
    assert response.status_code == 404, f"Expected status code 404, but got {response.status_code}. Response: {response.text}"
    response_data = response.json()
    assert response_data["detail"] == "用户不存在"