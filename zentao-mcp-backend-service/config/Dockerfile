# 统一的生产级 Dockerfile
# 支持 test 和 prod 环境，通过环境变量控制差异
ARG PIP_INDEX_URL=https://pypi.tuna.tsinghua.edu.cn/simple
ARG PIP_TRUSTED_HOST=pypi.tuna.tsinghua.edu.cn
ARG APT_MIRROR=mirrors.tuna.tsinghua.edu.cn

FROM python:3.11-slim

# 重新声明ARG变量（FROM之后需要重新声明）
ARG PIP_INDEX_URL
ARG PIP_TRUSTED_HOST
ARG APT_MIRROR

WORKDIR /app

# 配置时区和语言环境
ENV TZ=Asia/Shanghai \
    LANG=zh_CN.UTF-8 \
    LC_ALL=zh_CN.UTF-8 \
    DEBIAN_FRONTEND=noninteractive

# 配置pip镜像源和APT镜像源
ENV PIP_INDEX_URL=${PIP_INDEX_URL} \
    PIP_TRUSTED_HOST=${PIP_TRUSTED_HOST} \
    APT_MIRROR=${APT_MIRROR}

# 配置APT镜像源并安装系统依赖
RUN set -eux; \
    # 完全替换APT源配置
    rm -rf /etc/apt/sources.list.d/*; \
    mkdir -p /etc/apt; \
    # 配置清华大学镜像源（使用trixie源）
    echo "deb https://${APT_MIRROR}/debian/ trixie main" > /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian/ trixie-updates main" >> /etc/apt/sources.list; \
    echo "deb https://${APT_MIRROR}/debian-security/ trixie-security main" >> /etc/apt/sources.list; \
    # 清理可能存在的其他源文件
    find /etc/apt -name "*.list" -not -name "sources.list" -delete || true; \
    # 更新包列表并安装依赖
    apt-get update; \
    apt-get install -y --no-install-recommends \
        curl \
        locales \
        ca-certificates; \
    # 配置语言环境
    echo "zh_CN.UTF-8 UTF-8" > /etc/locale.gen; \
    locale-gen; \
    # 清理APT缓存
    apt-get clean; \
    rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*;

# 复制依赖文件
COPY requirements.txt .

# 安装Python依赖（使用国内镜像源）
RUN set -eux; \
    if [ -n "${PIP_INDEX_URL}" ] && [ -n "${PIP_TRUSTED_HOST}" ]; then \
        pip install --no-cache-dir -i ${PIP_INDEX_URL} --trusted-host ${PIP_TRUSTED_HOST} -r requirements.txt; \
    else \
        pip install --no-cache-dir -r requirements.txt; \
    fi; \
    # 清理pip缓存
    pip cache purge || true;

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p data logs

# 创建非root用户
RUN useradd -m -u 1000 appuser && chown -R appuser:appuser /app
USER appuser

# 暴露端口
EXPOSE 8000

# 健康检查（通过环境变量控制启动等待时间）
HEALTHCHECK --interval=30s --timeout=10s --start-period=30s --retries=3 \
    CMD curl -f http://localhost:8000/health || exit 1

# 启动命令（通过环境变量控制workers数量）
CMD ["sh", "-c", "python -m uvicorn main:app --host 0.0.0.0 --port 8000 --workers ${WORKERS:-1}"]
