version: '3.8'

services:
  backend:
    build:
      context: ../
      dockerfile: config/Dockerfile
    container_name: ${CONTAINER_NAME}
    env_file:
      - ./environments/${ENVIRONMENT}.env
    ports:
      - "8000:8000"
    volumes:
      - ${VOLUME_PREFIX}-logs:/app/logs
      - ${VOLUME_PREFIX}-data:/app/data
    networks:
      - ${NETWORK_NAME}
    restart: unless-stopped
    deploy:
      replicas: ${REPLICAS:-1}
      resources:
        limits:
          memory: ${MEMORY_LIMIT:-512M}
          cpus: ${CPU_LIMIT:-0.25}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: ${HEALTH_START_PERIOD:-30s}

networks:
  ${NETWORK_NAME}:
    driver: bridge
    name: ${NETWORK_NAME}

volumes:
  ${VOLUME_PREFIX}-logs:
    driver: local
  ${VOLUME_PREFIX}-data:
    driver: local
