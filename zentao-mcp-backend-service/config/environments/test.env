# 测试环境配置
APP_NAME=zentao-mcp-backend
APP_VERSION=test
ENVIRONMENT=testing

# 数据库配置 - SQLite测试环境
DATABASE_URL=sqlite:///./data/zentao_mcp_test.db
DATABASE_ECHO=false

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=2
RELOAD=false

# 安全配置
SECRET_KEY=test-secret-key-not-for-production
DEBUG=false
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=json

# 禅道API配置
ZENTAO_ENV=beta
ZENTAO_TIMEOUT=20

# 测试配置
ENABLE_DOCS=true
ENABLE_PROFILER=false

# 容器配置
CONTAINER_NAME=zentao-backend-test
NETWORK_NAME=zentao-test-network
VOLUME_PREFIX=zentao-backend-test
ENVIRONMENT=test

# Docker Compose 资源配置
MEMORY_LIMIT=512M
CPU_LIMIT=0.25
HEALTH_START_PERIOD=30s
REPLICAS=1
