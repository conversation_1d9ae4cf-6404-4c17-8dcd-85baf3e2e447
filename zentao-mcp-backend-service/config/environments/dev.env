# 开发环境配置
APP_NAME=zentao-mcp-backend
APP_VERSION=dev
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=sqlite:///./data/zentao_mcp_dev.db
DATABASE_ECHO=true

# 服务配置
HOST=0.0.0.0
PORT=8000
WORKERS=1
RELOAD=true

# 安全配置
SECRET_KEY=dev-secret-key-not-for-production
DEBUG=true
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# 日志配置
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed

# 禅道API配置
ZENTAO_ENV=beta
ZENTAO_TIMEOUT=30

# 开发工具配置
ENABLE_DOCS=true
ENABLE_PROFILER=true

# 容器配置
CONTAINER_NAME=zentao-backend-dev
NETWORK_NAME=zentao-dev-network
