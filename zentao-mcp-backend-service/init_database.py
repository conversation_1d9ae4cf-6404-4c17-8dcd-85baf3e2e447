#!/usr/bin/env python3
"""
数据库初始化脚本
用于创建数据库表和初始化数据
"""
import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent))

from app.core.config import settings
from app.core.database import engine, Base
from sqlalchemy import text
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_data_directory():
    """创建数据目录"""
    data_dir = Path("./data")
    if not data_dir.exists():
        data_dir.mkdir(parents=True, exist_ok=True)
        logger.info(f"✅ 创建数据目录: {data_dir.absolute()}")
    else:
        logger.info(f"✅ 数据目录已存在: {data_dir.absolute()}")

def check_database_connection():
    """检查数据库连接"""
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            logger.info("✅ 数据库连接成功")
            return True
    except Exception as e:
        logger.error(f"❌ 数据库连接失败: {e}")
        return False

def create_tables():
    """创建数据库表"""
    try:
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        logger.info("✅ 数据库表创建成功")
        
        # 显示创建的表
        with engine.connect() as conn:
            result = conn.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result]
            if tables:
                logger.info(f"📋 已创建的表: {', '.join(tables)}")
            else:
                logger.info("📋 暂无数据表")
                
    except Exception as e:
        logger.error(f"❌ 创建数据库表失败: {e}")
        return False
    return True

def show_database_info():
    """显示数据库信息"""
    logger.info("📊 数据库配置信息:")
    logger.info(f"   数据库URL: {settings.database_url}")
    
    # 获取数据库文件路径
    if "sqlite:///" in settings.database_url:
        db_path = settings.database_url.replace("sqlite:///", "")
        if db_path.startswith("./"):
            db_path = Path(db_path).absolute()
        logger.info(f"   数据库文件: {db_path}")
        
        # 检查文件大小
        if Path(db_path).exists():
            size = Path(db_path).stat().st_size
            logger.info(f"   文件大小: {size} bytes")
        else:
            logger.info("   文件状态: 不存在（将在首次使用时创建）")

def main():
    """主函数"""
    logger.info("🚀 开始初始化数据库...")
    
    # 1. 创建数据目录
    create_data_directory()
    
    # 2. 显示数据库信息
    show_database_info()
    
    # 3. 检查数据库连接
    if not check_database_connection():
        logger.error("❌ 数据库初始化失败：无法连接数据库")
        sys.exit(1)
    
    # 4. 创建数据库表
    if not create_tables():
        logger.error("❌ 数据库初始化失败：无法创建表")
        sys.exit(1)
    
    logger.info("🎉 数据库初始化完成！")
    
    # 5. 提供使用提示
    logger.info("\n📝 使用提示:")
    logger.info("   - 启动服务: python -m uvicorn main:app --reload")
    logger.info("   - API文档: http://localhost:8000/docs")
    logger.info("   - 健康检查: http://localhost:8000/health")

if __name__ == "__main__":
    main()