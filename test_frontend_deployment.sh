#!/bin/bash
# ============================================================================
# 前端项目部署完整测试脚本
# ============================================================================

echo "=== 前端项目部署完整测试 ==="
echo

exit_code=0

# 测试1: help命令
echo "1. 测试 help 命令:"
echo "命令: cd zentao-mcp-admin-web && bash deploy.sh --help"
echo "预期: 显示帮助信息，无错误信息"
echo "结果:"
cd zentao-mcp-admin-web
if bash deploy.sh --help 2>&1 | grep -q "ERROR"; then
    echo "❌ FAILED: help命令仍然显示错误信息"
    exit_code=1
else
    echo "✅ PASSED: help命令正常工作"
fi
cd ..
echo

# 测试2: 统一部署脚本测试环境部署
echo "2. 测试统一部署脚本 - 测试环境部署:"
echo "命令: ./deploy-zentao.sh frontend test deploy"
echo "预期: 成功部署，无错误"
echo "结果:"
if ./deploy-zentao.sh frontend test deploy >/dev/null 2>&1; then
    echo "✅ PASSED: 测试环境部署成功"
else
    echo "❌ FAILED: 测试环境部署失败"
    exit_code=1
fi
echo

# 测试3: 服务状态检查
echo "3. 测试服务状态:"
echo "命令: ./deploy-zentao.sh frontend test status"
echo "预期: 容器运行中且健康"
echo "结果:"
status_output=$(./deploy-zentao.sh frontend test status 2>/dev/null)
if echo "$status_output" | grep -q "Up.*healthy"; then
    echo "✅ PASSED: 服务运行正常且健康"
else
    echo "❌ FAILED: 服务状态异常"
    echo "状态输出: $status_output"
    exit_code=1
fi
echo

# 测试4: HTTP健康检查
echo "4. 测试HTTP健康检查:"
echo "命令: curl -f http://localhost:3000/health"
echo "预期: 返回 'healthy'"
echo "结果:"
if curl -f -s http://localhost:3000/health | grep -q "healthy"; then
    echo "✅ PASSED: HTTP健康检查通过"
else
    echo "❌ FAILED: HTTP健康检查失败"
    exit_code=1
fi
echo

# 测试5: 前端页面访问
echo "5. 测试前端页面访问:"
echo "命令: curl -f http://localhost:3000/"
echo "预期: 返回HTML页面"
echo "结果:"
if curl -f -s http://localhost:3000/ | grep -q "<html"; then
    echo "✅ PASSED: 前端页面可正常访问"
else
    echo "❌ FAILED: 前端页面访问失败"
    exit_code=1
fi
echo

# 测试6: API模拟响应
echo "6. 测试API模拟响应:"
echo "命令: curl -f http://localhost:3000/api/test"
echo "预期: 返回测试环境JSON响应"
echo "结果:"
api_response=$(curl -f -s http://localhost:3000/api/test)
if echo "$api_response" | grep -q "test_mode"; then
    echo "✅ PASSED: API模拟响应正常"
    echo "响应内容: $api_response"
else
    echo "❌ FAILED: API模拟响应异常"
    echo "响应内容: $api_response"
    exit_code=1
fi
echo

# 测试7: 容器日志检查
echo "7. 测试容器日志:"
echo "预期: 无错误日志"
echo "结果:"
log_output=$(./deploy-zentao.sh frontend test logs 2>/dev/null | tail -10)
if echo "$log_output" | grep -q "emerg\|error"; then
    echo "❌ FAILED: 发现错误日志"
    echo "错误日志: $log_output"
    exit_code=1
else
    echo "✅ PASSED: 容器日志正常"
fi
echo

# 测试8: 端口监听检查
echo "8. 测试端口监听:"
echo "命令: netstat -tuln | grep :3000"
echo "预期: 端口3000正在监听"
echo "结果:"
if netstat -tuln 2>/dev/null | grep -q ":3000.*LISTEN" || lsof -i :3000 >/dev/null 2>&1; then
    echo "✅ PASSED: 端口3000正在监听"
else
    echo "❌ FAILED: 端口3000未监听"
    exit_code=1
fi
echo

echo "=== 测试完成 ==="
if [[ $exit_code -eq 0 ]]; then
    echo "🎉 所有测试通过！前端项目部署完全正常！"
    echo
    echo "📋 测试总结:"
    echo "✅ help命令修复成功"
    echo "✅ Dockerfile构建问题修复"
    echo "✅ nginx配置问题修复"
    echo "✅ 容器运行正常"
    echo "✅ 服务健康检查通过"
    echo "✅ 前端页面可访问"
    echo "✅ API模拟响应正常"
    echo "✅ 日志无错误"
    echo "✅ 端口监听正常"
    echo
    echo "🌐 访问地址: http://localhost:3000"
else
    echo "❌ 部分测试失败，需要进一步检查"
fi

exit $exit_code
