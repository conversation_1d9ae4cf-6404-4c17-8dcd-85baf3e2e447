#!/bin/bash
# ============================================================================
# 核心修复验证测试脚本
# ============================================================================

echo "=== 核心修复验证测试 ==="
echo

exit_code=0

# 测试项目列表
projects=("zentao-mcp-backend-service" "zentao-mcp-admin-web" "zentao-mcp-client")
project_names=("后端服务" "前端Web" "客户端")

# 测试1: help命令测试
echo "1. Help命令测试:"
for i in "${!projects[@]}"; do
    project="${projects[$i]}"
    name="${project_names[$i]}"
    
    echo "  $name:"
    cd "$project"
    if bash deploy.sh --help 2>&1 | grep -q "ERROR"; then
        echo "    ❌ FAILED"
        exit_code=1
    else
        echo "    ✅ PASSED"
    fi
    cd ..
done
echo

# 测试2: 未知参数处理测试
echo "2. 未知参数处理测试:"
for i in "${!projects[@]}"; do
    project="${projects[$i]}"
    name="${project_names[$i]}"
    
    echo "  $name:"
    cd "$project"
    output=$(bash deploy.sh unknown_param 2>&1)
    
    if echo "$output" | grep -q "未识别的参数" && echo "$output" | grep -q "用法:"; then
        echo "    ✅ PASSED"
    else
        echo "    ❌ FAILED"
        exit_code=1
    fi
    cd ..
done
echo

# 测试3: 环境参数解析测试（关键修复）
echo "3. 环境参数解析测试:"
test_cases=(
    "zentao-mcp-backend-service:prod"
    "zentao-mcp-admin-web:test"
    "zentao-mcp-client:dev"
)

for test_case in "${test_cases[@]}"; do
    project="${test_case%%:*}"
    env="${test_case##*:}"
    
    name=""
    case $project in
        "zentao-mcp-backend-service") name="后端服务" ;;
        "zentao-mcp-admin-web") name="前端Web" ;;
        "zentao-mcp-client") name="客户端" ;;
    esac
    
    echo "  $name ($env):"
    cd "$project"
    output=$(timeout 5s bash deploy.sh $env deploy 2>&1 | head -15)
    
    if echo "$output" | grep -q "环境: $env"; then
        echo "    ✅ PASSED"
    else
        echo "    ❌ FAILED"
        echo "    实际输出: $(echo "$output" | grep "环境:")"
        exit_code=1
    fi
    cd ..
done
echo

# 测试4: 前端项目容器状态检查
echo "4. 前端项目容器状态检查:"
cd zentao-mcp-admin-web
if ./deploy.sh test status 2>/dev/null | grep -q "Up.*healthy"; then
    echo "  前端Web: ✅ PASSED (容器运行正常)"
else
    echo "  前端Web: ❌ FAILED (容器状态异常)"
    exit_code=1
fi
cd ..
echo

echo "=== 测试完成 ==="
if [[ $exit_code -eq 0 ]]; then
    echo "🎉 所有核心修复验证通过！"
    echo
    echo "📋 验证通过的修复:"
    echo "✅ Help命令错误处理冲突 - 已修复"
    echo "✅ 未知参数处理缺失 - 已修复"
    echo "✅ 环境参数解析失败 - 已修复（关键问题）"
    echo "✅ 前端Docker构建和部署 - 已修复"
    echo
    echo "🎯 关键成果:"
    echo "- 所有脚本的help命令正常工作（无错误信息）"
    echo "- 所有脚本正确拒绝未知参数并显示帮助"
    echo "- 所有脚本正确解析环境参数（dev/test/prod）"
    echo "- 前端项目可以成功部署到测试环境"
    echo
    echo "现在用户可以安全地使用所有部署脚本！"
else
    echo "❌ 部分验证失败，需要进一步检查"
fi

exit $exit_code
