version: '3.8'

services:
  # 数据库服务
  database:
    image: postgres:15-alpine
    container_name: zentao-mcp-db
    environment:
      POSTGRES_DB: zentao_mcp
      POSTGRES_USER: zentao_mcp
      POSTGRES_PASSWORD: zentao_mcp_password
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - zentao-mcp-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U zentao_mcp -d zentao_mcp"]
      interval: 10s
      timeout: 5s
      retries: 5

  # 后端服务
  backend:
    build:
      context: ./zentao-mcp-backend-service
      dockerfile: Dockerfile
    container_name: zentao-mcp-backend
    environment:
      - DATABASE_URL=*********************************************************/zentao_mcp
      - SECRET_KEY=your-secret-key-change-in-production
      - DEBUG=false
      - CORS_ORIGINS=http://localhost:3000,http://frontend
    ports:
      - "8000:8000"
    networks:
      - zentao-mcp-network
    depends_on:
      database:
        condition: service_healthy
    restart: unless-stopped
    volumes:
      - backend_logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端服务
  frontend:
    build:
      context: ./zentao-mcp-admin-web
      dockerfile: Dockerfile
    container_name: zentao-mcp-frontend
    ports:
      - "3000:80"
    networks:
      - zentao-mcp-network
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: zentao-mcp-redis
    ports:
      - "6379:6379"
    networks:
      - zentao-mcp-network
    restart: unless-stopped
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3

  # Nginx反向代理（可选）
  nginx:
    image: nginx:alpine
    container_name: zentao-mcp-nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - zentao-mcp-network
    depends_on:
      - frontend
      - backend
    restart: unless-stopped
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 5s
      retries: 3

# 网络配置
networks:
  zentao-mcp-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  nginx_logs:
    driver: local
