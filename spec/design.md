
# 技术设计文档 (design.md) - v3.2 (最终版)

## 1. 项目目标

本项目旨在将现有的本地化 `zentao-mcp-server` 重构为“云端服务 + 轻量级客户端”架构。核心目标是将封装了禅道(Zentao)核心访问逻辑的代码部署于中心服务器，并通过API密钥提供安全可控的访问。

同时，本项目将集成一个完整的用户权限管理系统，支持多角色（用户/管理员）认证与授权、安全的会话管理和详细的操作审计。最终目标是构建一个安全、可集中管理、易于维护和扩展的中心化服务。

## 2. 技术选型与理由

| 组件 | 技术选型 | 理由 | 备选方案 |
| :--- | :--- | :--- | :--- |
| **后端语言** | Python 3.10+ | 与现有 `zentao-mcp-server` 技术栈完全对齐，确保代码平滑迁移。 | - |
| **后端框架** | FastAPI | 性能卓越，原生支持异步，**自动生成OpenAPI文档作为开发契约**。 | Django REST Framework |
| **数据库** | **SQLite** | **简单、零配置、服务器无依赖**，数据以单个文件形式存在，非常适合本项目轻量级、快速部署的需求。 | MySQL/PostgreSQL |
| **密码哈希** | `passlib` with bcrypt | 业界标准，提供安全的哈希和验证机制。 | `hashlib` |
| **配置管理** | `pydantic-settings` | 支持从环境变量和 `.env` 文件加载配置，实现开发与生产环境的轻松分离。 | 手动解析 |
| **部署方案** | Docker | 实现环境标准化，简化依赖管理。 | - |
| **客户端框架** | FastMCP SDK | **核心依赖**。利用其协议兼容性，快速构建本地代理。 | - |
| **Admin Web** | Vue.js | 轻量、高效，适合快速开发单页面后台。 | React |

## 3. 总体架构

系统分为后端服务和轻量级客户端。**Admin Web前端构建后，其静态文件将由FastAPI后端在根路径 `/` 下统一提供服务**，简化部署。

```mermaid
graph TD
    subgraph 用户本地环境
        UserApp[用户AI Agent/脚本]
        MCP_Client[Zentao MCP Client]
        UserApp -- 本地调用 --> MCP_Client
    end

    subgraph 私有云/服务器 (Docker Container)
        
        subgraph Zentao MCP Backend Service (FastAPI)
            StaticFiles[Static Files (Admin Web)]
            APILayer[API层: 路由与验证]
            ServiceLayer[服务层: 业务逻辑]
            DataLayer[数据层: 数据库交互]

            APILayer -- 调用 --> ServiceLayer
            ServiceLayer -- 操作 --> DataLayer
        end

        Database[(SQLite File)]
        ZentaoAPI[Zentao API]
        Admin[管理员]

        Admin -- 浏览器访问 (Session) --> StaticFiles
        StaticFiles -- Admin API调用 --> APILayer
        DataLayer -- 读/写 --> Database
        ServiceLayer -- 调用外部 --> ZentaoAPI
    end

    MCP_Client -- MCP Service API调用 (HTTPS + API Key) --> APILayer

    style UserApp fill:#cde4ff
    style Admin fill:#cde4ff
    style MCP_Client fill:#e6ffc2
    style APILayer fill:#fff2c2
    style ServiceLayer fill:#fff2c2
    style DataLayer fill:#fff2c2
    style ZentaoAPI fill:#ffcccc
```

## 4. 项目目录结构 (后端服务)

```
zentao-mcp-backend-service/
├── app/
│   ├── __init__.py
│   ├── api/
│   │   └── v1/
│   │       ├── auth.py
│   │       ├── users.py
│   │       ├── api_keys.py
│   │       └── endpoints/
│   │           ├── zentao_tools.py
│   │           └── zentao_resources.py
│   ├── core/
│   │   ├── security.py
│   │   └── config.py
│   ├── crud/
│   │   ├── base.py
│   │   ├── crud_user.py
│   │   └── crud_api_key.py
│   ├── models/
│   │   ├── user.py
│   │   ├── api_key.py
│   │   ├── audit_log.py
│   │   └── user_session.py
│   ├── schemas/
│   │   ├── user.py
│   │   ├── token.py
│   │   ├── error.py        # 客户端专用错误Schema
│   │   └── zentao/
│   │           ├── 其他禅道使用schemas
│   │           └── ...
│   └── services/
│       ├── user_service.py
│       ├── auth_service.py
│       ├── audit_service.py
│       └── zentao/
│               ├── 其他禅道使用services
│               └── ...
├── migrations/               # Alembic 数据库迁移脚本
├── static/                   # 存放Admin Web构建后的静态文件 (index.html, css, js)
├── .env.example
├── Dockerfile
├── main.py                   # FastAPI 应用入口 (包含静态文件挂载)
├── pyproject.toml
└── requirements.txt          # 使用pip管理依赖
```

## 5. 数据表结构 (SQLite)

**`users` 表**

```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    hashed_password TEXT NOT NULL,
    user_type TEXT NOT NULL DEFAULT 'user',
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**`api_keys` 表**

```sql
CREATE TABLE api_keys (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    key_hash TEXT UNIQUE NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    revoked_at TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
CREATE INDEX idx_api_keys_user_id ON api_keys(user_id);
```

**`audit_logs` 表**

```sql
CREATE TABLE audit_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action TEXT NOT NULL,
    details TEXT, -- JSON stored as TEXT
    ip_address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL
);
CREATE INDEX idx_audit_logs_user_id ON audit_logs(user_id);
```

**`user_sessions` 表**

```sql
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    session_token TEXT UNIQUE NOT NULL,
    ip_address TEXT,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
```

## 6. API 设计

### 6.1 Admin API (供Web管理界面使用, 需会话认证)

- **认证**: `POST /api/v1/auth/login`, `POST /api/v1/auth/logout`
- **用户管理**:
    - `GET /api/v1/users`
    - `POST /api/v1/users`
    - `GET /api/v1/users/{user_id}`
    - `PUT /api/v1/users/{user_id}`
    - `DELETE /api/v1/users/{user_id}` (软删除)
    - `POST /api/v1/users/{user_id}/reset-password`
- **个人信息**: `GET /api/v1/users/me`, `POST /api/v1/users/me/change-password`
- **API Key管理**: `GET /api/v1/users/{user_id}/keys`, `POST /api/v1/users/{user_id}/keys`, `DELETE /api/v1/keys/{key_id}`

### 6.2 MCP Service API (供轻量级客户端使用, 需API Key认证)

- **端点**: 遵循RESTful风格，例如:
    - `POST /api/v1/mcp/tools/zentao_get_all_projects`
    - `GET /api/v1/mcp/resources/some_resource`
- **客户端映射**: 客户端将采用**硬编码**的方式将本地调用映射到上述后端API端点。

## 7. 安全与认证设计

### 7.1 密码安全
- **存储**: 所有用户密码使用 `passlib` 库和 `bcrypt` 算法进行哈希处理后存储。
- **验证**: 登录时，将用户输入的密码与存储的哈希值进行比较验证。

### 7.2 认证机制
- **API Key**: 客户端发来的Key，系统对其进行哈希计算，并与数据库中存储的 `key_hash` 进行比对。
- **会话 (Session)**: 用户Web登录成功后，后端生成安全的`session_token`，存入`user_sessions`表并返回给前端。后续请求通过此Token验证身份。

### 7.3 权限控制
- 权限检查通过FastAPI的依赖注入系统实现。
- 创建可重用的依赖项，如 `get_current_active_user` 和 `get_current_active_admin`，应用于需要相应权限的API端点。

## 8. 错误处理机制

系统沿用HTTP状态码进行错误反馈。对于客户端调用的MCP Service API，当发生错误时，响应体将遵循一个标准化的JSON结构，以便客户端解析。

**标准错误响应体 (schemas/error.py):**
```json
{
  "detail": {
    "error_code": "ZENTAO_CONNECTION_FAILED",
    "message": "无法连接到上游Zentao服务，请检查网络或服务状态。"
  }
}
```

- **`400/422`**: 业务逻辑或验证错误。
- **`401/403`**: 认证或权限错误。
- **`404`**: 资源未找到。
- **`500`**: 服务器内部未知错误。
- **`502/503/504`**: 上游服务（如Zentao API）错误。

## 9. 部署与构建

- **构建流程**: `Dockerfile` 将使用标准的`pip install -r requirements.txt`来安装依赖。
- **部署方案**: Vue.js前端项目在开发完成后，执行`npm run build`，将其生成的静态文件（`dist`目录下的内容）复制到后端项目的`static/`目录下，然后一同构建Docker镜像进行部署。
