
# 需求文档 (requirements.md) - v2.2 (最终版)

## 1. 介绍

本项目旨在将现有的本地化MCP数据访问工具改造为一个中心化的云服务。该服务将通过一个轻量级客户端提供给用户，以解决当前方案中核心逻辑暴露、版本更新困难、访问控制缺失和维护成本高昂的问题。

此版本**集成了完整的用户权限管理系统**，支持多用户、双角色（**普通用户**、**管理员**）的精细化访问控制。最终目标是实现核心逻辑保护、集中化管理、安全可控的用户和权限体系，并为最终用户提供与现有习惯完全兼容的无缝体验。

## 2. 需求列表

### **后端服务 (Backend Service)**

#### **需求 1: 核心逻辑与API服务**

**用户故事：**  作为一名开发者，我希望将现有的MCP核心业务逻辑重构并封装成独立的服务模块，通过一组标准化的HTTP API接口暴露出来，以便保护我们的核心代码资产，并为不同的客户端提供统一、清晰的数据访问入口。  
**验收标准:**   
1.1. 现有的FastMCP数据处理逻辑被完整地封存放在在一个独立的业务服务模块文件夹中（例如，在`services/zentao`目录下），与FastAPI的路由层代码解耦。  
1.2. 为不同的MCP功能提供对应的HTTP API端点，统一归属在 `/api/v1/mcp/` 路径下（例如，工具调用可能是 `/api/v1/mcp/tools/{tool_name}`）。  
1.3. 当这些端点收到请求时，系统应执行相应的业务逻辑，并以标准JSON格式一次性返回全部结果。

---

#### **需求 2: 用户认证与授权**

**用户故事：**  作为一名系统管理员，我需要一个强大的认证与授权体系，以确保只有经过授权的用户才能访问API服务和管理功能，从而保障系统的整体安全。  
**验收标准:**   
2.1. **用户角色与权限：** 系统必须支持两种用户角色：
    - **普通用户 (User):** 可以在Web界面管理自己的信息（如密码）和API Key。其生成的API Key用于访问核心MCP服务。
    - **管理员 (Admin):** 可以管理所有用户（包括其他管理员）和API Key。
2.2. **系统管理员保障：** 系统必须始终保持至少有一名处于激活状态的管理员账户。
2.3. **API Key认证：** 所有对核心MCP服务（`/api/v1/mcp/`下的所有端点）的调用，必须通过请求头 `Authorization: Bearer <API_KEY>` 的形式传递API Key进行身份验证。
2.4. **权限验证：** 系统必须能够验证传入的API Key所关联的用户是否处于“激活”状态。对于无效、被吊销或关联用户被停用的Key，系统应返回 `401 Unauthorized` 或 `403 Forbidden` 的HTTP错误状态码。
2.5. **管理功能权限：** 所有管理功能的API端点必须验证用户是否具备管理员权限。

---

#### **需求 3: 用户管理**

**用户故事：** 作为一名管理员，我需要在Web界面上方便地创建、查询、更新和停用用户，以有效管理系统的所有账户。  
**验收标准:**
3.1. **用户创建：** 管理员可以创建普通用户和管理员。创建时必须提供用户名、密码、邮箱和用户类型。用户名和邮箱必须唯一。
3.2. **密码和Key的安全性：** 创建新用户时，其初始密码由系统生成并**仅在创建成功时显示一次**。管理员需负责安全地将该密码交付给用户。
3.3. **用户停用（软删除）：** 管理员可以停用任何用户。用户被停用后，其账户将无法登录，关联的API Key和会话也将全部失效。此操作为软删除（例如，标记`is_active=false`），以保留用户数据用于审计。
3.4. **安全约束：** 管理员不能停用自己的账户，如果他们是系统中最后一名激活状态的管理员。
3.5. **密码管理：**
    - 用户可以修改自己的密码。
    - 管理员可以重置任何用户的密码。
    - 密码必须符合安全要求（例如，最少8位，包含字母和数字），并使用强哈希算法（如bcrypt）存储。
    - 密码修改或重置后，该用户的所有其他活跃会话应被强制下线。

---

#### **需求 4: API Key管理**

**用户故事：** 作为一名管理员，我需要在一个Web界面上管理所有用户的API Key；同时作为一名普通用户，我也需要能方便地管理我自己的API Key。  
**验收标准:**
4.1. **关联用户：** 每个API Key都必须关联一个具体的用户。
4.2. **管理员功能：** 管理员可以在Web界面上为任何用户生成新的API Key、吊销指定的Key，并查看所有Key的列表（包括关联用户、状态、创建日期等）。
4.3. **用户功能：** 普通用户可以在Web界面上查看、生成或重置自己的API Key。
4.4. **Key的安全性：** 当一个新的API Key被生成时，其完整的Key值**仅显示一次**。用户或管理员必须立即复制并妥善保存。之后，该Key值将无法被再次查看。

---

#### **需求 5: Admin Web管理界面**

**用户故事：**  作为一名管理员，我需要一个统一、安全的Web管理界面来执行所有用户管理、API Key管理和系统监控任务。  
**验收标准:**   
5.1. **统一入口：** 提供一个集中的Web界面，用于管理用户（需求3）和API Key（需求4）。
5.2. **访问控制：** 访问该Web界面需要通过用户名和密码登录，并进行会话管理（Session-based）。只有管理员才能登录。
5.3. **用户列表与操作：** 界面需提供用户列表，展示用户名、邮箱、角色、状态（激活/停用）等信息，并支持搜索、筛选和批量操作。
5.4. **会话管理：** 用户的密码被修改、账户被停用时，其所有Web会话必须立即失效。管理员应有权限强制下线指定用户。

---

#### **需求 6: 日志与审计**

**用户故事：**  作为一名运维人员和安全审计员，我需要系统能够记录所有API调用的关键信息和所有敏感的管理操作，以便于故障排查和安全审计。  
**验收标准:**   
6.1. **服务日志：** 每一次对API端点的调用都必须被记录。日志应包含时间戳、来源IP、使用的API Key（可部分脱敏）、端点、响应状态码和处理时长。
6.2. **审计日志：** 所有关键的用户管理操作（如用户创建/停用、权限变更、密码重置）都必须被详细记录。
6.3. **审计日志内容：** 审计日志必须包含操作时间、操作者、操作类型、目标用户和操作结果。
6.4. **日志安全：** 审计日志不能被普通用户访问或修改。

---

#### **需求 7: 系统初始化与管理员恢复**

**用户故事：**  作为首次部署系统的管理员，或在所有管理员凭证丢失的情况下，我需要一个安全的命令行工具来创建或恢复管理员账户。  
**验收标准:**   
7.1. **首次初始化：** 系统提供一个安全的命令行工具，当数据库中不存在任何管理员账户时，用于创建第一个管理员账户。
7.2. **管理员恢复：** 该工具也必须支持在已有数据的情况下，强制重置指定管理员用户的密码，用于处理管理员凭证丢失的紧急情况。
7.3. **安全输入：** 在执行创建或重置操作时，新的密码必须通过安全的、**交互式的命令行提示**来输入，而不是通过参数或环境变量。
7.4. **配置保护：** 一旦系统中存在管理员账户，通过配置文件设置的默认管理员密码将不再生效。

### **轻量级客户端 (MCP Client)**

#### **需求 8: 客户端代理与兼容性**

**用户故事：**  作为一名最终用户，我希望能使用一个新的轻量级客户端工具，它在使用上与我原来的本地工具完全一样（支持HTTP, STDIO, SSE模式），让我可以无缝切换，而不需要修改我现有的脚本和工作流程。  
**验收标准:**   
8.1. 客户端利用FastMCP SDK，在本地启动后，能像原始工具一样，监听并接收来自HTTP、STDIO和SSE三种模式的请求。  
8.2. 当客户端收到请求时（例如一个工具调用），**它必须将该请求映射并转发到后端对应的API端点**，并将后端返回的完整JSON结果（或错误）响应给本地请求方。  
8.3. 客户端在与后端服务通信时，必须自动附加配置文件中设置的API Key。

#### **需求 9: 客户端配置与分发**

**用户故事：**  作为一名最终用户，我需要一种简单的方式来配置客户端，告诉它后端服务的地址和我的个人API Key。同时，作为开发者，我需要能将这个客户端方便地打包分发给用户。  
**验收标准:**   
9.1. 客户端提供一个命令行工具，如 `mcp-client configure`，用于引导用户输入并保存后端服务URL和API Key。  
9.2. 配置文件应存储在用户主目录下的特定位置（如 `~/.mcp_client/config.ini`）。  
9.3. 客户端在启动时能自动读取该配置文件以获取连接信息。  
9.4. 客户端项目可以方便地打包成PyPI包或独立的可执行文件。

## 3. 非功能性需求

### **性能要求**
- 用户权限验证响应时间 < 100ms
- 用户列表查询支持分页，单页最多100条记录
- 审计日志查询响应时间 < 500ms

### **安全要求**
- 密码使用强哈希算法（如 bcrypt）加密存储
- 会话令牌使用安全随机生成
- 所有敏感操作（如停用用户）需要二次确认
- 防止常见的Web攻击，如SQL注入和XSS

## 4. 约束条件
- 必须兼容现有的API Key认证系统逻辑（即客户端行为不变）。
- 不能影响现有的Zentao MCP核心业务功能。
- Web管理界面必须是响应式设计，以支持不同设备的访问。
