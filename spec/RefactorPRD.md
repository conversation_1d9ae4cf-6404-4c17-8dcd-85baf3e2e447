# PRD：MCP数据访问服务化改造项目

**文档版本:**  1.0  
**发布日期:**  2025年8月27日  
**负责人:**  [您的名字]

### 1. 项目背景与目标

**1.1. 背景**  
当前，我们的MCP数据访问工程是一个基于FastMCP SDK开发的本地化工具。它以单个代码仓库的形式存在，通过HTTP或STDIO方式在本地运行和集成。随着业务发展，我们需要将此能力提供给更多的内部或外部用户使用。

**1.2. 存在的问题**  
直接分发当前工程源码存在以下核心问题：

- **核心逻辑暴露**：数据处理和访问的核心算法、商业逻辑完全暴露。
- **版本更新困难**：无法对已分发的服务进行统一、及时的升级和Bug修复。
- **访问控制缺失**：无法对使用者进行身份认证和授权，也无法追踪其使用情况。
- **维护成本高**：用户需要在本地配置和运行整个服务，对环境依赖性强。

**1.3. 项目目标**  
本项目旨在将现有的本地MCP工程改造为一套标准的\*\*“云端服务 + 轻量级客户端”\*\*架构。最终目标是：

- ​**保护核心资产**：将核心逻辑部署在中心化的私有服务器上。
- ​**实现集中管理**：实现服务的统一更新、监控和日志记录。
- ​**提供可控访问**：通过API密钥机制，对用户进行认证和授权管理。
- **优化用户体验**：为用户提供一个轻量、与现有使用习惯（**HTTP, STDIO, SSE**）完全兼容的客户端工具。

### 2. 现有架构

```undefined
[用户应用/脚本] <--- (本地HTTP/STDIO) ---> [完整的Python MCP工程 (在用户本地运行)]
```

### 3. 规划架构

```undefined
+-------------------------------------------------+
|              用户本地环境                     |
|                                                 |
| [用户应用/脚本] <--- (本地HTTP/STDIO/SSE) ---> [MCP Client] |
+-------------------------------------------------+
      ^
      | (通过互联网, 使用HTTPS + API Key加密和认证)
      v
+-------------------------------------------------+
|              您的私有云/服务器环境              |
|                                                 |
| [API网关] -> [MCP Backend Service] -> [数据库/数据源] |
|   (认证/日志)      (核心业务逻辑)                |
+-------------------------------------------------+
```

### 4. 技术栈选型  

|组件|技术|用途|备注|
| ------| ------| ------------------------------| ---------------------------------------------------|
|**后端服务**|**Python 3.9+**|核心开发语言||
||**FastAPI**|提供高性能异步HTTP API接口|**只支持Http就好**|
||**Uvicorn + Gunicorn**|ASGI服务器，用于生产环境部署|业界标准|
||**PostgreSQL/SQLite**|存储API Keys、用户信息和日志|SQLite用于快速开发，PostgreSQL用于生产|
||**Docker**|容器化部署|保证环境一致性，简化部署流程|
|**轻量级客户端**|**Python 3.9+**|核心开发语言||
||**FastMCP SDK**|**构建客户端的核心框架**|**用于快速实现MCP协议兼容的本地服务器(HTTP/STDIO/SSE)和对后端服务的请求转发**|
||**PyInstaller / setuptools**|打包和分发|PyInstaller打包成可执行文件，setuptools发布到PyPI|

### 5. 功能模块拆解  

#### 5.1. 后端服务 (MCP Backend Service)

- **模块1：核心MCP逻辑层**

  - ​**功能**: 完整保留并封装您现有的FastMCP数据访问和处理逻辑。
  - **实现**: 封装成独立的业务逻辑模块，移除所有服务器相关代码 **。**
- **模块2：API接口层 (使用FastAPI)**

  - **功能**: 暴露安全的HTTP端点，接收来自客户端的请求。
  - ​**实现**:

    - ​**标准请求/响应端点**: POST /api/v1/mcp/execute。用于处理一次性返回结果的HTTP和STDIO请求。
- **模块3：认证与授权层**

  - ​**功能**: 验证每个API请求的合法性。
  - **实现**: 使用FastAPI中间件检查请求头中的Authorization: Bearer \<API\_KEY\>，并与数据库中的记录进行比对。
- **模块4：API Key管理工具 (Admin Web)**

  - **功能**: **一个简单的后台管理Web页面**，用于API Key的生命周期管理。
  - ​**实现**:

    - 基于Vue 开发一个管理页面。
    - 提供明确的功能：

      - 为指定用户生成一个新的API Key。
      - 吊销（禁用）一个API Key。
      - 列出所有API Key及其状态和关联用户。
- **模块5：日志监控**

  - ​**功能**: ​**提供基础的API调用日志记录**，用于审计和问题排查。
  - ​**实现**:

    - 使用Python内置的logging模块。
    - 记录核心API调用事件：时间戳、来源IP、使用的API Key（可脱敏）、请求的端点、响应状态码、处理时长。
    - 日志直接输出到​**标准输出(stdout)或日志文件**，方便后续通过Docker日志或Filebeat等工具接入集中式日志系统。

#### 5.2. 轻量级客户端 (MCP Client)

此模块分发给用户，其核心是​**使用FastMCP SDK作为代理工具**。

- **模块1：配置管理**

  - ​**功能**: 读取用户的后端服务地址和API Key。
  - ​**实现**: 客户端启动时读取配置文件 (\~/.mcp\_client/config.ini)，并提供一个mcp-client configure命令引导用户设置。
- **模块2：本地服务代理 (基于FastMCP SDK)**

  - ​**功能**: 在用户本地启动一个与原始工程行为一致的MCP服务器，支持HTTP, STDIO, SSE三种模式。
  - ​**实现**:

    - ​**利用 FastMCP 的服务器功能**，在本地启动服务。
    - ​**HTTP模式**: 当本地服务器收到一个标准的HTTP请求时，客户端会捕获它，然后​**利用 FastMCP 的客户端功能**，附加上API Key，向后端服务的 POST /api/v1/mcp/execute 端点发起请求，并将结果返回。
    - ​**STDIO模式**: 当以STDIO模式启动时，客户端会监听标准输入，将接收到的指令通过HTTP请求发送到后端的 /execute 端点，并将结果打印到标准输出。
    - ​**SSE模式**: 当本地服务器收到一个要求建立SSE连接的请求时，客户端会捕获它，然后​**利用 FastMCP 的客户端功能**，向后端服务的 GET /api/v1/mcp/stream 端点发起一个流式请求。客户端会持续接收从后端返回的数据流，并实时地将这些数据转发给本地的SSE连接。
- **模块3：后端通信模块 (基于FastMCP SDK)**

  - ​**功能**: 封装所有与后端服务的安全通信。
  - ​**实现**:

    - **利用 FastMCP 的客户端功能**来处理所有出站HTTP/HTTPS请求。
    - 在发起请求前，自动从配置中读取SERVER\_URL和API\_KEY，并将Key注入到Authorization请求头中。
    - SDK负责处理网络连接、超时和错误处理。
- **模块4：打包与分发**

  - ​**功能**: 将客户端打包成一个方便安装的包。
  - ​**实现**: 使用setuptools和PyInstaller。

### 6. 实施路线图 (Roadmap)

**阶段一：后端服务 MVP**

- ​**任务1**: 剥离和封装现有MCP核心逻辑，确保支持增量返回。
- **任务2**: 使用FastAPI搭建API服务，**实现/execute**。
- **任务3**: 实现基于数据库和命令行脚本的API Key生成与认证机制。
- ​**任务4**: 使用Docker将服务容器化。

- ​**目标**: 完成一个功能完备、可安全访问的后端服务。

**阶段二：客户端开发与集成**

- ​**任务1**: 开发客户端的配置管理功能 (mcp-client configure)。
- **任务2**: **使用FastMCP SDK实现客户端的代理逻辑**，打通与后端/execute端点的连接（用于HTTP/STDIO/SSE）。
- **任务3**: 编写客户端的启动脚本，支持通过参数选择不同的运行模式。

- ​**目标**: 完成一个功能完整的、可替代原始工程的轻量级客户端。

**阶段三：测试、文档与发布**

- ​**任务1**: 编写单元测试和端到端集成测试（覆盖HTTP, STDIO, SSE三种模式）。
- ​**任务2**: 编写用户文档。
- ​**任务3**: 将客户端打包并发布。
- ​**任务4**: 部署后端服务到生产环境，并邀请用户进行测试。

- ​**目标**: 确保产品质量，准备正式发布。

### 7. 成功指标

- **技术指标**: 后端服务API成功率 > 99.9%，平均响应延迟 < 500ms（不含核心逻辑处理时间）。
- **业务指标**: 成功迁移X个现有用户到新服务架构。
- **安全指标**: 未发生未经授权的访问事件。

---
