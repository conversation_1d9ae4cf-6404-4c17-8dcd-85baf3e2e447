# 开发任务清单 (tasks.md) - v3.1 (最终版)

## [✅] 阶段零：项目初始化与环境配置

- [✅] **任务 0.1: 初始化统一项目仓库**
  - **Prompt**: 根目录下，创建三个子目录：`zentao-mcp-backend-service`，`zentao-mcp-client` 和 `zentao-token-web`。
  - _需求: N/A_
  - **完成状态**: ✅ 已完成 - 三个子项目目录已创建并配置完成
- [✅] **任务 0.2: 各模块依赖配置**
  - **Prompt**: 为 `zentao-mcp-backend-service` 和 `zentao-mcp-client` 创建 `requirements.txt` 文件，并添加各自所需的核心依赖。
  - **Prompt**: 为 `zentao-token-web` 初始化一个 Vue.js 项目 (e.g., using Vite)。
  - _需求: N/A_
  - **完成状态**: ✅ 已完成 - 后端使用 uv 管理依赖，前端使用 bun+Vite+TypeScript+Tailwind CSS

## [✅] 阶段一：后端服务 - 基础与用户权限核心

- [✅] **任务 1.1: 搭建 FastAPI 应用与数据库基础**
  - **Prompt**: 在 `zentao-mcp-backend-service` 中，创建 `main.py` 并初始化 FastAPI 应用。配置 Alembic 和 SQLAlchemy 以连接 SQLite 数据库。
  - _需求: 7.1_
  - **完成状态**: ✅ 已完成 - FastAPI 应用已搭建，SQLAlchemy 和 Alembic 配置完成
- [✅] **任务 1.2: 创建数据库模型 (Models)**
  - **Prompt**: 在 `app/models/` 目录下，为 `User`, `APIKey`, `UserSession`, `AuditLog` 创建完整的 SQLAlchemy 模型。
  - _需求: 2.1, 4.1, 5.4, 6.2_
  - **完成状态**: ✅ 已完成 - AdminUser, APIKey, UserSession, AuditLog 模型已创建
- [✅] **任务 1.3: 生成并执行首次数据库迁移**
  - **Prompt**: 使用 Alembic 根据任务 1.2 的模型生成首次数据库迁移脚本，并执行它以创建所有表。
  - _需求: 7.1_
  - **完成状态**: ✅ 已完成 - 数据库迁移脚本已生成并可执行
- [✅] **任务 1.4: 实现核心服务层逻辑 (Services)**
  - **Prompt**: 在 `app/services/` 中，创建并实现 `UserService` (处理用户创建、软删除、密码管理) 和 `AuthService` (处理登录、会话管理、密码哈希验证)。
  - _需求: 3.1, 3.3, 3.5, 5.2_
  - **完成状态**: ✅ 已完成 - 完整服务层架构已实现，包含 8 个服务类
- [✅] **任务 1.5: 实现安全与认证依赖**
  - **Prompt**: 在 `app/core/security.py` 中，编写 FastAPI 依赖项，用于验证 API Key 和会话 Token，并区分普通用户和管理员权限。
  - _需求: 2.4, 2.5_
  - **完成状态**: ✅ 已完成 - 安全认证依赖已实现
- [✅] **任务 1.6: 实现审计日志服务**
  - **Prompt**: 创建 `AuditService`，提供记录操作日志的方法。实现一个中间件或依赖项，以自动记录关键管理操作。
  - _需求: 6.2, 6.3, 6.4_
  - **完成状态**: ✅ 已完成 - AuditService 和请求日志中间件已实现
- [✅] **任务 1.7: 创建系统初始化与恢复工具**
  - **Prompt**: 创建一个独立的命令行脚本，用于安全地创建第一个管理员账户或在紧急情况下重置管理员密码。
  - _需求: 7.1, 7.2, 7.3_
  - **完成状态**: ✅ 已完成 - create_admin.py, init_system.py 等工具已创建

## [✅] 阶段二：后端服务 - API 端点实现

_(注：应优先完成任务 2.1 和 2.2，以便前端团队可以开始开发)_

- [✅] **任务 2.1: 实现认证与用户自管理 API**
  - **Prompt**: 创建 `POST /api/v1/auth/login` 端点。创建 `GET /users/me` 和 `POST /users/me/change-password` 端点。
  - _需求: 3.5, 5.2_
  - **完成状态**: ✅ 已完成 - 认证 API 和用户自管理 API 已实现
- [✅] **任务 2.2: 实现管理员用户管理 API**
  - **Prompt**: 创建完整的用户管理 CRUD API 端点 (`GET /users`, `POST /users`, `PUT /users/{id}`, `DELETE /users/{id}` (软删除), `POST /users/{id}/reset-password`)，并使用管理员权限依赖进行保护。
  - _需求: 3.1, 3.3, 3.4, 3.5_
  - **完成状态**: ✅ 已完成 - 完整的用户管理 CRUD API 已实现
- [✅] **任务 2.3: 实现 API Key 管理 API**
  - **Prompt**: 为管理员和用户创建管理其 API Key 的端点 (`GET /users/{id}/keys`, `POST /users/{id}/keys`, `DELETE /keys/{id}`等)。
  - _需求: 4.2, 4.3, 4.4_
  - **完成状态**: ✅ 已完成 - API Key 管理端点已实现
- [✅] **任务 2.4: 迁移并实现核心 MCP 服务 API**
  - **Prompt**: 将原 Zentao MCP 的核心逻辑重构并封装到 `app/services/zentao/` 下。然后，在 `app/api/v1/endpoints/` 下创建对应的 HTTP API 端点，并使用 API Key 认证依赖进行保护。
  - _需求: 1.1, 1.2, 1.3, 2.3_
  - **完成状态**: ✅ 已完成 - FastMCP 到 FastAPI 迁移 100%完成，zentao_engine 已集成
- [✅] **任务 2.5: 实现服务日志记录**
  - **Prompt**: 创建一个 FastAPI 中间件，以 JSON 格式记录所有 API 请求的关键信息到 stdout。
  - _需求: 6.1_
  - **完成状态**: ✅ 已完成 - RequestLoggingMiddleware 已实现

## [ ] 阶段三：轻量级客户端开发

- [ ] **任务 3.1: 实现客户端配置管理**
  - **Prompt**: 在 `zentao-mcp-client` 项目中，使用 `click` 或 `argparse` 实现 `mcp-client configure` 命令。
  - _需求: 9.1, 9.2_
- [ ] **任务 3.2: 实现核心代理逻辑**
  - **Prompt**: 使用 `FastMCP SDK` 启动本地服务，该服务将接收到的本地请求转发到后端对应的 MCP 服务 API 端点。
  - _需求: 8.1, 8.2, 8.3, 9.3_
- [ ] **任务 3.3: 创建客户端入口与打包配置**
  - **Prompt**: 配置 `pyproject.toml` 或 `setup.py`，使客户端可以通过命令行直接启动，并准备好 PyPI 发布和 PyInstaller 打包脚本。
  - _需求: 9.4_

## [✅] 阶段四：Admin Web 前端开发

- [✅] **任务 4.1: 实现登录页面与认证流程**
  - **Prompt**: 在 `zentao-token-web` 项目中，创建登录页面，调用后端登录 API，并将获取的会话 Token 安全地存储起来。
  - _需求: 5.2_
  - **完成状态**: ✅ 已完成 - Vue.js 项目已初始化，基础认证流程已实现
- [✅] **任务 4.2: 开发用户管理界面**
  - **Prompt**: 实现用户列表的展示（包含搜索和筛选）、创建新用户表单、编辑用户信息、以及停用/激活用户的操作。
  - _需求: 3.1, 3.3, 5.3_
  - **完成状态**: ✅ 已完成 - 用户管理界面基础功能已实现
- [✅] **任务 4.3: 开发 API Key 管理界面**
  - **Prompt**: 在用户详情页或专门的页面中，实现为用户展示、创建和吊销 API Key 的功能。确保新创建的 Key"仅显示一次"。
  - _需求: 4.2, 4.3, 4.4_
  - **完成状态**: ✅ 已完成 - API Key 管理界面基础功能已实现

## [🔄] 阶段五：测试、部署与发布

- [🔄] **任务 5.1: 后端单元与集成测试**
  - **Prompt**: 为`services`层编写单元测试，为 API 端点编写集成测试。**目标是核心业务逻辑的测试覆盖率达到 80%以上**。
  - _需求: (所有后端需求)_
  - **完成状态**: 🔄 部分完成 - 测试框架已搭建，但覆盖率需要提升到 80%以上
- [ ] **任务 5.2: 客户端测试**
  - **Prompt**: 使用`pytest-mock`模拟后端 API，验证客户端的代理逻辑和配置功能。
  - _需求: (所有客户端需求)_
- [ ] **任务 5.3: 端到端集成测试**
  - **Prompt**: 编写脚本，使用`docker-compose`启动完整后端，然后运行客户端，通过客户端调用执行一个完整的业务流程，验证结果。
  - _需求: 8.1, 8.2, 8.3_
- [ ] **任务 5.4: 后端服务 Docker 化**
  - **Prompt**: 编写`Dockerfile`。**该 Dockerfile 假定前端团队已提供构建好的静态文件（制品）并已放置在`static/`目录下**。它将负责复制整个应用代码（包括`static`目录），使用`pip`安装 Python 依赖，并启动 FastAPI 服务。
  - _需求: N/A_
- [ ] **任务 5.5: 创建 Docker Compose 配置**
  - **Prompt**: 创建一个 `docker-compose.yml` 文件，定义 `backend` 服务，并配置数据卷以持久化 SQLite 数据库文件。
  - _需求: N/A_
- [ ] **任务 5.6: 客户端打包与分发**
  - **Prompt**: 最终确认客户端打包脚本功能完备，准备发布到 PyPI 和提供独立的二进制可执行文件。
  - _需求: 9.4_

## [ ] 阶段六：文档与交接

- [ ] **任务 6.1: 完成 API 文档**
  - **Prompt**: 审查并为所有 API 端点在代码中添加详细的描述，以确保 FastAPI 自动生成的 OpenAPI 文档清晰、准确。
  - _需求: N/A_
- [ ] **任务 6.2: 编写管理员手册**
  - **Prompt**: 创建一份 Markdown 格式的管理员手册，内容至少包括：系统首次部署、创建第一个管理员、用户和 API Key 管理流程、以及紧急情况下的管理员密码恢复步骤。
  - _需求: N/A_
- [ ] **任务 6.3: 编写客户端用户指南**
  - **Prompt**: 创建一份面向最终用户的快速入门指南，说明如何下载/安装客户端、如何使用`configure`命令配置 API Key，并提供一两个基本的使用示例。
  - _需求: N/A_
