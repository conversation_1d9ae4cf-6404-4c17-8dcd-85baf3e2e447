#!/bin/bash
# ============================================================================
# 部署功能测试脚本
# 测试所有项目的部署功能和镜像源配置
# ============================================================================

set -euo pipefail

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 日志函数
log_info() {
    echo -e "${BLUE}[TEST]${NC} $(date '+%H:%M:%S') $1"
}

log_success() {
    echo -e "${GREEN}[PASS]${NC} $(date '+%H:%M:%S') $1"
    ((PASSED_TESTS++))
}

log_error() {
    echo -e "${RED}[FAIL]${NC} $(date '+%H:%M:%S') $1"
    ((FAILED_TESTS++))
}

log_warning() {
    echo -e "${YELLOW}[WARN]${NC} $(date '+%H:%M:%S') $1"
}

# 测试函数
run_test() {
    local test_name="$1"
    local test_command="$2"
    
    ((TOTAL_TESTS++))
    log_info "测试: $test_name"
    
    if eval "$test_command" &>/dev/null; then
        log_success "$test_name"
        return 0
    else
        log_error "$test_name"
        return 1
    fi
}

# 测试部署脚本存在性
test_deployment_scripts() {
    log_info "========== 测试部署脚本 =========="
    
    run_test "统一部署脚本存在" "[[ -f 'deploy-zentao.sh' ]]"
    run_test "统一部署脚本可执行" "[[ -x 'deploy-zentao.sh' ]]"
    run_test "后端部署脚本存在" "[[ -f 'zentao-mcp-backend-service/deploy.sh' ]]"
    run_test "前端部署脚本存在" "[[ -f 'zentao-mcp-admin-web/deploy.sh' ]]"
    run_test "客户端部署脚本存在" "[[ -f 'zentao-mcp-client/deploy.sh' ]]"
}

# 测试镜像源配置
test_mirror_configuration() {
    log_info "========== 测试镜像源配置 =========="
    
    run_test "镜像源配置文件存在" "[[ -f 'config/mirrors.env' ]]"
    run_test "镜像源配置脚本存在" "[[ -f 'scripts/setup-mirrors.sh' ]]"
    run_test "镜像源配置脚本可执行" "[[ -x 'scripts/setup-mirrors.sh' ]]"
    
    # 测试配置文件内容
    run_test "pip镜像源配置存在" "grep -q 'PIP_INDEX_URL' 'config/mirrors.env'"
    run_test "npm镜像源配置存在" "grep -q 'NPM_REGISTRY' 'config/mirrors.env'"
    run_test "apk镜像源配置存在" "grep -q 'APK_MIRROR' 'config/mirrors.env'"
}

# 测试Dockerfile优化
test_dockerfile_optimization() {
    log_info "========== 测试Dockerfile优化 =========="
    
    # 测试后端Dockerfile
    run_test "后端开发Dockerfile包含pip配置" "grep -q 'PIP_INDEX_URL' 'zentao-mcp-backend-service/config/docker/Dockerfile.dev'"
    run_test "后端生产Dockerfile包含pip配置" "grep -q 'PIP_INDEX_URL' 'zentao-mcp-backend-service/config/docker/Dockerfile.prod'"
    
    # 测试前端Dockerfile
    run_test "前端开发Dockerfile包含npm配置" "grep -q 'NPM_REGISTRY' 'zentao-mcp-admin-web/config/Dockerfile.dev'"
    run_test "前端测试Dockerfile包含npm配置" "grep -q 'NPM_REGISTRY' 'zentao-mcp-admin-web/config/Dockerfile.test'"
    
    # 测试客户端Dockerfile
    run_test "客户端开发Dockerfile包含pip配置" "grep -q 'PIP_INDEX_URL' 'zentao-mcp-client/config/Dockerfile.dev'"
}

# 测试部署脚本功能
test_deployment_functionality() {
    log_info "========== 测试部署脚本功能 =========="
    
    # 测试帮助信息
    run_test "统一部署脚本帮助" "./deploy-zentao.sh --help"
    run_test "镜像源配置脚本帮助" "./scripts/setup-mirrors.sh --help"
    
    # 测试状态检查
    run_test "后端状态检查" "./deploy-zentao.sh backend dev status"
    run_test "前端状态检查" "./deploy-zentao.sh frontend dev status"
    run_test "客户端状态检查" "./deploy-zentao.sh client dev status"
}

# 测试镜像源连通性
test_mirror_connectivity() {
    log_info "========== 测试镜像源连通性 =========="
    
    if command -v curl &> /dev/null; then
        # 测试npm源
        if curl -s --connect-timeout 5 https://registry.npmmirror.com/ | grep -q "cnpmjs"; then
            log_success "淘宝npm源连通正常"
            ((PASSED_TESTS++))
        else
            log_warning "淘宝npm源连接失败"
        fi
        ((TOTAL_TESTS++))
        
        # 测试pip源
        if curl -s --connect-timeout 5 https://pypi.tuna.tsinghua.edu.cn/simple/ | grep -q "Simple index"; then
            log_success "清华pip源连通正常"
            ((PASSED_TESTS++))
        else
            log_warning "清华pip源连接失败"
        fi
        ((TOTAL_TESTS++))
    else
        log_warning "curl未安装，跳过连通性测试"
    fi
}

# 测试清理结果
test_cleanup_results() {
    log_info "========== 测试清理结果 =========="
    
    # 确认旧文件已删除
    run_test "旧的通用后端脚本已删除" "[[ ! -f 'deploy-backend-universal.sh' ]]"
    run_test "旧的通用前端脚本已删除" "[[ ! -f 'deploy-frontend-universal.sh' ]]"
    run_test "旧的通用客户端脚本已删除" "[[ ! -f 'deploy-client-universal.sh' ]]"
    run_test "旧的镜像测试脚本已删除" "[[ ! -f 'test-mirror-optimization.sh' ]]"
    run_test "旧的部署策略文档已删除" "[[ ! -f 'zentao-backend-deployment-strategy.md' ]]"
    
    # 确认有用文件仍存在
    run_test "IDE集成配置脚本保留" "[[ -f 'configure-ide-integration.sh' ]]"
    run_test "IDE集成配置文档保留" "[[ -f 'ide-integration-configs.md' ]]"
    run_test "镜像源配置文件保留" "[[ -f 'config/mirrors.env' ]]"
}

# 显示使用建议
show_usage_suggestions() {
    log_info "========== 使用建议 =========="
    
    echo "1. 配置镜像源 (推荐首次使用):"
    echo "   ./scripts/setup-mirrors.sh"
    echo ""
    
    echo "2. 检查项目状态:"
    echo "   ./deploy-zentao.sh all dev status"
    echo ""
    
    echo "3. 部署开发环境:"
    echo "   ./deploy-zentao.sh backend dev deploy"
    echo "   ./deploy-zentao.sh frontend dev deploy"
    echo "   ./deploy-zentao.sh client dev deploy"
    echo ""
    
    echo "4. 部署所有项目:"
    echo "   ./deploy-zentao.sh all dev deploy"
    echo ""
    
    echo "5. 查看详细输出:"
    echo "   ./deploy-zentao.sh all dev status --verbose"
    echo ""
}

# 显示测试结果
show_test_results() {
    echo ""
    log_info "========== 部署功能测试结果汇总 =========="
    echo "总测试数: $TOTAL_TESTS"
    echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
    echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"
    
    if [[ $FAILED_TESTS -eq 0 ]]; then
        echo -e "${GREEN}✅ 所有部署功能测试通过！${NC}"
        return 0
    else
        echo -e "${RED}❌ 有 $FAILED_TESTS 个测试失败，请检查相关配置。${NC}"
        return 1
    fi
}

# 主函数
main() {
    log_info "开始部署功能测试..."
    echo ""
    
    # 运行所有测试
    test_deployment_scripts
    echo ""
    
    test_mirror_configuration
    echo ""
    
    test_dockerfile_optimization
    echo ""
    
    test_deployment_functionality
    echo ""
    
    test_mirror_connectivity
    echo ""
    
    test_cleanup_results
    echo ""
    
    show_usage_suggestions
    
    # 显示结果
    show_test_results
}

# 执行主函数
main "$@"
