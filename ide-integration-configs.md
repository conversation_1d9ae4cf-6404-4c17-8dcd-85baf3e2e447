# IDE集成配置指南

## 🎯 Cursor IDE 集成配置

### 1. MCP配置文件位置
```
# macOS
~/Library/Application Support/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings/cline_mcp_settings.json

# Windows
%APPDATA%\Cursor\User\globalStorage\rooveterinaryinc.roo-cline\settings\cline_mcp_settings.json

# Linux
~/.config/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings/cline_mcp_settings.json
```

### 2. 配置文件内容
```json
{
  "mcpServers": {
    "zentao-mcp": {
      "command": "zentao-mcp-client",
      "args": ["start", "--mode", "stdio"],
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "http://localhost:8000",
        "ZENTAO_MCP_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

### 3. 开发环境配置
```json
{
  "mcpServers": {
    "zentao-mcp-dev": {
      "command": "uv",
      "args": ["run", "python", "-m", "zentao_mcp_client", "start", "--mode", "stdio"],
      "cwd": "/path/to/zentao-mcp-client",
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "http://localhost:8000",
        "ZENTAO_MCP_API_KEY": "dev-api-key-for-testing"
      }
    }
  }
}
```

## 🎯 Claude Desktop 集成配置

### 1. 配置文件位置
```
# macOS
~/Library/Application Support/Claude/claude_desktop_config.json

# Windows
%APPDATA%\Claude\claude_desktop_config.json

# Linux
~/.config/Claude/claude_desktop_config.json
```

### 2. 配置文件内容
```json
{
  "mcpServers": {
    "zentao-mcp": {
      "command": "zentao-mcp-client",
      "args": ["start"],
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "http://localhost:8000",
        "ZENTAO_MCP_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

## 🎯 VS Code 集成配置

### 1. 安装MCP扩展
```bash
# 安装MCP扩展（如果有的话）
code --install-extension mcp-extension
```

### 2. settings.json配置
```json
{
  "mcp.servers": {
    "zentao-mcp": {
      "command": "zentao-mcp-client",
      "args": ["start", "--mode", "stdio"],
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "http://localhost:8000",
        "ZENTAO_MCP_API_KEY": "your-api-key-here"
      }
    }
  }
}
```

## 🎯 通用HTTP模式集成

### 1. HTTP服务启动
```bash
# 启动HTTP模式服务
./deploy-client-universal.sh dev start --mode=http --host=0.0.0.0 --port=8080
```

### 2. HTTP API调用示例
```bash
# 获取部门列表
curl -X POST http://localhost:8080/mcp/tools/zentao_get_departments \
  -H "Content-Type: application/json" \
  -d '{}'

# 获取项目列表
curl -X POST http://localhost:8080/mcp/tools/zentao_get_projects \
  -H "Content-Type: application/json" \
  -d '{}'
```

## 🔧 自动化配置脚本

### 1. Cursor配置脚本
```bash
#!/bin/bash
# configure-cursor-mcp.sh

CURSOR_CONFIG_DIR=""
case "$(uname)" in
    Darwin)
        CURSOR_CONFIG_DIR="$HOME/Library/Application Support/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
        ;;
    Linux)
        CURSOR_CONFIG_DIR="$HOME/.config/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
        ;;
    CYGWIN*|MINGW*|MSYS*)
        CURSOR_CONFIG_DIR="$APPDATA/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
        ;;
esac

mkdir -p "$CURSOR_CONFIG_DIR"

cat > "$CURSOR_CONFIG_DIR/cline_mcp_settings.json" << 'EOF'
{
  "mcpServers": {
    "zentao-mcp": {
      "command": "zentao-mcp-client",
      "args": ["start", "--mode", "stdio"],
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "http://localhost:8000",
        "ZENTAO_MCP_API_KEY": "your-api-key-here"
      }
    }
  }
}
EOF

echo "✅ Cursor MCP配置已创建: $CURSOR_CONFIG_DIR/cline_mcp_settings.json"
echo "请修改API Key后重启Cursor"
```

### 2. Claude Desktop配置脚本
```bash
#!/bin/bash
# configure-claude-mcp.sh

CLAUDE_CONFIG_DIR=""
case "$(uname)" in
    Darwin)
        CLAUDE_CONFIG_DIR="$HOME/Library/Application Support/Claude"
        ;;
    Linux)
        CLAUDE_CONFIG_DIR="$HOME/.config/Claude"
        ;;
    CYGWIN*|MINGW*|MSYS*)
        CLAUDE_CONFIG_DIR="$APPDATA/Claude"
        ;;
esac

mkdir -p "$CLAUDE_CONFIG_DIR"

cat > "$CLAUDE_CONFIG_DIR/claude_desktop_config.json" << 'EOF'
{
  "mcpServers": {
    "zentao-mcp": {
      "command": "zentao-mcp-client",
      "args": ["start"],
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "http://localhost:8000",
        "ZENTAO_MCP_API_KEY": "your-api-key-here"
      }
    }
  }
}
EOF

echo "✅ Claude Desktop MCP配置已创建: $CLAUDE_CONFIG_DIR/claude_desktop_config.json"
echo "请修改API Key后重启Claude Desktop"
```

## 🚀 一键配置脚本

### configure-ide-integration.sh
```bash
#!/bin/bash
set -euo pipefail

# 颜色输出
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 获取API Key
get_api_key() {
    local api_key=""
    
    # 尝试从环境变量获取
    if [[ -n "${ZENTAO_MCP_API_KEY:-}" ]]; then
        api_key="$ZENTAO_MCP_API_KEY"
    else
        # 尝试从配置文件获取
        if [[ -f "zentao-mcp-client/config.ini" ]]; then
            api_key=$(grep "^api_key" zentao-mcp-client/config.ini | cut -d'=' -f2 | tr -d ' ')
        fi
    fi
    
    if [[ -z "$api_key" ]]; then
        log_warning "未找到API Key，请手动配置"
        api_key="your-api-key-here"
    fi
    
    echo "$api_key"
}

# 配置Cursor
configure_cursor() {
    log_info "配置Cursor IDE集成..."
    
    local cursor_config_dir=""
    case "$(uname)" in
        Darwin)
            cursor_config_dir="$HOME/Library/Application Support/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
            ;;
        Linux)
            cursor_config_dir="$HOME/.config/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            cursor_config_dir="$APPDATA/Cursor/User/globalStorage/rooveterinaryinc.roo-cline/settings"
            ;;
    esac
    
    if [[ -n "$cursor_config_dir" ]]; then
        mkdir -p "$cursor_config_dir"
        
        local api_key=$(get_api_key)
        
        cat > "$cursor_config_dir/cline_mcp_settings.json" << EOF
{
  "mcpServers": {
    "zentao-mcp": {
      "command": "zentao-mcp-client",
      "args": ["start", "--mode", "stdio"],
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "http://localhost:8000",
        "ZENTAO_MCP_API_KEY": "$api_key"
      }
    }
  }
}
EOF
        
        log_success "Cursor配置已创建: $cursor_config_dir/cline_mcp_settings.json"
    else
        log_warning "无法确定Cursor配置目录"
    fi
}

# 配置Claude Desktop
configure_claude() {
    log_info "配置Claude Desktop集成..."
    
    local claude_config_dir=""
    case "$(uname)" in
        Darwin)
            claude_config_dir="$HOME/Library/Application Support/Claude"
            ;;
        Linux)
            claude_config_dir="$HOME/.config/Claude"
            ;;
        CYGWIN*|MINGW*|MSYS*)
            claude_config_dir="$APPDATA/Claude"
            ;;
    esac
    
    if [[ -n "$claude_config_dir" ]]; then
        mkdir -p "$claude_config_dir"
        
        local api_key=$(get_api_key)
        
        cat > "$claude_config_dir/claude_desktop_config.json" << EOF
{
  "mcpServers": {
    "zentao-mcp": {
      "command": "zentao-mcp-client",
      "args": ["start"],
      "env": {
        "ZENTAO_MCP_BACKEND_URL": "http://localhost:8000",
        "ZENTAO_MCP_API_KEY": "$api_key"
      }
    }
  }
}
EOF
        
        log_success "Claude Desktop配置已创建: $claude_config_dir/claude_desktop_config.json"
    else
        log_warning "无法确定Claude Desktop配置目录"
    fi
}

# 主函数
main() {
    echo "🚀 IDE集成配置工具"
    echo "===================="
    
    # 检查zentao-mcp-client是否已安装
    if ! command -v zentao-mcp-client &> /dev/null; then
        log_warning "zentao-mcp-client未安装，请先运行部署脚本"
        log_info "运行: ./deploy-client-universal.sh dev deploy"
        exit 1
    fi
    
    # 配置各个IDE
    configure_cursor
    configure_claude
    
    echo ""
    log_success "IDE集成配置完成！"
    echo ""
    echo "📋 下一步操作："
    echo "1. 确保后端服务运行: ./deploy-backend-universal.sh dev deploy"
    echo "2. 配置客户端: zentao-mcp-client configure"
    echo "3. 重启IDE以加载新配置"
    echo ""
    echo "🔧 测试连接："
    echo "zentao-mcp-client start --mode stdio"
}

main "$@"
```

## 📋 使用步骤总结

### 1. 部署后端服务
```bash
./deploy-backend-universal.sh dev deploy
```

### 2. 部署客户端
```bash
./deploy-client-universal.sh dev deploy
```

### 3. 配置客户端
```bash
zentao-mcp-client configure
```

### 4. 配置IDE集成
```bash
chmod +x configure-ide-integration.sh
./configure-ide-integration.sh
```

### 5. 重启IDE
重启Cursor或Claude Desktop以加载新的MCP配置

### 6. 测试集成
在IDE中尝试使用禅道相关功能，如查询项目、Bug等
