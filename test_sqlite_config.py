#!/usr/bin/env python3
"""
测试SQLite配置修改的验证脚本
验证所有环境配置文件是否正确使用SQLite
"""

import os
import sys
import configparser
from pathlib import Path

def test_env_files():
    """测试环境配置文件"""
    print("🔍 检查环境配置文件...")
    
    env_dir = Path("zentao-mcp-backend-service/config/environments")
    env_files = ["dev.env", "test.env", "prod.env"]
    
    results = []
    
    for env_file in env_files:
        file_path = env_dir / env_file
        if not file_path.exists():
            results.append(f"❌ {env_file}: 文件不存在")
            continue
            
        with open(file_path, 'r') as f:
            content = f.read()
            
        if "sqlite://" in content:
            results.append(f"✅ {env_file}: 使用SQLite配置")
        elif "postgresql://" in content:
            results.append(f"❌ {env_file}: 仍使用PostgreSQL配置")
        else:
            results.append(f"⚠️  {env_file}: 未找到数据库配置")
    
    return results

def test_compose_files():
    """测试Docker Compose配置文件"""
    print("🔍 检查Docker Compose配置文件...")
    
    compose_dir = Path("zentao-mcp-backend-service/config/compose")
    compose_files = ["docker-compose.dev.yml", "docker-compose.test.yml", "docker-compose.prod.yml"]
    
    results = []
    
    for compose_file in compose_files:
        file_path = compose_dir / compose_file
        if not file_path.exists():
            results.append(f"❌ {compose_file}: 文件不存在")
            continue
            
        with open(file_path, 'r') as f:
            content = f.read()
            
        if "postgres:" in content and "image: postgres" in content:
            results.append(f"❌ {compose_file}: 仍包含PostgreSQL服务")
        else:
            results.append(f"✅ {compose_file}: 已移除PostgreSQL服务")
    
    return results

def test_pyproject_toml():
    """测试pyproject.toml依赖"""
    print("🔍 检查pyproject.toml依赖...")
    
    file_path = Path("zentao-mcp-backend-service/pyproject.toml")
    if not file_path.exists():
        return ["❌ pyproject.toml: 文件不存在"]
    
    with open(file_path, 'r') as f:
        content = f.read()
    
    if "psycopg2-binary" in content:
        return ["❌ pyproject.toml: 仍包含PostgreSQL依赖 psycopg2-binary"]
    else:
        return ["✅ pyproject.toml: 已移除PostgreSQL依赖"]

def test_docker_files():
    """测试Dockerfile文件"""
    print("🔍 检查Dockerfile文件...")
    
    docker_dir = Path("zentao-mcp-backend-service/config/docker")
    docker_files = ["Dockerfile.dev", "Dockerfile.test", "Dockerfile.prod"]
    
    results = []
    
    for docker_file in docker_files:
        file_path = docker_dir / docker_file
        if file_path.exists():
            results.append(f"✅ {docker_file}: 文件存在")
        else:
            results.append(f"⚠️  {docker_file}: 文件不存在")
    
    return results

def main():
    """主测试函数"""
    print("=" * 60)
    print("🧪 SQLite配置修改验证测试")
    print("=" * 60)
    
    all_results = []
    
    # 测试环境配置文件
    all_results.extend(test_env_files())
    print()
    
    # 测试Compose配置文件
    all_results.extend(test_compose_files())
    print()
    
    # 测试pyproject.toml
    all_results.extend(test_pyproject_toml())
    print()
    
    # 测试Dockerfile文件
    all_results.extend(test_docker_files())
    print()
    
    # 输出结果
    print("=" * 60)
    print("📋 测试结果汇总:")
    print("=" * 60)
    
    success_count = 0
    warning_count = 0
    error_count = 0
    
    for result in all_results:
        print(result)
        if result.startswith("✅"):
            success_count += 1
        elif result.startswith("⚠️"):
            warning_count += 1
        elif result.startswith("❌"):
            error_count += 1
    
    print()
    print(f"📊 统计: ✅ {success_count} 成功, ⚠️ {warning_count} 警告, ❌ {error_count} 错误")
    
    if error_count == 0:
        print("🎉 所有配置修改验证通过！")
        return 0
    else:
        print("💥 发现配置问题，请检查上述错误项")
        return 1

if __name__ == "__main__":
    sys.exit(main())
