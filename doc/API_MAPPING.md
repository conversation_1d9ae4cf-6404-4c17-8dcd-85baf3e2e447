# 禅道 API 与 MCP 工具映射表

## 直接接口映射 (1:1 对应)

### 部门管理

| 禅道 API              | MCP 工具名称                 | 功能描述             |
| --------------------- | ---------------------------- | -------------------- |
| `/apiData/getAllDept` | `zentao_get_all_departments` | 获取所有部门列表     |
| `/api/getUserByDept`  | `zentao_get_users_by_dept`   | 根据部门查询用户列表 |

### 项目管理

| 禅道 API                   | MCP 工具名称                 | 功能描述                  |
| -------------------------- | ---------------------------- | ------------------------- |
| `/apiData/getAllProject`   | `zentao_get_all_projects`    | 获取所有项目列表          |
| `/api/getTaskByProject`    | `zentao_get_project_tasks`   | 根据项目 ID 查询任务列表  |
| `/api/getStoryByProjectid` | `zentao_get_project_stories` | 根据项目 ID 查询需求列表  |
| `/api/getBugByProject`     | `zentao_get_project_bugs`    | 根据项目 ID 查询 Bug 列表 |

### 需求管理

| 禅道 API               | MCP 工具名称                        | 功能描述                   |
| ---------------------- | ----------------------------------- | -------------------------- |
| `/api/getStory`        | `zentao_get_story_effort`           | 批量获取需求工时信息       |
| `/api/getStoryEnd`     | `zentao_get_story_completed_effort` | 批量获取已完成需求任务工时 |
| `/api/getStoryId`      | `zentao_check_story_exists`         | 批量检查需求是否存在       |
| `/api/getStoryDetail`  | `zentao_get_story_detail`           | 根据需求 ID 获取详情       |
| `/api/getStorysByTime` | `zentao_get_stories_by_time`        | 根据时间段查询需求         |

### 任务管理

| 禅道 API                | MCP 工具名称                  | 功能描述             |
| ----------------------- | ----------------------------- | -------------------- |
| `/api/getTaskById`      | `zentao_get_task_detail`      | 根据任务 ID 查询详情 |
| `/api/getTaskByAccount` | `zentao_get_tasks_by_account` | 根据域账号查询任务   |
| `/api/getTaskByDept`    | `zentao_get_tasks_by_dept`    | 根据部门查询任务     |

### Bug 管理

| 禅道 API                                  | MCP 工具名称                       | 功能描述                 |
| ----------------------------------------- | ---------------------------------- | ------------------------ |
| `/apiData/getBugListByTimeRange`          | `zentao_get_bugs_by_time_range`    | 根据时间段查询 Bug 列表  |
| `/apiData/getBugListByTimeRangeAndDeptId` | `zentao_get_bugs_by_time_and_dept` | 根据时间段和部门查询 Bug |
| `/api/getBugDetail`                       | `zentao_get_bug_detail`            | 根据 Bug ID 获取详情     |
| `/api/getPersonalBugs`                    | `zentao_get_personal_bugs`         | 查询个人 Bug             |

### 用户管理

| 禅道 API                | MCP 工具名称                  | 功能描述                   |
| ----------------------- | ----------------------------- | -------------------------- |
| `/api/getUserByAccount` | `zentao_get_users_by_account` | 根据域账号批量查询用户信息 |

## 数据加工接口 (基于直接接口的二次封装)

### 统计分析类

| MCP 工具名称                    | 基于的直接接口                                                                      | 功能描述                    |
| ------------------------------- | ----------------------------------------------------------------------------------- | --------------------------- |
| `analyze_story_workload`        | `zentao_get_story_effort`, `zentao_get_story_completed_effort`                      | 分析需求工时统计            |
| `analyze_bugs_by_dept_and_time` | `zentao_get_bugs_by_time_and_dept`                                                  | 按部门和时间段统计 Bug 分析 |
| `project_summary_analysis`      | `zentao_get_project_tasks`, `zentao_get_project_stories`, `zentao_get_project_bugs` | 项目整体统计分析            |
| `personnel_workload_analysis`   | `zentao_get_tasks_by_account`, `zentao_get_tasks_by_dept`                           | 人员工作量统计和分析        |

### 关联查询类

| MCP 工具名称                | 基于的直接接口                                           | 功能描述          |
| --------------------------- | -------------------------------------------------------- | ----------------- |
| `story_task_relation_query` | `zentao_get_project_stories`, `zentao_get_project_tasks` | 需求-任务关联查询 |
| `bug_to_story_tracking`     | `zentao_get_bug_detail`, `zentao_get_story_detail`       | Bug 转需求追踪    |

### 数据过滤类

| MCP 工具名称               | 基于的直接接口                  | 功能描述             |
| -------------------------- | ------------------------------- | -------------------- |
| `filter_bugs_by_criteria`  | `zentao_get_bugs_by_time_range` | 根据多种条件过滤 Bug |
| `batch_query_stories`      | `zentao_get_story_detail`       | 批量查询需求信息     |
| `validate_story_existence` | `zentao_check_story_exists`     | 验证需求是否存在     |

## 实现优先级

### 第一优先级：直接接口层 (19 个接口)

1. 部门管理：2 个接口
2. 项目管理：4 个接口
3. 需求管理：5 个接口
4. 任务管理：3 个接口
5. Bug 管理：4 个接口
6. 用户管理：1 个接口

### 第二优先级：数据加工层 (9 个接口)

1. 统计分析类：4 个接口
2. 关联查询类：2 个接口
3. 数据过滤类：3 个接口

### 第三优先级：业务封装层

根据用户需求和使用反馈逐步完善

## 接口命名规范

### 直接接口命名

- 前缀：`zentao_`
- 格式：`zentao_<action>_<resource>_<condition>`
- 示例：`zentao_get_bugs_by_time_range`

### 数据加工接口命名

- 无前缀，直接使用业务含义
- 格式：`<action>_<resource>_<purpose>`
- 示例：`analyze_story_workload`

## 数据结构处理

### 直接接口

- 保持禅道 API 原始数据结构
- 仅进行必要的数据类型转换
- 保留所有原始字段

### 数据加工接口

- 提供标准化的数据结构
- 中英文字段映射
- 枚举值转换
- 数据聚合和计算
