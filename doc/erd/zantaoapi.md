#  禅道数据接口

domain

beta:http://newzentao-api.beta1.fn/
preview:http://newzentao-api.idc1.fn/ // 需要配置Pre环境代理来访问
online:http://newzentao-api.idc1.fn/

# 文档一


## 【验】1：获取所有部门

url ：http://{{domain}}/apiData/getAllDept

入参 ：空

出参：{

"traceId": "d115c244-f812-46ea-9c44-d43e0e7c7d89",

"rsCode": "********",

"msg": "正常返回",

"body": [

{

"id": 2, //id

"name": "技术部", //部门名称

"parent": "0", //父级部门id

"path": ",2,", //部门路径

"grade": "1" //层级

},

{

"id": 4,

"name": "规划部",

"parent": "0",

"path": ",4,",

"grade": "1"

},

{

"id": 11,

"name": "测试组",

"parent": "2",

"path": ",2,11,",

"grade": "2"

}]}

## 【验】2：获取所有项目

url :http://{{domain}}/apiData/getAllProject

入参 ：空

出参

{

"traceId": "08c40efb-7d4b-4646-9770-84b08adb76e1",

"rsCode": "********",

"msg": "正常返回",

"body": [

{

"id": 1, //id

"name": "企业网站第一期", //名称

"code": "coWeb1", //编号

"begin": "2012-06-05", //开始时间

"end": "2012-12-04", //结束时间

"status": "done", //状态 doing（进行中）,wait(待执行),done(已完成),suspended (暂停状态)

"parent": "0", //父级id

"openedBy": "", // 开始时间

"closedDate": "0", //结束时间

"closedBy": "" //关闭人

},

{

"id": 2,

"name": "禅道管理软件上线时间表",

"code": "zentao ",

"begin": "2014-07-07",

"end": "2014-07-16",

"status": "doing",

"parent": "0",

"openedBy": "",

"closedDate": "0",

"closedBy": ""

}]

}

## 【验】3：用时间段查询bug 列表

url : http://{{domain}}/apiData/getBugListByTimeRange

入参 ：{

"startDate" : "2025-04-02 14:04:07",

"endDate" :"2025-07-09 10:54:54"

}

出参

{

"traceId": "359a87bb-2240-44bf-98dc-8789e5297bbe",

"rsCode": "********",

"msg": "正常返回",

"body": [

{

"id": 183831, //bug id

"assignedTo": "closed", //指派给了谁

"project": 2377, //项目id

"module": 21584, //模块id

"product": 192, //产品id

"assignedDate": "2025-04-11T06:58:42.000+00:00", //指派时间

"status": "closed", //bug状态

"closedDate": "2025-04-11T06:58:42.000+00:00", //关闭时间

"title": "1111", //bug标题

"environment": "Beta", // bug环境

"openedDate": "2024-01-04T03:01:36.000+00:00", //bug创建时间

"severity": 1,

"resolvedBy": "tony.lu", //解决人

"openedBy": "tony.lu", //bug创建人

"isxi": 1,

"finishdate": null,

"senddingding": 0,

"toStory": 0, //是否转需求 >0表示转需求

"activatedCount": 0,

"confirmed": 1,

"resolvedDate": "2025-04-11T06:58:10.000+00:00",

"openedBuild": "22328",

"resolvedBuild": "22426",

"resolution": "bydesign", //解决方案

"closedBy": "tony.lu", //关闭人

"lastEditedBy": "tony.lu", // 最后修改人

"steps": "`<p>`[步骤]`</p>`\r\n`<p>`[结果]`</p>`\r\n`<p>`[期望]`</p>`"

},

{

"id": 184073,

"assignedTo": "closed",

"project": 143,

"module": 11675,

"product": 63,

"assignedDate": "2025-05-16T07:14:54.000+00:00",

"status": "closed",

"closedDate": "2025-05-16T07:14:54.000+00:00",

"title": "特色图2222",

"environment": "Online",

"openedDate": "2024-07-05T07:09:03.000+00:00",

"severity": 1,

"resolvedBy": "fuwu",

"openedBy": "luoheying",

"isxi": 0,

"finishdate": null,

"senddingding": 0,

"toStory": 28713,

"activatedCount": 0,

"confirmed": 1,

"resolvedDate": "2025-05-16T07:03:43.000+00:00",

"openedBuild": "12880",

"resolvedBuild": "",

"resolution": "tostory",

"closedBy": "fuwu",

"lastEditedBy": "fuwu",

"steps": "`<p>`[前置条件]`</p>`特色图`<p>`[步骤]`</p>`1. sett`<br />`\r\n`<p>`[结果]`</p>`\r\n`<p>`[期望]`</p>`1. teset`<br />`"

}]

}

## 【验】4：根据时间段和部门查询Bug

url : http://{{domain}}/apiData/getBugListByTimeRangeAndDeptId

入参：

{

"startDate":"2025-04-02 14:04:07",

"endDate":"2025-07-09 10:54:54",

"deptId":41

}

出参同3接口

# 文档二

## 1.产品需求工时

接口地址:api/getStory

入参：需求；出参：需求号、创建人、需求工时

入参:

{"story":["28673","12345"]}

出参:

{

    "traceId": "30f96d9b-8ef8-42f4-bd6e-3d3c96ec8a27",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "total": 2,//总数

        "stories": [

            {

                "empid": "20190229788",//empid

                "openedBy": "jiaju.zha",//创建者域账号

                "estimate": 0.0,//需求工时

                "story": 12345 //需求号

            },

            {

                "empid": "***********",

                "openedBy": "tiny.cui",

                "estimate": 124.0,

                "story": 28673

            }

        ]

    }

}

## 2.需求任务工时（已完成）

接口地址:api/getStoryEnd

入参：需求号；出参：需求号、任务id、完成人、总消耗

入参:

{"story":["28673","12345"]}

出参:

{

    "traceId": "5918c884-673f-4e6b-9172-815efa174950",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "task": [

            {

                "tasklist": [],

                "story": 12345

            },

            {

                "tasklist": [

                    {

                        "consumed": 10.0,//消耗

                        "empid": "***********",//empid

                        "finishedBy": "tiny.cui",//完成者域账号

                        "taskid": 372030,//任务id

                        "story": 28673 //需求id

                    },

                    {

                        "consumed": 10.0,

                        "empid": "***********",

                        "finishedBy": "tiny.cui",

                        "taskid": 372031,

                        "story": 28673

                    },

                    {

                        "consumed": 10.0,

                        "empid": "***********",

                        "finishedBy": "tiny.cui",

                        "taskid": 372047,

                        "story": 28673

                    }

                ],

                "story": 28673

            }

        ]

    }

}

## 3.是否存在需求号（批量）

接口地址：/api/getStoryId

入参：{"story":["28673","12345"}

出参：{

    "traceId": "cc943bfa-e933-4fea-9206-22cfa7944a74",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "data": [

            12345,

            28673

        ]

    }

}

2025-07-23

## 5.根据BUGID查询指定BUG明细详情

接口地址:api/getBugDetail

入参:

{"detailId":184213}

出参:

{

    "traceId": "08355012-82d4-4203-8e01-8f49e2fc1e8b",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "date": {

                "id": 184213,

                "assignedTo": "closed",//当前指派者,关闭会显示closed

                "assignedToEmpid": "closed",//当前指派者,关闭会显示closed

                "project": 2345,//所属项目

                "projectname": "会员店1.0.0",//所属项目

                "module": 0,//所属模块

                "modulename": "未知模块",//所属模块

                "product": 249,//所属产品

                "productname": "大润发会员店",//所属产品

                "assignedDate": **null**,//指派日期

                "status": "closed",*//Bug状态 active = '未关闭';resolved = '已解决';closed = '已关闭';released = '已发布';*

                "closedDate": **null**,//关闭日期

                "title": **null**,//bug标题

                "environment": "Online",//环境

                "openedDate": **null**,//创建时间

                "severity": 1,*//严重程度 0:冒烟 1:严重 2:一般 3:次要 4:低级*

                "resolvedBy": "code72",//解决人

                "resolvedByEmpid": "20190227831",//解决人

                "openedBy": "weidong.wang",//创建人

                "openedByEmpid": "20190230303",//创建人

                "isxi": 1,*//是否需要洗数据 1.无需洗数据 2.数据已洗完 3.数据未洗完*

                "finishdate": **null**,//洗数据完成时间

                "senddingding": **null**,

                "toStory": 28712,//转需求id

                "activatedCount": 0,//激活次数

                "confirmed": 1,*//是否确认1 = '已确认';0 = '未确认'*

                "openedBuild": "21817",

                "resolvedBuild": "22033",

                "resolution": "fixed",*//解决方案
 bydesign = '设计如此';
 duplicate = '重复Bug';
 external = '外部原因';
 fixed = '已解决';
 notrepro = '无法重现';
 postponed = '延期处理';
 tostory = '转为需求';*

                "closedBy": "weidong.wang",//关闭人

                "closedByEmpid": "20190230303",//关闭人

                "lastEditedBy": "weidong.wang",//最后编辑

                "lastEditedByEmpid": "20190230303",//最后编辑

"steps": "`<p>`[步骤]<br />\r\n订单号：2210177079473000832`</p>`\r\n`<p>`[结果]`</p>`\r\n`<p>`\r\n<img src=\"data/upload/1/202210/172009240740532b.png\" alt=\"\" />\r\n`</p>`\r\n`<p>`[期望]`</p>`\r\n`<p>`有退订单原因可以退订`</p>`" //**重现步骤**

}

    }

}

## 6.根据项目ID查询项目任务列表

接口地址:api/getTaskByProject

入参:

{"detailId":143}

出参:

{

    "traceId": "8c338304-ed38-410a-83cc-87fe51e1d698",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "data": [

           {

                "assignedDate": "2014-09-18 17:30:54",//指派时间

                "assignedToEmpid": **null**,//指派人

                "closedBy": "admin",//由谁关闭

                "consumed": 1,//总消耗

                "finishedBy": "peter.wang",//由谁完成

                "canceledBy": "",//由谁取消

                "project": 143,//所属项目

                "type": "devel",//任务类型：

affair:处理BUG,

design:设计--ERD,

devel:开发--code、code review、Unit test和开发内部集成测试,

test:测试--系统测试、集成测试、性能测试,

study:研究,

discuss:讨论--开会讨论,

misc:其他,

bug:编写测试用例,

ui:UI设计

                "assignedTo": "closed",//当前指派者,关闭会显示closed

                "closedByEmpid": **null**,//由谁关闭

                "finishedByEmpid": **null**,//由谁完成

                "openedBy": "kent.lin",//由谁创建

                "pages": 0,//UI张数

                "statusCustom": 24,

                "storyname": **null**,//需求名称

                "canceledByEmpid": **null**,

                "estimate": 4,//最初预计工时

                "modulename": "未知模块",//模块名称

                "id": 1038,//任务id

                "deadline": **null**,//截止日期

                "finishedDate": "2014-09-17 19:29:03",//完成时间

                "realStarted": **null**,//实际开始

                "projectname": "【飞牛网禅道】二次开发",//所属项目

                "closedReason": "done",

                "pri": 3,//优先级

                "module": 0,

                "lastEditedBy": "admin",//最后编辑

                "openedDate": "2014-09-17 18:45:37",

                "deleted": "0",//是否删除 0否 1是

                "canceledDate": **null**,

                "closedDate": "2014-09-18 17:30:54",

                "left": 0,//预计剩余

                "estStarted": **null**,//预计开始

                "fromBug": 0,

                "name": "增加任务状态",

                "openedByEmpid": **null**,//由谁创建

                "storyVersion": 1,

                "lastEditedDate": "2014-09-18 17:30:54",

                "mailto": "",

                "lastEditedByEmpid": **null**,

                "desc": "",

                "status": "closed",//任务状态

//wait = '未开始';
//doing = '进行中';
//done = '已完成';
//cancel = '已取消';
//closed = '已关闭';
//beta = '已上BETA';
//test\_completed' = '测试完毕';

                "story": 747//需求ID

            }

}

## 7.根据任务ID查询任务详情

接口地址:api/getTaskById

入参:

{"detailId":371999}

出参:字段同上一个接口

{

    "traceId": "70d93aed-892b-4aff-b82a-fd88c6cf0b74",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "data": {

            "id": 371999,

            "project": 143,

            "projectname": "【飞牛网禅道】二次开发",

            "module": 11675,

            "modulename": "业务需求",

            "story": 8681,

            "storyname": "禅道业务需求负责人可以指派全体员工",

            "storyVersion": 1,

            "fromBug": 0,

            "name": "6",

            "type": "test",

            "pri": 3,

            "estimate": 1,

            "consumed": 0,

            "left": 1,

            "deadline": "2024-10-14T16:00:00.000+00:00",

            "status": "wait",

            "statusCustom": 1,

            "mailto": "",

            "desc": "666",

            "openedBy": "fuwu",

            "openedByEmpid": "20190239122",

            "openedDate": "2024-10-15T08:53:03.000+00:00",

            "assignedTo": "fuwu",

            "assignedToEmpid": "20190239122",

            "assignedDate": "2024-10-15T08:53:03.000+00:00",

            "estStarted": "2024-10-14T16:00:00.000+00:00",

            "realStarted": **null**,

            "finishedBy": "",

            "finishedByEmpid": **null**,

            "finishedDate": **null**,

            "canceledBy": "",

            "canceledByEmpid": **null**,

            "canceledDate": **null**,

            "closedBy": "",

            "closedByEmpid": **null**,

            "closedDate": **null**,

            "closedReason": "",

            "lastEditedBy": "",

            "lastEditedByEmpid": **null**,

            "lastEditedDate": **null**,

            "deleted": "0",

            "pages": 0

        }

    }

}

## 8.根据项目ID查询项目需求列表

接口地址:/api/getStoryByProjectid

入参:

{"detailId":377}

出参:

{

    "traceId": "f703bfe3-e1fa-4d28-81f3-b0a29d4ed35c",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "data": [

            {

                "assignedDate": "2014-12-30 08:55:57",//指派日期

                "assignedToEmpid": **null**,//指派人

                "closedBy": "pd05",//关闭人

                "keywords": "",//关键词

                "storyTypeComment": 0,

                "source": "",//来源

//customer = '客户';
//user = '用户';
//po = '产品经理';
//market = '市场部';

//goods = '商品部';
//service = '客服部';
//competitor = '竞争对手';
//partner = '合作伙伴';
//dev = '开发人员';
//tester = '测试人员';
//bug = 'Bug';
//other = '其他';

                "title": "RT大电只送上海",//需求名称

                "type": "",

                "assignedTo": "closed",//指派人

                "closedByEmpid": **null**,//关闭人

                "openedBy": "pd05",//由谁创建

                "wishFinishedDate": **null**,//期望完成时间

                "reviewWay": "",//评审方式needNotReview= 不需要评审

                "estimate": 0,//需求工时

                "modulename": "商品管理",//所属模块

                "productname": "建议关闭\_飞牛网网站",//所属产品

                "id": 1125,

                "isdemand": 0,//是否有需求单

                "sn": "",//需求单审批编号

                "reviewedDate": **null**,

                "plan": 54,//计划

"planname": 54,//计划

                "estimateDate": **null**,//预计完成时间

                "product": 34,//所属产品

                "closedReason": "done",//关闭原因

                "pri": 0,//优先级

                "changeType": 0,//需求变更类型0=>"新增需求", 1=>"需求变更", 2=>"需求优化"*)*

                "module": 269,//所属模块

                "workitemname": "未知工作项",//所属工作项

                "requirement": 0,//所属业务单位

                "reviewedBy": "",

                "version": 1,

                "lastEditedBy": "pd05",

                "childStories": "",

                "toBug": 0,

                "linkStories": "",

                "openedDate": "2014-11-14 13:58:18",

                "requirementname": "未知业务单位",//所属业务单位sss

                "deleted": "0",

                "stage": "released",//所处阶段
wait= '未开始';
planned= '已计划';
projected= '已立项';
developing= '研发中';
developed= '研发完毕';
testing= '测试中';
tested= '测试完毕';
verified= '已验收';
released= '已发布';

                "closedDate": "2014-12-30 08:55:57",//关闭日期

                "fromBug": 0,

                "openedByEmpid": **null**,

                "lastEditedDate": "2014-12-30 08:55:57",

                "workitem": 0,//所属工作项

                "mailto": ",code44",

                "lastEditedByEmpid": **null**,

                "duplicateStory": 0,

                "status": "closed",
draft = '草稿';
active = '激活';
changed = '已变更';

"spec": [

                    {

                        "change\_remark": **null**,

                        "verify": "以需求为准",

                        "title": "针对自营的商品，商详页的售后服务新增温馨提示",

                        "version": 1,

                        "spec": "<p class=\"tit\">\r\n[span style=\&#34;font-size:10.5pt;color:#333333;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:#333333;%5C%22)针对\r\n[span style=\&#34;color:#333333;\&#34;](span%C2%A0style=%5C%22color:#333333;%5C%22)自营商详页，售后服务模块需增加如下内容：\r\n`</p>`\r\n<p class=\"tit\">\r\n`<b>`\r\n[span style=\&#34;font-size:10.5pt;color:red;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:red;%5C%22)温馨提示：\r\n`</b>`\r\n`<b>`\r\n[span style=\&#34;font-size:10.5pt;font-family:&#34;color:red;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;font-family:"color:red;%5C%22)\r\n\r\n`</b>`\r\n`</p>`\r\n`<p>`\r\n[span style=\&#34;font-size:10.5pt;color:red;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:red;%5C%22)由于部分商品包装更换较为频繁，因此您收到的货品有可能与展示图片不完全一致，请您以收到的商品实物为准，同时我们会尽量做到及时更新，由此给您带来不便多多谅解，谢谢！\r\n[span style=\&#34;font-size:10.5pt;font-family:&#34;color:red;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;font-family:"color:red;%5C%22)\r\n\r\n`</p>`\r\n`<p>`\r\n[span style=\&#34;font-size:10.5pt;color:#333333;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:#333333;%5C%22)具体位置加在如下图：\r\n`</p>`\r\n`<p>`\r\n[span style=\&#34;font-size:10.5pt;color:#333333;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:#333333;%5C%22)\r\n<img src=\"data/upload/1/201610/11092412090582tb.png\" alt=\"\" />\r\n<br />\r\n\r\n`</p>`\r\n`<p>`\r\n[span style=\&#34;font-size:10.5pt;font-family:&#34;color:#44546A;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;font-family:"color:#44546A;%5C%22)\r\n<br />\r\n`<!--[endif]-->`\r\n\r\n`</p>`",

                        "story": 11360

                    }

//**需求描述**

            }

}

## 9.根据需求ID查询需求详情

接口地址:/api/getStoryDetail

入参:

{"detailId":11360}

出参:字段同上一个

{

    "traceId": "6269b45d-4263-4174-8529-d7f58edb9d63",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "data": {

            "id": 11360,

            "product": 110,

            "productname": "飞牛网（new）",

            "module": 13859,

            "modulename": "商详页",

            "requirement": 12,

            "requirementname": "飞牛网PC大杂汇",

            "workitem": 12958,

            "workitemname": "飞牛网PC大杂汇",

            "plan": 0,

            "planname": "未知计划",

            "source": "service",

            "fromBug": 0,

            "title": "针对自营的商品，商详页的售后服务新增温馨提示",

            "keywords": "",

            "type": "",

            "pri": 0,

            "estimate": 0,

            "estimateDate": **null**,

            "wishFinishedDate": **null**,

            "status": "active",

            "stage": "developed",

            "mailto": "",

            "openedBy": "zhaohong.yu",

            "openedByEmpid": **null**,

            "openedDate": "2016-10-11T01:24:33.000+00:00",

            "assignedTo": "",

            "assignedToEmpid": **null**,

            "assignedDate": **null**,

            "lastEditedBy": "zhaohong.yu",

            "lastEditedByEmpid": **null**,

            "lastEditedDate": "2016-10-11T02:52:17.000+00:00",

            "reviewWay": "needNotReview",

            "reviewedBy": "",

            "reviewedDate": **null**,

            "closedBy": "",

            "closedByEmpid": **null**,

            "closedDate": **null**,

            "closedReason": "",

            "toBug": 0,

            "childStories": "",

            "linkStories": "",

            "duplicateStory": 0,

            "version": 2,

            "deleted": "0",

            "changeType": 0,

            "storyTypeComment": 0,

            "isdemand": 0,

            "sn": "",

            "spec": [

                {

                    "story": 11360,

                    "version": 1,

                    "title": "针对自营的商品，商详页的售后服务新增温馨提示",

                    "spec": "<p class=\"tit\">\r\n[span style=\&#34;font-size:10.5pt;color:#333333;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:#333333;%5C%22)针对\r\n[span style=\&#34;color:#333333;\&#34;](span%C2%A0style=%5C%22color:#333333;%5C%22)自营商详页，售后服务模块需增加如下内容：\r\n`</p>`\r\n<p class=\"tit\">\r\n`<b>`\r\n[span style=\&#34;font-size:10.5pt;color:red;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:red;%5C%22)温馨提示：\r\n`</b>`\r\n`<b>`\r\n[span style=\&#34;font-size:10.5pt;font-family:&#34;color:red;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;font-family:"color:red;%5C%22)\r\n\r\n`</b>`\r\n`</p>`\r\n`<p>`\r\n[span style=\&#34;font-size:10.5pt;color:red;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:red;%5C%22)由于部分商品包装更换较为频繁，因此您收到的货品有可能与展示图片不完全一致，请您以收到的商品实物为准，同时我们会尽量做到及时更新，由此给您带来不便多多谅解，谢谢！\r\n[span style=\&#34;font-size:10.5pt;font-family:&#34;color:red;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;font-family:"color:red;%5C%22)\r\n\r\n`</p>`\r\n`<p>`\r\n[span style=\&#34;font-size:10.5pt;color:#333333;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:#333333;%5C%22)具体位置加在如下图：\r\n`</p>`\r\n`<p>`\r\n[span style=\&#34;font-size:10.5pt;color:#333333;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;color:#333333;%5C%22)\r\n<img src=\"data/upload/1/201610/11092412090582tb.png\" alt=\"\" />\r\n<br />\r\n\r\n`</p>`\r\n`<p>`\r\n[span style=\&#34;font-size:10.5pt;font-family:&#34;color:#44546A;\&#34;](span%C2%A0style=%5C%22font-size:10.5pt;font-family:"color:#44546A;%5C%22)\r\n<br />\r\n`<!--[endif]-->`\r\n\r\n`</p>`",

                    "verify": "以需求为准",

                    "change\_remark": **null**

                }

        }

    }

}

## 10.根据项目ID查询项目BUG列表

接口地址:api/getBugByProject

入参:

{"detailId":143}

出参:字段同(5.根据BUGID查询指定BUG明细详情)

{

    "traceId": "4561da97-f799-4aeb-9845-894005a5a17f",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "bugdetail": [

            {

                "id": 4958,

                "assignedTo": "closed",

                "assignedToEmpid": "closed",

                "project": 143,

                "projectname": "【飞牛网禅道】二次开发",

                "module": 0,

                "modulename": "未知模块",

                "product": 63,

                "productname": "飞牛网禅道",

                "assignedDate": **null**,

                "status": "closed",

                "closedDate": "2014-12-02T07:11:58.000+00:00",

                "title": "点击复制BUG，BUG来源为空",

                "environment": **null**,

                "openedDate": "2014-12-02T05:35:38.000+00:00",

                "severity": 3,

                "resolvedBy": "code29",

                "resolvedByEmpid": **null**,

                "openedBy": "code",

                "openedByEmpid": **null**,

                "isxi": 0,

                "finishdate": **null**,

                "senddingding": **null**,

                "toStory": 0,

                "activatedCount": 0,

                "confirmed": 1,

                "openedBuild": "trunk",

                "resolvedBuild": "trunk",

                "resolution": "fixed",

                "closedBy": "code",

                "closedByEmpid": **null**,

                "lastEditedBy": "code",

                "lastEditedByEmpid": **null**,

                "steps": "`<p>`[步骤]`</p>`\r\n`<p>`点击复制BUG，BUG来源为空`</p>`\r\n<br />"

            }

],

"total": 2

}

## 11.根据域账号查询信息

接口地址:/api/getUserByAccount

入参:

["tiny.cui","xiang.chen"]

出参：

{

    "traceId": "99a0c199-3432-4eea-9d2e-6ef2a3dc0d0a",

    "rsCode": "********",

    "msg": "正常返回",

    "body": [

        {

            "id": 1953,

            "dept": 343,//部门代码

            "account": "tiny.cui",//域账号

            "password": "f832b71632f3e43a11c8a669a91b70c6",

            "role": "qd",//职位 *dev = '研发';
qa = '测试';
pm = '项目经理';
po = '产品经理';
td = '研发主管';
pd = '产品主管';
qd = '测试主管';
top = '高层管理';
others = '其他';*

            "realname": "崔巍巍",

            "nickname": "",

            "commiter": "",

            "avatar": "",

            "birthday": **null**,

            "gender": "m",

            "status": "work",

work= '在职';
quit= '离职';

            "email": "<EMAIL>",

            "skype": "",

            "qq": "",

            "yahoo": "",

            "gtalk": "",

            "wangwang": "",

            "mobile": "",

            "phone": "",

            "address": "",

            "zipcode": "",

            "join": "2021-03-23T16:00:00.000+00:00",

            "visits": 1159,

            "ip": "***********",

            "last": **********,

            "fails": 0,

            "locked": **null**,

            "deleted": "0",

            "empId": "***********"

        },

        {

            "id": 1517,

            "dept": 144,

            "account": "xiang.chen",

            "password": "57a479973a5e78917ef0b166346d4a7f",

            "role": "dev",

            "realname": "陈翔",

            "nickname": "",

            "commiter": "",

            "avatar": "",

            "birthday": **null**,

            "gender": "m",

            "status": "work",

            "email": "<EMAIL>",

            "skype": "",

            "qq": "",

            "yahoo": "",

            "gtalk": "",

            "wangwang": "",

            "mobile": "",

            "phone": "",

            "address": "",

            "zipcode": "",

            "join": "2018-06-19T16:00:00.000+00:00",

            "visits": 5225,

            "ip": "**************",

            "last": **********,

            "fails": 0,

            "locked": **null**,

            "deleted": "0",

            "empId": "***********"

        }

    ]

}

## 12.根据域账号查询任务

接口地址:/api/getTaskByAccount

入参:

{"account":"tiny.cui","start":"2020-01-01 00:00:00","end":"2023-01-01 23:59:59","isdo":1}

isdo:任务是否进行中 1：是 0:否 如果为1，不带时间查询所有数据

如果isdo=1 就查询所有时间内 正在进行的任务

出参：

{

    "traceId": "13bcd31b-d882-4ed1-a2a8-aba9a4ab7898",

    "rsCode": "********",

    "msg": "正常返回",

    "body": [

        {

            "id": 268306,

            "project": 1971,

            "projectname": "发文系统",

            "module": 18428,

            "modulename": "高鑫工作门户",

            "story": 21557,

            "storyname": "发文与规范系统",

            "storyVersion": 1,

            "fromBug": 0,

            "name": "规范检视操作页面",

            "type": "devel",

            "pri": 0,

            "estimate": 6,

            "consumed": 0,

            "left": 6,

            "deadline": "2021-08-11T16:00:00.000+00:00",

            "status": "doing",

            "statusCustom": 1,

            "mailto": "",

            "desc": "",

            "openedBy": "amy.shi",

            "openedByEmpid": **null**,

            "openedDate": "2021-07-30T10:18:06.000+00:00",

            "assignedTo": "tiny.cui",

            "assignedToEmpid": **null**,

            "assignedDate": "2021-07-30T10:18:06.000+00:00",

            "estStarted": "2021-08-08T16:00:00.000+00:00",

            "realStarted": **null**,

            "finishedBy": "",

            "finishedByEmpid": **null**,

            "finishedDate": **null**,

            "canceledBy": "",

            "canceledByEmpid": **null**,

            "canceledDate": **null**,

            "closedBy": "",

            "closedByEmpid": **null**,

            "closedDate": **null**,

            "closedReason": "",

            "lastEditedBy": "",

            "lastEditedByEmpid": **null**,

            "lastEditedDate": **null**,

            "deleted": "1",

            "pages": 0

        }

    ]

}

## 13.根据部门查询任务

接口地址:api/getTaskByDept

入参:

{"dept":343,"start":"2020-01-01 00:00:00","end":"2023-01-01 23:59:59","isdo":1}

isdo:任务是否进行中 1：是 0:否 如果为1，不带时间查询所有数据

如果isdo=1 就查询所有时间内 正在进行的任务


出参：

{

    "traceId": "13bcd31b-d882-4ed1-a2a8-aba9a4ab7898",

    "rsCode": "********",

    "msg": "正常返回",

    "body": [

        {

            "id": 268306,

            "project": 1971,

            "projectname": "发文系统",

            "module": 18428,

            "modulename": "高鑫工作门户",

            "story": 21557,

            "storyname": "发文与规范系统",

            "storyVersion": 1,

            "fromBug": 0,

            "name": "规范检视操作页面",

            "type": "devel",

            "pri": 0,

            "estimate": 6,

            "consumed": 0,

            "left": 6,

            "deadline": "2021-08-11T16:00:00.000+00:00",

            "status": "doing",

            "statusCustom": 1,

            "mailto": "",

            "desc": "",

            "openedBy": "amy.shi",

            "openedByEmpid": **null**,

            "openedDate": "2021-07-30T10:18:06.000+00:00",

            "assignedTo": "tiny.cui",

            "assignedToEmpid": **null**,

            "assignedDate": "2021-07-30T10:18:06.000+00:00",

            "estStarted": "2021-08-08T16:00:00.000+00:00",

            "realStarted": **null**,

            "finishedBy": "",

            "finishedByEmpid": **null**,

            "finishedDate": **null**,

            "canceledBy": "",

            "canceledByEmpid": **null**,

            "canceledDate": **null**,

            "closedBy": "",

            "closedByEmpid": **null**,

            "closedDate": **null**,

            "closedReason": "",

            "lastEditedBy": "",

            "lastEditedByEmpid": **null**,

            "lastEditedDate": **null**,

            "deleted": "1",

            "pages": 0

        }

    ]

}

## 14.根据部门查人员

接口地址:/api/getUserByDept

入参:

"344"


出参：

{

    "traceId": "99a0c199-3432-4eea-9d2e-6ef2a3dc0d0a",

    "rsCode": "********",

    "msg": "正常返回",

    "body": [

        {

            "id": 1953,

            "dept": 343,//部门代码

            "account": "tiny.cui",//域账号

            "password": "f832b71632f3e43a11c8a669a91b70c6",

            "role": "qd",//职位 *dev = '研发';
qa = '测试';
pm = '项目经理';
po = '产品经理';
td = '研发主管';
pd = '产品主管';
qd = '测试主管';
top = '高层管理';
others = '其他';*

            "realname": "崔巍巍",

            "nickname": "",

            "commiter": "",

            "avatar": "",

            "birthday": **null**,

            "gender": "m",

            "status": "work",

work= '在职';
quit= '离职';

            "email": "<EMAIL>",

            "skype": "",

            "qq": "",

            "yahoo": "",

            "gtalk": "",

            "wangwang": "",

            "mobile": "",

            "phone": "",

            "address": "",

            "zipcode": "",

            "join": "2021-03-23T16:00:00.000+00:00",

            "visits": 1159,

            "ip": "***********",

            "last": **********,

            "fails": 0,

            "locked": **null**,

            "deleted": "0",

            "empId": "***********"

        },

        {

            "id": 1517,

            "dept": 144,

            "account": "xiang.chen",

            "password": "57a479973a5e78917ef0b166346d4a7f",

            "role": "dev",

            "realname": "陈翔",

            "nickname": "",

            "commiter": "",

            "avatar": "",

            "birthday": **null**,

            "gender": "m",

            "status": "work",

            "email": "<EMAIL>",

            "skype": "",

            "qq": "",

            "yahoo": "",

            "gtalk": "",

            "wangwang": "",

            "mobile": "",

            "phone": "",

            "address": "",

            "zipcode": "",

            "join": "2018-06-19T16:00:00.000+00:00",

            "visits": 5225,

            "ip": "**************",

            "last": **********,

            "fails": 0,

            "locked": **null**,

            "deleted": "0",

            "empId": "***********"

        }

    ]

}

## 15.查询个人BUG

接口地址:/api/getPersonalBugs

入参:

{"account":"tiny.cui","status":"active","start":"2022-01-01 00:00:00","end":"2025-01-01 23:59:59"}*//Bug状态 active = '未关闭';resolved = '已解决';;released = '已发布';closed = '已关闭'*

*根据bug状态查数据,Status不传查询当前指派或者解决人是入参account的数据*

出参：

{

    "traceId": "5f8d4e86-3208-4d54-ae71-9d658741e7c7",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "bugdetail": [

            {

                "id": 183947,

                "assignedTo": "tiny.cui",

                "assignedToEmpid": **null**,

                "project": 1616,

                "projectname": "高鑫工作门户V1.1",

                "module": 0,

                "modulename": "未知模块",

                "product": 173,

                "productname": **null**,

                "assignedDate": "2024-01-26T00:53:56.000+00:00",

                "status": "resolved",

                "closedDate": **null**,

                "title": "1",

                "environment": "Beta",

                "openedDate": "2024-01-24T05:36:03.000+00:00",

                "severity": 1,

                "resolvedBy": "tony.zhang",

                "resolvedByEmpid": **null**,

                "openedBy": "tony.zhang",

                "openedByEmpid": **null**,

                "isxi": 0,

                "finishdate": **null**,

                "senddingding": 0,

                "toStory": 0,

                "activatedCount": 0,

                "confirmed": 1,

                "openedBuild": "11487",

                "resolvedBuild": "11608",

                "resolution": "fixed",

                "closedBy": "",

                "closedByEmpid": **null**,

                "lastEditedBy": "tony.zhang",

                "lastEditedByEmpid": **null**,

                "steps": "`<p>`[步骤]`</p>`\n`<p>`[结果]`</p>`\n`<p>`[期望]`</p>`"

            }

        ],

        "total": 1

    }

}同5

## 16.根据时间段来查询需求

接口地址:/api/getStorysByTime

入参:

{"status":"active","start":"2023-01-01 00:00:00","end":"2024-05-01 23:59:59"}

draft= '草稿',active= '激活';changed= '已变更';closed='已关闭'

出参：同8

{

    "traceId": "0ffd148c-d871-4366-8349-d93939641f3f",

    "rsCode": "********",

    "msg": "正常返回",

    "body": {

        "total": 9,

        "data": [

            {

                "id": 28549,

                "product": 109,

                "productname": "B2B平台",

                "module": 6128,

                "modulename": "订单/退单模块",

                "requirement": 256,

                "requirementname": "未知需求",

                "workitem": 11833,

                "workitemname": **null**,

                "plan": 0,

                "planname": "未知计划",

                "source": "",

                "fromBug": 0,

                "title": "abc",

                "keywords": "",

                "type": "",

                "pri": 0,

                "estimate": 0,

                "estimateDate": **null**,

                "wishFinishedDate": **null**,

                "status": "active",

                "stage": "projected",

                "mailto": "",

                "openedBy": "tony.zhang",

                "openedByEmpid": **null**,

                "openedDate": "2023-07-12T09:54:00.000+00:00",

                "assignedTo": "tony.zhang",

                "assignedToEmpid": **null**,

                "assignedDate": "2023-07-13T02:38:54.000+00:00",

                "lastEditedBy": "tony.zhang",

                "lastEditedByEmpid": **null**,

                "lastEditedDate": "2023-07-14T08:28:13.000+00:00",

                "reviewWay": "needNotReview",

                "reviewedBy": "",

                "reviewedDate": **null**,

                "closedBy": "",

                "closedByEmpid": **null**,

                "closedDate": **null**,

                "closedReason": "",

                "toBug": 0,

                "childStories": "",

                "linkStories": "",

                "duplicateStory": 0,

                "version": 17,

                "deleted": "0",

                "changeType": 1,

                "storyTypeComment": 0,

                "isdemand": 0,

                "sn": "",

                "spec": **null**

            }

]

}
