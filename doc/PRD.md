# 禅道 MCP 服务产品需求文档 (PRD)

## 1. 产品概述

### 1.1 产品定位

禅道 MCP 服务是一个基于 FastMCP 框架的中间件服务，通过 MCP 协议为 AI Agent 提供禅道项目管理系统的数据查询和访问能力。

### 1.2 产品目标

- 为 AI Agent 提供标准化的禅道数据访问接口
- 实现高性能、高可用的数据查询服务
- 支持灵活的配置管理和权限控制
- 提供完整的日志监控和错误处理机制

### 1.3 目标用户

- AI Agent 开发者
- 项目管理系统集成商
- 企业内部开发团队

## 2. 功能需求

### 2.1 设计原则

**分层设计原则**:

1. **直接接口层**: 提供与禅道 API 1:1 对应的直接查询接口，保持原始数据结构
2. **数据加工层**: 基于直接接口提供数据聚合、分析和统计功能
3. **业务封装层**: 提供面向特定业务场景的高级接口

**实现优先级**:

1. 优先实现直接接口层，确保所有禅道 API 都有对应的 MCP 工具
2. 基于直接接口实现数据加工功能
3. 根据用户需求逐步完善业务封装

**接口映射**:
详细的禅道 API 与 MCP 工具映射关系请参考：[API 映射表](./API_MAPPING.md)

### 2.2 直接接口层 (第一优先级)

#### 2.2.1 部门管理 (Department)

**功能描述**: 提供部门相关数据的直接查询接口

**直接接口**:

- `zentao_get_all_departments()`: 对应 `/apiData/getAllDept`
- `zentao_get_users_by_dept(dept_id)`: 对应 `/api/getUserByDept`

**数据字段** (保持原始结构):

- `id`: 部门 ID
- `name`: 部门名称
- `parent`: 父级部门 ID
- `path`: 部门路径
- `grade`: 层级

#### 2.2.2 项目管理 (Project)

**功能描述**: 提供项目相关数据的直接查询接口

**直接接口**:

- `zentao_get_all_projects()`: 对应 `/apiData/getAllProject`
- `zentao_get_project_tasks(project_id)`: 对应 `/api/getTaskByProject`
- `zentao_get_project_stories(project_id)`: 对应 `/api/getStoryByProjectid`
- `zentao_get_project_bugs(project_id)`: 对应 `/api/getBugByProject`

**数据字段** (保持原始结构):

- `id`: 项目 ID
- `name`: 项目名称
- `code`: 项目编号
- `begin`: 开始时间
- `end`: 结束时间
- `status`: 状态 (doing/wait/done/suspended)
- `parent`: 父级 ID
- `openedBy`: 创建人
- `closedDate`: 关闭日期
- `closedBy`: 关闭人

#### 2.2.3 需求管理 (Story)

**功能描述**: 提供需求相关数据的直接查询接口

**直接接口**:

- `zentao_get_story_effort(story_ids)`: 对应 `/api/getStory`
- `zentao_get_story_completed_effort(story_ids)`: 对应 `/api/getStoryEnd`
- `zentao_check_story_exists(story_ids)`: 对应 `/api/getStoryId`
- `zentao_get_story_detail(story_id)`: 对应 `/api/getStoryDetail`
- `zentao_get_stories_by_time(status, start_date, end_date)`: 对应 `/api/getStorysByTime`

**数据字段** (保持原始结构):

- 需求工时: `empid`, `openedBy`, `estimate`, `story`
- 已完成工时: `consumed`, `empid`, `finishedBy`, `taskid`, `story`
- 需求详情: 完整的需求对象结构

#### 2.2.4 任务管理 (Task)

**功能描述**: 提供任务相关数据的直接查询接口

**直接接口**:

- `zentao_get_task_detail(task_id)`: 对应 `/api/getTaskById`
- `zentao_get_tasks_by_account(account, start_date, end_date, is_doing)`: 对应 `/api/getTaskByAccount`
- `zentao_get_tasks_by_dept(dept_id, start_date, end_date, is_doing)`: 对应 `/api/getTaskByDept`

**数据字段** (保持原始结构):

- 完整的任务对象结构，包含所有原始字段

#### 2.2.5 Bug 管理 (Bug)

**功能描述**: 提供 Bug 相关数据的直接查询接口

**直接接口**:

- `zentao_get_bugs_by_time_range(start_date, end_date)`: 对应 `/apiData/getBugListByTimeRange`
- `zentao_get_bugs_by_time_and_dept(start_date, end_date, dept_id)`: 对应 `/apiData/getBugListByTimeRangeAndDeptId`
- `zentao_get_bug_detail(bug_id)`: 对应 `/api/getBugDetail`
- `zentao_get_personal_bugs(account, status, start_date, end_date)`: 对应 `/api/getPersonalBugs`

**数据字段** (保持原始结构):

- 完整的 Bug 对象结构，包含所有原始字段

#### 2.2.6 用户管理 (User)

**功能描述**: 提供用户相关数据的直接查询接口

**直接接口**:

- `zentao_get_users_by_account(accounts)`: 对应 `/api/getUserByAccount`

**数据字段** (保持原始结构):

- 完整的用户对象结构，包含所有原始字段

### 2.3 数据加工层 (第二优先级)

#### 2.1.1 部门管理 (Department)

**功能描述**: 提供部门相关数据的查询接口

**接口列表**:

- `get_all_departments()`: 获取所有部门列表
- `get_department_by_id(dept_id)`: 根据部门 ID 获取部门信息
- `get_sub_departments(parent_id)`: 根据父级部门获取子部门
- `get_departments_by_level(level)`: 根据层级获取部门列表

**数据字段**:

- `id`: 部门 ID
- `name`: 部门名称
- `parent_id`: 父级部门 ID
- `level`: 部门层级
- `manager`: 部门负责人
- `description`: 部门描述

#### 2.1.2 项目管理 (Project)

**功能描述**: 提供项目相关数据的查询接口

**接口列表**:

- `get_all_projects()`: 获取所有项目列表
- `get_project_by_id(project_id)`: 根据项目 ID 获取项目信息
- `get_projects_by_status(status)`: 根据状态获取项目列表
- `get_project_bugs(project_id)`: 获取项目的 Bug 列表
- `get_project_stories(project_id)`: 获取项目的需求列表
- `get_project_tasks(project_id)`: 获取项目的任务列表

**数据字段**:

- `id`: 项目 ID
- `name`: 项目名称
- `status`: 项目状态
- `begin_date`: 开始日期
- `end_date`: 结束日期
- `team`: 项目团队
- `description`: 项目描述

#### 2.1.3 需求管理 (Story)

**功能描述**: 提供需求相关数据的查询接口

**接口列表**:

- `get_story_effort(story_ids)`: 批量获取需求工时信息
- `get_completed_story_effort(story_ids)`: 批量获取已完成需求任务工时
- `check_story_exists(story_ids)`: 批量检查需求是否存在
- `get_story_by_id(story_id)`: 根据需求 ID 获取详情
- `get_stories_by_project(project_id)`: 根据项目获取需求列表
- `get_stories_by_time_range(status, start_date, end_date)`: 根据时间段查询需求

**数据字段**:

- `id`: 需求 ID
- `title`: 需求标题
- `status`: 需求状态 (draft:草稿 active:激活 changed:已变更 closed:已关闭)
- `priority`: 优先级
- `estimate`: 预估工时
- `stage`: 需求阶段 (wait:未开始 planned:已计划 projected:已立项 developing:研发中 developed:研发完毕 testing:测试中 tested:测试完毕 verified:已验收 released:已发布)
- `assigned_to`: 指派给
- `source`: 需求来源 (customer:客户 user:用户 po:产品经理 market:市场部 etc.)
- `change_type`: 需求变更类型 (0:新增需求 1:需求变更 2:需求优化)
- `spec`: 需求描述详情

#### 2.1.4 任务管理 (Task)

**功能描述**: 提供任务相关数据的查询接口

**接口列表**:

- `get_tasks_by_project(project_id)`: 根据项目获取任务列表
- `get_task_by_id(task_id)`: 根据任务 ID 获取详情
- `get_tasks_by_status(status)`: 根据状态筛选任务
- `get_tasks_by_type(task_type)`: 根据类型筛选任务
- `get_tasks_by_assignee(assignee)`: 根据指派人筛选任务
- `get_tasks_by_account(account, start_date, end_date, is_doing)`: 根据域账号查询任务
- `get_tasks_by_dept(dept_id, start_date, end_date, is_doing)`: 根据部门查询任务

**数据字段**:

- `id`: 任务 ID
- `name`: 任务名称
- `type`: 任务类型 (affair:处理 BUG design:设计 devel:开发 test:测试 study:研究 discuss:讨论 misc:其他 bug:编写测试用例 ui:UI 设计)
- `status`: 任务状态 (wait:未开始 doing:进行中 done:已完成 cancel:已取消 closed:已关闭 beta:已上 BETA preview:已上 Preview test_completed:测试完毕)
- `assigned_to`: 指派给
- `estimate`: 预估工时
- `consumed`: 已消耗工时
- `left`: 剩余工时
- `deadline`: 截止日期
- `story`: 关联需求 ID
- `pages`: UI 张数

#### 2.1.5 Bug 管理 (Bug)

**功能描述**: 提供 Bug 相关数据的查询接口

**接口列表**:

- `get_bugs_by_date_range(start_date, end_date)`: 根据时间段查询 Bug 列表
- `get_bugs_by_date_and_dept(start_date, end_date, dept_id)`: 根据时间段和部门查询 Bug
- `get_bug_by_id(bug_id)`: 根据 Bug ID 获取详情
- `get_bugs_by_project(project_id)`: 根据项目获取 Bug 列表
- `get_bugs_by_status(status)`: 根据状态筛选 Bug
- `get_bugs_by_severity(severity)`: 根据严重程度筛选 Bug
- `get_personal_bugs(account, status, start_date, end_date)`: 查询个人 Bug

**数据字段**:

- `id`: Bug ID
- `title`: Bug 标题
- `severity`: 严重程度 (0:冒烟 1:严重 2:一般 3:次要 4:低级)
- `priority`: 优先级
- `status`: Bug 状态 (active:未关闭 resolved:已解决 closed:已关闭 released:已发布)
- `assigned_to`: 指派给
- `opened_by`: 创建人
- `opened_date`: 创建日期
- `environment`: 环境 (Beta/Online/Test)
- `resolution`: 解决方案 (bydesign:设计如此 duplicate:重复 Bug external:外部原因 fixed:已解决 notrepro:无法重现 postponed:延期处理 tostory:转为需求)
- `to_story`: 转需求 ID
- `steps`: 重现步骤

#### 2.1.6 用户管理 (User)

**功能描述**: 提供用户相关数据的查询接口

**接口列表**:

- `get_user_by_account(accounts)`: 根据域账号批量查询用户信息
- `get_users_by_dept(dept_id)`: 根据部门查询用户列表

**数据字段**:

- `id`: 用户 ID
- `account`: 域账号
- `realname`: 真实姓名
- `dept`: 部门 ID
- `role`: 职位 (dev:研发 qa:测试 pm:项目经理 po:产品经理 td:研发主管 pd:产品主管 qd:测试主管 top:高层管理 others:其他)
- `status`: 状态 (work:在职 quit:离职)
- `email`: 邮箱
- `empid`: 员工 ID
- `join`: 入职时间

#### 2.3.1 数据聚合接口

**功能描述**: 基于直接接口提供数据聚合和统计功能

**聚合接口**:

- `analyze_story_workload(story_ids, project_id, include_completed)`: 分析需求工时统计
- `analyze_bugs_by_dept_and_time(start_date, end_date, dept_id)`: 按部门和时间段统计 Bug 分析
- `filter_bugs_by_criteria(bugs_data, filters)`: 根据多种条件过滤 Bug
- `project_summary_analysis(project_id)`: 项目整体统计，包含需求、任务、Bug 数量
- `story_task_relation_query(story_ids, project_id)`: 需求-任务关联查询
- `bug_to_story_tracking(bug_ids, project_id)`: Bug 转需求追踪
- `personnel_workload_analysis(user_accounts, project_id, dept_id)`: 人员工作量统计和分析

#### 2.3.2 数据验证接口

**功能描述**: 提供数据存在性验证和批量查询功能

**验证接口**:

- `batch_query_stories(story_ids)`: 批量查询需求信息
- `validate_story_existence(story_ids)`: 验证需求是否存在

#### 2.3.3 数据转换接口

**功能描述**: 提供数据格式转换和字段映射功能

**转换功能**:

- 中文字段名映射为英文
- 枚举值转换为可读格式
- 数据结构标准化
- 时间格式统一处理

### 2.4 业务封装层 (第三优先级)

#### 2.4.1 项目管理业务接口

- 项目进度分析
- 项目风险评估
- 项目资源分配分析

#### 2.4.2 团队效能分析接口

- 团队工作量分析
- 个人效能评估
- 部门协作分析

#### 2.4.3 质量管理接口

- Bug 趋势分析
- 需求变更分析
- 测试覆盖率分析

### 2.5 系统功能模块

#### 2.5.1 配置管理系统

**功能描述**: 提供灵活的配置管理能力

**配置项**:

- **环境配置**:
  - `MCP_TOKEN`: 禅道简单通行token
  - `request_timeout`: 请求超时时间
- **功能开关**:
  - `enable_cache`: 是否启用缓存
  - `enable_pagination`: 是否启用分页
  - `enable_auth`: 是否启用鉴权
  - `enable_retry`: 是否启用重试
- **缓存配置**:
  - `cache_ttl`: 缓存过期时间
  - `cache_max_size`: 缓存最大大小
  - `cache_strategy`: 缓存策略 (LRU/FIFO)
- **分页配置**:
  - `default_page_size`: 默认页大小
  - `max_page_size`: 最大页大小

#### 2.5.2 认证鉴权模块

**功能描述**: 提供安全的访问控制机制

**功能特性**:

- Token 验证: 验证 MCP 调用的 Token 有效性
- 禅道 API 认证: 管理与禅道系统的认证会话
- 配置化鉴权: 支持通过配置开启/关闭鉴权功能
- 会话管理: 自动管理 API 会话的创建和刷新

#### 2.5.3 缓存系统

**功能描述**: 提供高效的数据缓存机制

**缓存策略**:

- **静态数据缓存**: 部门、用户等相对不变的数据
- **TTL 过期**: 基于时间的缓存过期机制
- **LRU 淘汰**: 基于最近最少使用的缓存淘汰策略
- **缓存键管理**: 统一的缓存键命名和管理规范

#### 2.5.4 数据处理层

**功能描述**: 提供数据转换和处理能力

**处理功能**:

- **数据转换**: 将禅道 API 返回的数据转换为标准格式
- **字段映射**: 中文字段名映射为英文，便于 AI 理解
- **数据验证**: 验证返回数据的完整性和有效性
- **分页处理**: 处理大数据集的分页查询和结果合并

#### 2.5.5 错误处理和重试机制

**功能描述**: 提供健壮的错误处理能力

**错误处理**:

- **异常分类**: 网络异常、API 异常、数据异常的分类处理
- **重试策略**: 指数退避算法，支持配置最大重试次数
- **错误日志**: 结构化错误日志记录，便于问题排查
- **优雅降级**: 部分功能异常时保障核心服务可用

#### 2.5.6 日志和监控

**功能描述**: 提供完整的日志记录和监控能力

**日志功能**:

- **结构化日志**: JSON 格式日志，包含请求 ID、用户信息、执行时间
- **日志级别**: 支持 DEBUG、INFO、WARN、ERROR 等级别
- **日志轮转**: 支持按大小和时间的日志文件轮转

**监控功能**:

- **性能监控**: API 响应时间、缓存命中率、错误率统计
- **健康检查**: 提供服务健康状态检查接口
- **指标收集**: 支持 Prometheus 格式的指标输出

#### 2.5.7 MCP 协议增强

**功能描述**: 提供完整的 MCP 协议支持

**协议功能**:

- **工具描述**: 每个 MCP 工具的详细描述和参数说明
- **参数验证**: 输入参数的类型和格式验证
- **结果格式化**: 统一的 JSON 响应格式，包含数据字段描述
- **错误响应**: 标准化的错误响应格式

## 3. 非功能需求

### 3.1 性能需求

- **响应时间**: API 响应时间 < 2 秒
- **并发处理**: 支持 100 个并发请求
- **缓存命中率**: 静态数据缓存命中率 > 80%
- **可用性**: 服务可用性 > 99.5%

### 3.2 安全需求

- **数据传输**: 支持 HTTPS 加密传输
- **访问控制**: 基于 Token 的访问控制
- **日志安全**: 敏感信息不记录在日志中
- **配置安全**: 敏感配置信息加密存储

### 3.3 可维护性需求

- **代码质量**: 代码覆盖率 > 80%
- **文档完整**: 提供完整的 API 文档和部署文档
- **配置管理**: 支持环境变量和配置文件管理
- **版本管理**: 支持 API 版本管理和向后兼容

### 3.4 扩展性需求

- **模块化设计**: 支持功能模块的独立开发和部署
- **插件机制**: 支持自定义数据处理插件
- **多实例部署**: 支持水平扩展和负载均衡
- **数据源扩展**: 支持接入其他项目管理系统

## 4. 技术约束

### 4.1 技术栈

- **后端框架**: FastAPI + FastMCP
- **HTTP 客户端**: httpx (异步) + requests (同步兼容)
- **数据验证**: Pydantic
- **缓存**: 内存缓存 (可扩展 Redis)
- **日志**: Python logging + 结构化日志
- **测试**: pytest + pytest-asyncio

### 4.2 部署环境

- **Python 版本**: >= 3.10
- **操作系统**: Linux/macOS/Windows
- **容器化**: 支持 Docker 部署
- **依赖管理**: uv + pyproject.toml

## 5. 验收标准

### 5.1 功能验收

**直接接口层验收**:

- [ ] 部门管理直接接口 (2 个接口)
- [ ] 项目管理直接接口 (4 个接口)
- [ ] 用户管理直接接口 (1 个接口)
- [ ] 需求管理直接接口 (5 个接口)
- [ ] 任务管理直接接口 (3 个接口)
- [ ] Bug 管理直接接口 (4 个接口)

**数据加工层验收**:

- [ ] 数据聚合接口 (7 个接口)
- [ ] 数据验证接口 (2 个接口)
- [ ] 数据转换功能完整

**系统功能验收**:

- [ ] 配置管理功能完整
- [ ] 认证鉴权机制有效
- [ ] 缓存系统运行正常
- [ ] 错误处理和重试机制有效
- [ ] 日志和监控功能完整

### 5.2 性能验收

- [ ] API 响应时间满足要求
- [ ] 并发处理能力达标
- [ ] 缓存命中率达标
- [ ] 服务可用性达标

### 5.3 质量验收

- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试通过
- [ ] 代码质量检查通过
- [ ] 文档完整性检查通过

## 6. 项目里程碑

### 6.1 第一阶段 (基础框架)

- [ ] 项目结构搭建
- [ ] 配置管理系统
- [ ] 基础 MCP 框架集成
- [ ] 禅道 API 客户端

### 6.2 第二阶段 (直接接口层)

- [ ] 部门管理直接接口实现 (2 个)
- [ ] 项目管理直接接口实现 (4 个)
- [ ] 用户管理直接接口实现 (1 个)
- [ ] 需求管理直接接口实现 (5 个)
- [ ] 任务管理直接接口实现 (3 个)
- [ ] Bug 管理直接接口实现 (4 个)
- [ ] 数据模型定义
- [ ] 认证鉴权模块
- [ ] 缓存系统

### 6.3 第三阶段 (数据加工层)

- [ ] 数据聚合接口实现 (7 个)
- [ ] 数据验证接口实现 (2 个)
- [ ] 数据转换功能实现
- [ ] 批量操作支持
- [ ] 时间范围查询优化

### 6.4 第四阶段 (业务封装层)

- [ ] 项目管理业务接口
- [ ] 团队效能分析接口
- [ ] 质量管理接口
- [ ] 高级统计分析功能

### 6.5 第五阶段 (增强功能)

- [ ] 错误处理和重试
- [ ] 日志和监控
- [ ] 性能优化
- [ ] 扩展功能

### 6.6 第六阶段 (测试和文档)

- [ ] 单元测试和集成测试
- [ ] 性能测试
- [ ] 文档编写
- [ ] 部署和发布

## 7. 风险评估

### 7.1 技术风险

- **禅道 API 变更**: 禅道系统 API 接口变更可能影响服务稳定性
- **性能瓶颈**: 大量并发请求可能导致性能问题
- **缓存一致性**: 缓存数据与实际数据不一致的风险

### 7.2 业务风险

- **需求变更**: 业务需求变更可能影响开发进度
- **集成复杂度**: 与不同版本禅道系统集成的复杂度
- **用户接受度**: AI Agent 开发者对接口设计的接受度

### 7.3 风险应对

- **API 监控**: 实时监控禅道 API 变更，及时适配
- **性能测试**: 定期进行性能测试，优化瓶颈点
- **版本管理**: 采用语义化版本管理，保证向后兼容
- **用户反馈**: 建立用户反馈机制，持续改进产品
